{"__meta": {"id": "Xce46f019d5daef697cfa83695991337a", "datetime": "2025-06-27 16:09:28", "utime": **********.250287, "method": "GET", "uri": "/manufacturing/order/create", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 4, "messages": [{"message": "[16:09:28] LOG.warning: str_contains(): Passing null to parameter #1 ($haystack) of type string is deprecated in D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 268", "message_html": null, "is_string": false, "label": "warning", "time": **********.04924, "xdebug_link": null, "collector": "log"}, {"message": "[16:09:28] LOG.warning: str_contains(): Passing null to parameter #1 ($haystack) of type string is deprecated in D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 268", "message_html": null, "is_string": false, "label": "warning", "time": **********.049384, "xdebug_link": null, "collector": "log"}, {"message": "[16:09:28] LOG.warning: str_contains(): Passing null to parameter #1 ($haystack) of type string is deprecated in D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 268", "message_html": null, "is_string": false, "label": "warning", "time": **********.049491, "xdebug_link": null, "collector": "log"}, {"message": "[16:09:28] LOG.warning: str_contains(): Passing null to parameter #1 ($haystack) of type string is deprecated in D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 268", "message_html": null, "is_string": false, "label": "warning", "time": **********.049573, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1751036967.692006, "end": **********.250309, "duration": 0.5583028793334961, "duration_str": "558ms", "measures": [{"label": "Booting", "start": 1751036967.692006, "relative_start": 0, "end": 1751036967.97487, "relative_end": 1751036967.97487, "duration": 0.2828638553619385, "duration_str": "283ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1751036967.974892, "relative_start": 0.2828857898712158, "end": **********.250311, "relative_end": 1.9073486328125e-06, "duration": 0.2754189968109131, "duration_str": "275ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 27688400, "peak_usage_str": "26MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 98, "templates": [{"name": "1x manufacturing_orders.create", "param_count": null, "params": [], "start": **********.064452, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/manufacturing_orders/create.blade.phpmanufacturing_orders.create", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fmanufacturing_orders%2Fcreate.blade.php&line=1", "ajax": false, "filename": "create.blade.php", "line": "?"}, "render_count": 1, "name_original": "manufacturing_orders.create"}, {"name": "1x components.breadcrumb", "param_count": null, "params": [], "start": **********.066973, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/components/breadcrumb.blade.phpcomponents.breadcrumb", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fcomponents%2Fbreadcrumb.blade.php&line=1", "ajax": false, "filename": "breadcrumb.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.breadcrumb"}, {"name": "43x components.label", "param_count": null, "params": [], "start": **********.070532, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/components/label.blade.phpcomponents.label", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fcomponents%2Flabel.blade.php&line=1", "ajax": false, "filename": "label.blade.php", "line": "?"}, "render_count": 43, "name_original": "components.label"}, {"name": "1x manufacturing_orders._store_basic_info", "param_count": null, "params": [], "start": **********.071592, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/manufacturing_orders/_store_basic_info.blade.phpmanufacturing_orders._store_basic_info", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fmanufacturing_orders%2F_store_basic_info.blade.php&line=1", "ajax": false, "filename": "_store_basic_info.blade.php", "line": "?"}, "render_count": 1, "name_original": "manufacturing_orders._store_basic_info"}, {"name": "33x components.input", "param_count": null, "params": [], "start": **********.073437, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/components/input.blade.phpcomponents.input", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fcomponents%2Finput.blade.php&line=1", "ajax": false, "filename": "input.blade.php", "line": "?"}, "render_count": 33, "name_original": "components.input"}, {"name": "1x manufacturing_orders._store_dimensions", "param_count": null, "params": [], "start": **********.078257, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/manufacturing_orders/_store_dimensions.blade.phpmanufacturing_orders._store_dimensions", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fmanufacturing_orders%2F_store_dimensions.blade.php&line=1", "ajax": false, "filename": "_store_dimensions.blade.php", "line": "?"}, "render_count": 1, "name_original": "manufacturing_orders._store_dimensions"}, {"name": "1x manufacturing_orders._store_pricing", "param_count": null, "params": [], "start": **********.080659, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/manufacturing_orders/_store_pricing.blade.phpmanufacturing_orders._store_pricing", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fmanufacturing_orders%2F_store_pricing.blade.php&line=1", "ajax": false, "filename": "_store_pricing.blade.php", "line": "?"}, "render_count": 1, "name_original": "manufacturing_orders._store_pricing"}, {"name": "2x components.textarea", "param_count": null, "params": [], "start": **********.090526, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/components/textarea.blade.phpcomponents.textarea", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fcomponents%2Ftextarea.blade.php&line=1", "ajax": false, "filename": "textarea.blade.php", "line": "?"}, "render_count": 2, "name_original": "components.textarea"}, {"name": "2x components.anchor-tag", "param_count": null, "params": [], "start": **********.091312, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/components/anchor-tag.blade.phpcomponents.anchor-tag", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fcomponents%2Fanchor-tag.blade.php&line=1", "ajax": false, "filename": "anchor-tag.blade.php", "line": "?"}, "render_count": 2, "name_original": "components.anchor-tag"}, {"name": "1x manufacturing_orders._rideaux_basic_info", "param_count": null, "params": [], "start": **********.091894, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/manufacturing_orders/_rideaux_basic_info.blade.phpmanufacturing_orders._rideaux_basic_info", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fmanufacturing_orders%2F_rideaux_basic_info.blade.php&line=1", "ajax": false, "filename": "_rideaux_basic_info.blade.php", "line": "?"}, "render_count": 1, "name_original": "manufacturing_orders._rideaux_basic_info"}, {"name": "1x manufacturing_orders._rideaux_dimensions", "param_count": null, "params": [], "start": **********.099099, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/manufacturing_orders/_rideaux_dimensions.blade.phpmanufacturing_orders._rideaux_dimensions", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fmanufacturing_orders%2F_rideaux_dimensions.blade.php&line=1", "ajax": false, "filename": "_rideaux_dimensions.blade.php", "line": "?"}, "render_count": 1, "name_original": "manufacturing_orders._rideaux_dimensions"}, {"name": "1x manufacturing_orders._rideaux_details", "param_count": null, "params": [], "start": **********.103228, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/manufacturing_orders/_rideaux_details.blade.phpmanufacturing_orders._rideaux_details", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fmanufacturing_orders%2F_rideaux_details.blade.php&line=1", "ajax": false, "filename": "_rideaux_details.blade.php", "line": "?"}, "render_count": 1, "name_original": "manufacturing_orders._rideaux_details"}, {"name": "1x manufacturing_orders._rideaux_pricing", "param_count": null, "params": [], "start": **********.107269, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/manufacturing_orders/_rideaux_pricing.blade.phpmanufacturing_orders._rideaux_pricing", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fmanufacturing_orders%2F_rideaux_pricing.blade.php&line=1", "ajax": false, "filename": "_rideaux_pricing.blade.php", "line": "?"}, "render_count": 1, "name_original": "manufacturing_orders._rideaux_pricing"}, {"name": "1x layouts.app", "param_count": null, "params": [], "start": **********.129008, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/layouts/app.blade.phplayouts.app", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.app"}, {"name": "1x layouts.head", "param_count": null, "params": [], "start": **********.129803, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/layouts/head.blade.phplayouts.head", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Flayouts%2Fhead.blade.php&line=1", "ajax": false, "filename": "head.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.head"}, {"name": "1x layouts.page-loader", "param_count": null, "params": [], "start": **********.130889, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/layouts/page-loader.blade.phplayouts.page-loader", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Flayouts%2Fpage-loader.blade.php&line=1", "ajax": false, "filename": "page-loader.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.page-loader"}, {"name": "1x layouts.navigation", "param_count": null, "params": [], "start": **********.131506, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/layouts/navigation.blade.phplayouts.navigation", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Flayouts%2Fnavigation.blade.php&line=1", "ajax": false, "filename": "navigation.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.navigation"}, {"name": "1x layouts.header", "param_count": null, "params": [], "start": **********.227829, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/layouts/header.blade.phplayouts.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Flayouts%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.header"}, {"name": "1x components.header-shortcut-menu", "param_count": null, "params": [], "start": **********.230557, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/components/header-shortcut-menu.blade.phpcomponents.header-shortcut-menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fcomponents%2Fheader-shortcut-menu.blade.php&line=1", "ajax": false, "filename": "header-shortcut-menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.header-shortcut-menu"}, {"name": "1x components.flag-toggle", "param_count": null, "params": [], "start": **********.240128, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/components/flag-toggle.blade.phpcomponents.flag-toggle", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fcomponents%2Fflag-toggle.blade.php&line=1", "ajax": false, "filename": "flag-toggle.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.flag-toggle"}, {"name": "1x layouts.footer", "param_count": null, "params": [], "start": **********.243761, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/layouts/footer.blade.phplayouts.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Flayouts%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.footer"}, {"name": "1x layouts.script", "param_count": null, "params": [], "start": **********.244277, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/layouts/script.blade.phplayouts.script", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Flayouts%2Fscript.blade.php&line=1", "ajax": false, "filename": "script.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.script"}]}, "route": {"uri": "GET manufacturing/order/create", "middleware": "web, auth", "as": "manufacturing.order.create", "controller": "App\\Http\\Controllers\\ManufacturingOrderController@create", "namespace": null, "prefix": "/manufacturing", "where": [], "file": "<a href=\"phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FHttp%2FControllers%2FManufacturingOrderController.php&line=22\" onclick=\"\">app/Http/Controllers/ManufacturingOrderController.php:22-34</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.028990000000000002, "accumulated_duration_str": "28.99ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 17, "namespace": "middleware", "name": "auth", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}], "start": **********.009489, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:168", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "deltapos", "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.0165598, "duration": 0.024460000000000003, "duration_str": "24.46ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "deltapos", "start_percent": 0, "width_percent": 84.374}, {"sql": "select * from `manufacturing_orders` order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ManufacturingOrderController.php", "file": "D:\\www\\delta\\app\\Http\\Controllers\\ManufacturingOrderController.php", "line": 25}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.050333, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "ManufacturingOrderController.php:25", "source": "app/Http/Controllers/ManufacturingOrderController.php:25", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FHttp%2FControllers%2FManufacturingOrderController.php&line=25", "ajax": false, "filename": "ManufacturingOrderController.php", "line": "25"}, "connection": "deltapos", "start_percent": 84.374, "width_percent": 3.622}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 1 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["1", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\www\\delta\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\www\\delta\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 193}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\www\\delta\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 244}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\www\\delta\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 136}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.147964, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:305", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:305", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=305", "ajax": false, "filename": "HasPermissions.php", "line": "305"}, "connection": "deltapos", "start_percent": 87.996, "width_percent": 4.208}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\www\\delta\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\www\\delta\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 291}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\www\\delta\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 193}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\www\\delta\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 244}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\www\\delta\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 136}], "start": **********.1503632, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:188", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:188", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=188", "ajax": false, "filename": "HasRoles.php", "line": "188"}, "connection": "deltapos", "start_percent": 92.204, "width_percent": 4.001}, {"sql": "select `id`, `name`, `code`, `emoji` from `languages` where `status` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/View/Components/FlagToggle.php", "file": "D:\\www\\delta\\app\\View\\Components\\FlagToggle.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 961}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.238533, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "FlagToggle.php:43", "source": "app/View/Components/FlagToggle.php:43", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FView%2FComponents%2FFlagToggle.php&line=43", "ajax": false, "filename": "FlagToggle.php", "line": "43"}, "connection": "deltapos", "start_percent": 96.206, "width_percent": 1.725}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "view", "name": "layouts.header", "file": "D:\\www\\delta\\resources\\views/layouts/header.blade.php", "line": 81}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.241683, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "layouts.header:81", "source": "view::layouts.header:81", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Flayouts%2Fheader.blade.php&line=81", "ajax": false, "filename": "header.blade.php", "line": "81"}, "connection": "deltapos", "start_percent": 97.93, "width_percent": 2.07}]}, "models": {"data": {"App\\Models\\Language": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 7, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 110, "messages": [{"message": "[ability => customer.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1101245235 data-indent-pad=\"  \"><span class=sf-dump-note>customer.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">customer.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1101245235\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.15735, "xdebug_link": null}, {"message": "[ability => customer.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-320422314 data-indent-pad=\"  \"><span class=sf-dump-note>customer.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">customer.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-320422314\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.158509, "xdebug_link": null}, {"message": "[ability => supplier.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-295127436 data-indent-pad=\"  \"><span class=sf-dump-note>supplier.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">supplier.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-295127436\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.159432, "xdebug_link": null}, {"message": "[ability => sale.invoice.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2140813166 data-indent-pad=\"  \"><span class=sf-dump-note>sale.invoice.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">sale.invoice.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2140813166\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.160344, "xdebug_link": null}, {"message": "[ability => sale.invoice.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-171502371 data-indent-pad=\"  \"><span class=sf-dump-note>sale.invoice.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">sale.invoice.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-171502371\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.161211, "xdebug_link": null}, {"message": "[ability => sale.invoice.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-986597793 data-indent-pad=\"  \"><span class=sf-dump-note>sale.invoice.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">sale.invoice.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-986597793\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.161824, "xdebug_link": null}, {"message": "[ability => sale.quotation.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-187021606 data-indent-pad=\"  \"><span class=sf-dump-note>sale.quotation.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">sale.quotation.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-187021606\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.162499, "xdebug_link": null}, {"message": "[ability => sale.invoice.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1298898485 data-indent-pad=\"  \"><span class=sf-dump-note>sale.invoice.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">sale.invoice.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1298898485\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.163033, "xdebug_link": null}, {"message": "[ability => sale.order.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-322990778 data-indent-pad=\"  \"><span class=sf-dump-note>sale.order.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">sale.order.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-322990778\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.164169, "xdebug_link": null}, {"message": "[ability => sale.return.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-188156870 data-indent-pad=\"  \"><span class=sf-dump-note>sale.return.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">sale.return.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-188156870\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.165001, "xdebug_link": null}, {"message": "[ability => purchase.bill.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1091231786 data-indent-pad=\"  \"><span class=sf-dump-note>purchase.bill.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">purchase.bill.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1091231786\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.165611, "xdebug_link": null}, {"message": "[ability => purchase.bill.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-645411465 data-indent-pad=\"  \"><span class=sf-dump-note>purchase.bill.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">purchase.bill.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-645411465\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.16646, "xdebug_link": null}, {"message": "[ability => purchase.bill.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1247698066 data-indent-pad=\"  \"><span class=sf-dump-note>purchase.bill.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">purchase.bill.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1247698066\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.166963, "xdebug_link": null}, {"message": "[ability => purchase.order.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1187890572 data-indent-pad=\"  \"><span class=sf-dump-note>purchase.order.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">purchase.order.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1187890572\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.167483, "xdebug_link": null}, {"message": "[ability => purchase.return.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1746525170 data-indent-pad=\"  \"><span class=sf-dump-note>purchase.return.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">purchase.return.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1746525170\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.167995, "xdebug_link": null}, {"message": "[ability => item.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-789533814 data-indent-pad=\"  \"><span class=sf-dump-note>item.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">item.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-789533814\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.168488, "xdebug_link": null}, {"message": "[ability => item.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1326061394 data-indent-pad=\"  \"><span class=sf-dump-note>item.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">item.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1326061394\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.170669, "xdebug_link": null}, {"message": "[ability => item.category.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-380785837 data-indent-pad=\"  \"><span class=sf-dump-note>item.category.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">item.category.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-380785837\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.171511, "xdebug_link": null}, {"message": "[ability => item.brand.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-867840814 data-indent-pad=\"  \"><span class=sf-dump-note>item.brand.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">item.brand.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-867840814\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.172487, "xdebug_link": null}, {"message": "[ability => expense.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-891098522 data-indent-pad=\"  \"><span class=sf-dump-note>expense.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">expense.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-891098522\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.173084, "xdebug_link": null}, {"message": "[ability => expense.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1301288854 data-indent-pad=\"  \"><span class=sf-dump-note>expense.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">expense.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1301288854\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.173872, "xdebug_link": null}, {"message": "[ability => expense.category.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-745768421 data-indent-pad=\"  \"><span class=sf-dump-note>expense.category.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">expense.category.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-745768421\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.17434, "xdebug_link": null}, {"message": "[\n  ability => expense.subcategory.view,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-493015164 data-indent-pad=\"  \"><span class=sf-dump-note>expense.subcategory.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">expense.subcategory.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-493015164\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.175028, "xdebug_link": null}, {"message": "[ability => transaction.cash.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1622384398 data-indent-pad=\"  \"><span class=sf-dump-note>transaction.cash.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">transaction.cash.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1622384398\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.175802, "xdebug_link": null}, {"message": "[ability => transaction.cash.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1832030478 data-indent-pad=\"  \"><span class=sf-dump-note>transaction.cash.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">transaction.cash.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1832030478\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.176514, "xdebug_link": null}, {"message": "[\n  ability => transaction.cheque.view,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>transaction.cheque.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">transaction.cheque.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.177145, "xdebug_link": null}, {"message": "[ability => transaction.bank.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>transaction.bank.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">transaction.bank.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.17773, "xdebug_link": null}, {"message": "[ability => warehouse.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>warehouse.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">warehouse.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.178158, "xdebug_link": null}, {"message": "[ability => warehouse.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-878610830 data-indent-pad=\"  \"><span class=sf-dump-note>warehouse.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">warehouse.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-878610830\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.178828, "xdebug_link": null}, {"message": "[ability => stock_transfer.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-379768474 data-indent-pad=\"  \"><span class=sf-dump-note>stock_transfer.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">stock_transfer.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-379768474\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.179487, "xdebug_link": null}, {"message": "[ability => import.item, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-817181813 data-indent-pad=\"  \"><span class=sf-dump-note>import.item</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">import.item</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-817181813\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.180295, "xdebug_link": null}, {"message": "[ability => import.item, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-995234010 data-indent-pad=\"  \"><span class=sf-dump-note>import.item</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">import.item</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-995234010\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.181065, "xdebug_link": null}, {"message": "[ability => import.party, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-889307192 data-indent-pad=\"  \"><span class=sf-dump-note>import.party</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">import.party</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-889307192\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.18192, "xdebug_link": null}, {"message": "[ability => generate.barcode, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>generate.barcode</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">generate.barcode</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.182731, "xdebug_link": null}, {"message": "[ability => account.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>account.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">account.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.183964, "xdebug_link": null}, {"message": "[ability => account.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>account.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">account.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.184872, "xdebug_link": null}, {"message": "[ability => account.group.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>account.group.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">account.group.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.1863, "xdebug_link": null}, {"message": "[ability => profile.edit, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>profile.edit</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">profile.edit</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.186875, "xdebug_link": null}, {"message": "[ability => profile.edit, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1415939436 data-indent-pad=\"  \"><span class=sf-dump-note>profile.edit</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">profile.edit</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1415939436\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.187806, "xdebug_link": null}, {"message": "[ability => user.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-317653467 data-indent-pad=\"  \"><span class=sf-dump-note>user.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">user.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-317653467\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.188401, "xdebug_link": null}, {"message": "[ability => role.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1610133634 data-indent-pad=\"  \"><span class=sf-dump-note>role.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">role.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1610133634\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.189009, "xdebug_link": null}, {"message": "[ability => permission.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-295541508 data-indent-pad=\"  \"><span class=sf-dump-note>permission.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">permission.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-295541508\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.190127, "xdebug_link": null}, {"message": "[ability => sms.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2105616572 data-indent-pad=\"  \"><span class=sf-dump-note>sms.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">sms.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2105616572\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.190587, "xdebug_link": null}, {"message": "[ability => sms.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-698082490 data-indent-pad=\"  \"><span class=sf-dump-note>sms.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">sms.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-698082490\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.191543, "xdebug_link": null}, {"message": "[ability => sms.template.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-347864215 data-indent-pad=\"  \"><span class=sf-dump-note>sms.template.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">sms.template.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-347864215\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.192205, "xdebug_link": null}, {"message": "[ability => email.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1520183417 data-indent-pad=\"  \"><span class=sf-dump-note>email.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">email.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1520183417\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.19299, "xdebug_link": null}, {"message": "[ability => email.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-625082229 data-indent-pad=\"  \"><span class=sf-dump-note>email.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">email.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-625082229\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.193623, "xdebug_link": null}, {"message": "[ability => email.template.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-124585060 data-indent-pad=\"  \"><span class=sf-dump-note>email.template.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">email.template.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-124585060\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.194299, "xdebug_link": null}, {"message": "[ability => report.*, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-746066311 data-indent-pad=\"  \"><span class=sf-dump-note>report.*</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">report.*</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-746066311\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.195371, "xdebug_link": null}, {"message": "[ability => report.profit_and_loss, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-561101054 data-indent-pad=\"  \"><span class=sf-dump-note>report.profit_and_loss</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">report.profit_and_loss</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-561101054\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.195966, "xdebug_link": null}, {"message": "[\n  ability => report.item.transaction.batch,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1636105429 data-indent-pad=\"  \"><span class=sf-dump-note>report.item.transaction.batch</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"29 characters\">report.item.transaction.batch</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1636105429\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.196629, "xdebug_link": null}, {"message": "[\n  ability => report.item.transaction.batch,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-916994853 data-indent-pad=\"  \"><span class=sf-dump-note>report.item.transaction.batch</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"29 characters\">report.item.transaction.batch</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-916994853\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.197192, "xdebug_link": null}, {"message": "[\n  ability => report.item.transaction.serial,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1007413878 data-indent-pad=\"  \"><span class=sf-dump-note>report.item.transaction.serial</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"30 characters\">report.item.transaction.serial</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1007413878\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.197859, "xdebug_link": null}, {"message": "[\n  ability => report.item.transaction.general,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-863623296 data-indent-pad=\"  \"><span class=sf-dump-note>report.item.transaction.general</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"31 characters\">report.item.transaction.general</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-863623296\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.198524, "xdebug_link": null}, {"message": "[ability => report.purchase, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-831885583 data-indent-pad=\"  \"><span class=sf-dump-note>report.purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">report.purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-831885583\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.199271, "xdebug_link": null}, {"message": "[ability => report.purchase, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1087414577 data-indent-pad=\"  \"><span class=sf-dump-note>report.purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">report.purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1087414577\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.199916, "xdebug_link": null}, {"message": "[ability => report.purchase.item, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1958102476 data-indent-pad=\"  \"><span class=sf-dump-note>report.purchase.item</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">report.purchase.item</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1958102476\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.200581, "xdebug_link": null}, {"message": "[\n  ability => report.purchase.payment,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1324601045 data-indent-pad=\"  \"><span class=sf-dump-note>report.purchase.payment</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">report.purchase.payment</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1324601045\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.201245, "xdebug_link": null}, {"message": "[ability => report.sale, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-144139603 data-indent-pad=\"  \"><span class=sf-dump-note>report.sale</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">report.sale</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-144139603\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.202109, "xdebug_link": null}, {"message": "[ability => report.sale, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1673056040 data-indent-pad=\"  \"><span class=sf-dump-note>report.sale</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">report.sale</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1673056040\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.203368, "xdebug_link": null}, {"message": "[ability => report.sale.item, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-97051359 data-indent-pad=\"  \"><span class=sf-dump-note>report.sale.item</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">report.sale.item</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-97051359\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.204244, "xdebug_link": null}, {"message": "[ability => report.sale.payment, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-266685831 data-indent-pad=\"  \"><span class=sf-dump-note>report.sale.payment</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">report.sale.payment</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-266685831\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.205145, "xdebug_link": null}, {"message": "[\n  ability => report.customer.due.payment,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1256312570 data-indent-pad=\"  \"><span class=sf-dump-note>report.customer.due.payment</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"27 characters\">report.customer.due.payment</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1256312570\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.20627, "xdebug_link": null}, {"message": "[\n  ability => report.customer.due.payment,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1378164047 data-indent-pad=\"  \"><span class=sf-dump-note>report.customer.due.payment</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"27 characters\">report.customer.due.payment</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1378164047\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.207085, "xdebug_link": null}, {"message": "[\n  ability => report.supplier.due.payment,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1725227452 data-indent-pad=\"  \"><span class=sf-dump-note>report.supplier.due.payment</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"27 characters\">report.supplier.due.payment</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1725227452\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.207763, "xdebug_link": null}, {"message": "[ability => report.expense, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-761871770 data-indent-pad=\"  \"><span class=sf-dump-note>report.expense</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">report.expense</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-761871770\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.20819, "xdebug_link": null}, {"message": "[ability => report.expense, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1464580878 data-indent-pad=\"  \"><span class=sf-dump-note>report.expense</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">report.expense</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1464580878\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.208559, "xdebug_link": null}, {"message": "[ability => report.expense.item, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1972007775 data-indent-pad=\"  \"><span class=sf-dump-note>report.expense.item</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">report.expense.item</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1972007775\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.208964, "xdebug_link": null}, {"message": "[ability => report.expense.payment, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-863689788 data-indent-pad=\"  \"><span class=sf-dump-note>report.expense.payment</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">report.expense.payment</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-863689788\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.209387, "xdebug_link": null}, {"message": "[\n  ability => report.transaction.cashflow,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-681850352 data-indent-pad=\"  \"><span class=sf-dump-note>report.transaction.cashflow</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"27 characters\">report.transaction.cashflow</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-681850352\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.210188, "xdebug_link": null}, {"message": "[\n  ability => report.transaction.cashflow,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>report.transaction.cashflow</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"27 characters\">report.transaction.cashflow</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.211278, "xdebug_link": null}, {"message": "[\n  ability => report.transaction.bank-statement,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>report.transaction.bank-statement</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"33 characters\">report.transaction.bank-statement</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.212454, "xdebug_link": null}, {"message": "[ability => report.gst*, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>report.gst*</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">report.gst*</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.213575, "xdebug_link": null}, {"message": "[ability => report.gstr1, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-724412759 data-indent-pad=\"  \"><span class=sf-dump-note>report.gstr1</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">report.gstr1</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-724412759\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.214604, "xdebug_link": null}, {"message": "[ability => report.gstr2, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-723534793 data-indent-pad=\"  \"><span class=sf-dump-note>report.gstr2</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">report.gstr2</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-723534793\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.215443, "xdebug_link": null}, {"message": "[ability => report.stock_transfer, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1233384364 data-indent-pad=\"  \"><span class=sf-dump-note>report.stock_transfer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">report.stock_transfer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1233384364\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.215972, "xdebug_link": null}, {"message": "[ability => report.stock_transfer, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-415014125 data-indent-pad=\"  \"><span class=sf-dump-note>report.stock_transfer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">report.stock_transfer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-415014125\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.216404, "xdebug_link": null}, {"message": "[\n  ability => report.stock_transfer.item,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-74619961 data-indent-pad=\"  \"><span class=sf-dump-note>report.stock_transfer.item</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"26 characters\">report.stock_transfer.item</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-74619961\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.216847, "xdebug_link": null}, {"message": "[ability => report.stock_report.*, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1963014291 data-indent-pad=\"  \"><span class=sf-dump-note>report.stock_report.*</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">report.stock_report.*</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1963014291\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.217539, "xdebug_link": null}, {"message": "[\n  ability => report.stock_report.item.batch,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-343998176 data-indent-pad=\"  \"><span class=sf-dump-note>report.stock_report.item.batch</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"30 characters\">report.stock_report.item.batch</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-343998176\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.218093, "xdebug_link": null}, {"message": "[\n  ability => report.stock_report.item.serial,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1893229447 data-indent-pad=\"  \"><span class=sf-dump-note>report.stock_report.item.serial</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"31 characters\">report.stock_report.item.serial</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1893229447\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.219277, "xdebug_link": null}, {"message": "[\n  ability => report.stock_report.item.general,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1846013102 data-indent-pad=\"  \"><span class=sf-dump-note>report.stock_report.item.general</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"32 characters\">report.stock_report.item.general</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1846013102\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.221032, "xdebug_link": null}, {"message": "[ability => report.expired.item, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-48474299 data-indent-pad=\"  \"><span class=sf-dump-note>report.expired.item</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">report.expired.item</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-48474299\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.221717, "xdebug_link": null}, {"message": "[ability => report.reorder.item, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>report.reorder.item</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">report.reorder.item</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.222252, "xdebug_link": null}, {"message": "[ability => tax.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>tax.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">tax.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.222633, "xdebug_link": null}, {"message": "[ability => app.settings.edit, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>app.settings.edit</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">app.settings.edit</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.222995, "xdebug_link": null}, {"message": "[ability => company.edit, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-32877192 data-indent-pad=\"  \"><span class=sf-dump-note>company.edit</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">company.edit</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-32877192\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.223382, "xdebug_link": null}, {"message": "[ability => tax.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1874021461 data-indent-pad=\"  \"><span class=sf-dump-note>tax.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">tax.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1874021461\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.223719, "xdebug_link": null}, {"message": "[ability => payment.type.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1157471772 data-indent-pad=\"  \"><span class=sf-dump-note>payment.type.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">payment.type.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1157471772\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.224484, "xdebug_link": null}, {"message": "[ability => currency.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1353124268 data-indent-pad=\"  \"><span class=sf-dump-note>currency.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">currency.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1353124268\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.225192, "xdebug_link": null}, {"message": "[ability => unit.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-809717930 data-indent-pad=\"  \"><span class=sf-dump-note>unit.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">unit.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-809717930\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.226156, "xdebug_link": null}, {"message": "[ability => language.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1943703324 data-indent-pad=\"  \"><span class=sf-dump-note>language.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">language.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1943703324\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.226985, "xdebug_link": null}, {"message": "[ability => purchase.bill.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1347568118 data-indent-pad=\"  \"><span class=sf-dump-note>purchase.bill.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">purchase.bill.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1347568118\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.228781, "xdebug_link": null}, {"message": "[ability => sale.invoice.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-326158923 data-indent-pad=\"  \"><span class=sf-dump-note>sale.invoice.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">sale.invoice.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-326158923\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.229348, "xdebug_link": null}, {"message": "[ability => sale.invoice.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1585634934 data-indent-pad=\"  \"><span class=sf-dump-note>sale.invoice.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">sale.invoice.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1585634934\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.229866, "xdebug_link": null}, {"message": "[ability => sale.invoice.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-115566258 data-indent-pad=\"  \"><span class=sf-dump-note>sale.invoice.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">sale.invoice.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-115566258\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.231385, "xdebug_link": null}, {"message": "[ability => purchase.bill.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1836455981 data-indent-pad=\"  \"><span class=sf-dump-note>purchase.bill.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">purchase.bill.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1836455981\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.231868, "xdebug_link": null}, {"message": "[ability => expense.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1901246977 data-indent-pad=\"  \"><span class=sf-dump-note>expense.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">expense.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1901246977\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.23228, "xdebug_link": null}, {"message": "[ability => sale.invoice.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1224620870 data-indent-pad=\"  \"><span class=sf-dump-note>sale.invoice.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">sale.invoice.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1224620870\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.232776, "xdebug_link": null}, {"message": "[ability => purchase.bill.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-956736874 data-indent-pad=\"  \"><span class=sf-dump-note>purchase.bill.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">purchase.bill.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-956736874\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.233233, "xdebug_link": null}, {"message": "[ability => purchase.order.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1191144324 data-indent-pad=\"  \"><span class=sf-dump-note>purchase.order.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">purchase.order.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1191144324\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.233672, "xdebug_link": null}, {"message": "[ability => sale.return.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1627367814 data-indent-pad=\"  \"><span class=sf-dump-note>sale.return.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">sale.return.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1627367814\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.234154, "xdebug_link": null}, {"message": "[ability => purchase.return.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1832626338 data-indent-pad=\"  \"><span class=sf-dump-note>purchase.return.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">purchase.return.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1832626338\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.234612, "xdebug_link": null}, {"message": "[ability => stock_transfer.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-690182308 data-indent-pad=\"  \"><span class=sf-dump-note>stock_transfer.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">stock_transfer.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-690182308\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.235055, "xdebug_link": null}, {"message": "[ability => item.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-438985154 data-indent-pad=\"  \"><span class=sf-dump-note>item.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">item.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-438985154\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.235823, "xdebug_link": null}, {"message": "[ability => customer.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2141830799 data-indent-pad=\"  \"><span class=sf-dump-note>customer.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">customer.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2141830799\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.236369, "xdebug_link": null}, {"message": "[ability => supplier.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1138820743 data-indent-pad=\"  \"><span class=sf-dump-note>supplier.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">supplier.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1138820743\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.237068, "xdebug_link": null}, {"message": "[ability => sale.quotation.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1622130276 data-indent-pad=\"  \"><span class=sf-dump-note>sale.quotation.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">sale.quotation.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1622130276\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.237749, "xdebug_link": null}, {"message": "[\n  ability => general.permission.to.apply.discount.to.purchase,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2144322010 data-indent-pad=\"  \"><span class=sf-dump-note>general.permission.to.apply.discount.to.purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"48 characters\">general.permission.to.apply.discount.to.purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2144322010\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.245203, "xdebug_link": null}, {"message": "[\n  ability => general.permission.to.apply.discount.to.sale,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-582774387 data-indent-pad=\"  \"><span class=sf-dump-note>general.permission.to.apply.discount.to.sale</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"44 characters\">general.permission.to.apply.discount.to.sale</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-582774387\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.245629, "xdebug_link": null}]}, "session": {"_token": "HNkZVWy1EvJYmVGpV26q1OhE2BZcLjdiaGB3lTAA", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/manufacturing/order/create\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"telescope": "<a href=\"http://127.0.0.1:8000/_debugbar/telescope/9f4181a5-eea1-4997-9ce9-414a5feab45f\" target=\"_blank\">View in Telescope</a>", "path_info": "/manufacturing/order/create", "status_code": "<pre class=sf-dump id=sf-dump-1649905908 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1649905908\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1942145297 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1942145297\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2131303424 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2131303424\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1960829538 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">http://127.0.0.1:8000/manufacturing_orders</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">en-GB,en;q=0.9,en-US;q=0.8,fr;q=0.7,ar;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2047 characters\">ph_phc_3ESMmY9SgqEAGBB6sMGK5ayYHkeUuknH2vP6FmWH9RA_posthog=%7B%22distinct_id%22%3A%220196b41d-0793-7e06-86ce-017691734ea0%22%2C%22%24sesid%22%3A%5B1746780823698%2C%220196b41d-078e-7e17-a700-24eed1211756%22%2C1746778523534%5D%7D; aureuserp_session=eyJpdiI6IlIvOXVBdXl4UFJOWHhsSjA2YWNNeGc9PSIsInZhbHVlIjoiVXFQdlhTd3ZFMXZyV1JwNjczTEJUczB4N3YxZG5uWXNpamRHWXZyb0xIdDdiaXFoL2xOalBmNmtqMVdKZWllamlOWnNxREZvNjV5VWVxOXVGY3Q5MVNqK1ArTVBteDZIY01KTXdoc2xEaDEwY2draUl3Qi9SbHJUUnVOOWNsY1ciLCJtYWMiOiI1OWU4NmE1NjczOTQyZjZjZDM4YTYyMzZhMmZmODg4NGJlMWQ1ZjY3MjRkNzM5ZmRkMTllNDFjMTE1ODYxN2QyIiwidGFnIjoiIn0%3D; language_data=eyJpdiI6IkFLeWYyUVZhc3dlMURKNE1kWHYxUmc9PSIsInZhbHVlIjoiSUtsV1UvZ1FmVFNjRTgvMVZpM3E2YkRTR3FGNXl4akNCQVhRVkU2SlFqTnJoYUlHTml2T0hQb21RMzdhcm0rdm1OZjExWjFPRmhRQ00zTjNuZEZWeU5HNUNxZEsxQUdVbG1yVk1CM2hMY1BiYkZXNjQ1czFIUVgrTXhPd2VvZFloMWZHMlh0d3VtaDVMVDRHWU16Qm9qZkJ6V3JuOWtCK3lZbEJud3ZLNytKaEw2dGxUcnd4d0psQTdER1k5bWMzIiwibWFjIjoiOWU3MGMxNTA5MGE5YjJlZjQ4NzNjZDc1ZTliNWM1NTM1MTQ4NzcwN2QxZTNlN2E3YTZmNGFjMTI3YWZhOThkYiIsInRhZyI6IiJ9; theme_mode=eyJpdiI6Ii9XM3FTcWRDTHBmaVg5M0k2cFJiMEE9PSIsInZhbHVlIjoiNlZBU0xFRmZDaEdXVE5JRVMyMFhQcG0waG1PUWdFSDdWb1pCMWx3R0JVank5WXhQMytscW4vTW4yMkZSWlhWNjVISlc3NjNtcGNMckVYK1JXb3FOU3c9PSIsIm1hYyI6ImU2MmRhOThiNGQyNDA3N2UwMGViMjRhMzRhOThkYmJkZDdjMmNjYjhjZDk2MjIxMWZlZjVlNjA4Y2ZkYTkzNGIiLCJ0YWciOiIifQ%3D%3D; XSRF-TOKEN=eyJpdiI6ImtqenJjTGFJd3UxbXlGQnVsUHpJTmc9PSIsInZhbHVlIjoid05FSVcyb2NDMjRtQ2I0a0swcWxQb1ZUV3J4d1gramVUWDBDc0tFRURHWnk3TmMrK2lwcGF5MHllcW5maUFWK3c3K3lVa21JazZnbFgxUHR5U2JOYXlWeUF5djdRTjArSmhqWHlRaVQzZDdsOHk4SnBoN3dON1RqN3NIK0Y3cisiLCJtYWMiOiI5ZTQ4ZGEwMDY4YTE1NzhhYjc5NTdlNDk0Y2YyOTVlZjcxMmM0YzIyZTVkZGM1NDUyYWE3ZmQwMDNhODMzOGU0IiwidGFnIjoiIn0%3D; delta_session=eyJpdiI6IkFDeTYrS2hCWVVSYzNQYVhseXlyMWc9PSIsInZhbHVlIjoicHNhOG5xeWpNcE93cmN3NmRTL1NwWVBOT1ZHTWxwVkJCaXRrTGQ0QlB4SmFVN05FNVhsQWFyL0NISHo5d3dYdVVZNzZ3anFlcnlsUXFMRERTVjYrK29hdWtyeFVYTWRJNVUxb0Q1VFFFc0J3R3RtWThDMU1FcVlKMTkrQkhUOWIiLCJtYWMiOiI4YjlhZTNiNzAzNDA3NzhhZmUxYWQ1YTAxN2JjMDBiZDA1ZWQxZTE2NDk0NGFkZjdkZjdhOWQyMGIwMjMzMzg3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1960829538\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1167459061 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ph_phc_3ESMmY9SgqEAGBB6sMGK5ayYHkeUuknH2vP6FmWH9RA_posthog</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>aureuserp_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>language_data</span>\" => \"<span class=sf-dump-str title=\"94 characters\">{&quot;language_code&quot;:null,&quot;language_flag&quot;:&quot;flag-icon-us&quot;,&quot;direction&quot;:&quot;ltr&quot;,&quot;emoji&quot;:&quot;flag-icon-us&quot;}</span>\"\n  \"<span class=sf-dump-key>theme_mode</span>\" => \"<span class=sf-dump-str title=\"11 characters\">light-theme</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HNkZVWy1EvJYmVGpV26q1OhE2BZcLjdiaGB3lTAA</span>\"\n  \"<span class=sf-dump-key>delta_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">uHnuvxItO3CJLJzok54Jqf12VJbTwvqHEtEyDXtZ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1167459061\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1531339034 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 15:09:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlhjTGFsdlhWbGMvYU5Da25QbHk4Rnc9PSIsInZhbHVlIjoiUkVncjFkbW40LzdEZVV5L09OWXh3djJBYWQ1djZGd3JnZEFGQXFRTzZWeUFycE51cWFYZ0Z4RjBwY2hTcGQycDlwQzlqcjVQbU5kdzRhZ3Q3QXBqZzRKMEhuQTBjalQxTXlCK0d4ZExxSy9OWHlKL3pzUHRlT0tFVnRNYkh4YnoiLCJtYWMiOiJmNDJjZjUwYTg3YjdkM2IyMjBjN2ZlNGM0ZTgxZjI0ZmQ2OGFiYjAxMWNiM2U4N2UxZGFiMDgxNWZiZTgxNWNjIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 17:09:28 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">delta_session=eyJpdiI6IlNHZVdsQmdlOE5vVjBKKysva3M4OGc9PSIsInZhbHVlIjoibUFFc2k2M2NvTlNUZVdXeXlza0p3NDB2VVdMVE5GUkloWjhkUFEzYzhtQjFTT29LbXNVVkR3bmFtVk9mamdURHAwQlNBdGtZRXFuVWt1cCtTOUNoeklja2ZBSFUyL3JPbEs2YW9oRGp0czdZaGtzYmhJaHU3L2hraW9BMUkyN0oiLCJtYWMiOiIwMjgwZTcwZTdmMzgzMGIwYTMyMzYwYWM3Y2RkZjZiMzJiNzQ1NmRjMWNkNzZmM2M2ZjZhN2Y4MjQ4ZWRmOTUyIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 17:09:28 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlhjTGFsdlhWbGMvYU5Da25QbHk4Rnc9PSIsInZhbHVlIjoiUkVncjFkbW40LzdEZVV5L09OWXh3djJBYWQ1djZGd3JnZEFGQXFRTzZWeUFycE51cWFYZ0Z4RjBwY2hTcGQycDlwQzlqcjVQbU5kdzRhZ3Q3QXBqZzRKMEhuQTBjalQxTXlCK0d4ZExxSy9OWHlKL3pzUHRlT0tFVnRNYkh4YnoiLCJtYWMiOiJmNDJjZjUwYTg3YjdkM2IyMjBjN2ZlNGM0ZTgxZjI0ZmQ2OGFiYjAxMWNiM2U4N2UxZGFiMDgxNWZiZTgxNWNjIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 17:09:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">delta_session=eyJpdiI6IlNHZVdsQmdlOE5vVjBKKysva3M4OGc9PSIsInZhbHVlIjoibUFFc2k2M2NvTlNUZVdXeXlza0p3NDB2VVdMVE5GUkloWjhkUFEzYzhtQjFTT29LbXNVVkR3bmFtVk9mamdURHAwQlNBdGtZRXFuVWt1cCtTOUNoeklja2ZBSFUyL3JPbEs2YW9oRGp0czdZaGtzYmhJaHU3L2hraW9BMUkyN0oiLCJtYWMiOiIwMjgwZTcwZTdmMzgzMGIwYTMyMzYwYWM3Y2RkZjZiMzJiNzQ1NmRjMWNkNzZmM2M2ZjZhN2Y4MjQ4ZWRmOTUyIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 17:09:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1531339034\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1249048960 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HNkZVWy1EvJYmVGpV26q1OhE2BZcLjdiaGB3lTAA</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"48 characters\">http://127.0.0.1:8000/manufacturing/order/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1249048960\", {\"maxDepth\":0})</script>\n"}}