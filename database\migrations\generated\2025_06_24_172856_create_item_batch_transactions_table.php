<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('item_batch_transactions', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('unique_code');
            $table->unsignedBigInteger('item_transaction_id')->index('item_batch_transactions_item_transaction_id_foreign');
            $table->unsignedBigInteger('item_batch_master_id')->nullable()->index('item_batch_transactions_item_batch_master_id_foreign');
            $table->unsignedBigInteger('warehouse_id')->index('item_batch_transactions_warehouse_id_foreign');
            $table->unsignedBigInteger('item_id')->index('item_batch_transactions_item_id_foreign');
            $table->decimal('quantity', 20, 4)->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('item_batch_transactions');
    }
};
