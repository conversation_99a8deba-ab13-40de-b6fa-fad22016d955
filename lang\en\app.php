<?php

return [

    /*
    |--------------------------------------------------------------------------
    | App Setting Language Lines
    |--------------------------------------------------------------------------
    |
    | Used for app view management only
    |
    */

    'settings' 						    => 'Settings',
    'app_settings' 						=> 'App Settings',
    'application_name' 					=> 'Application Name',
    'general' 						    => 'General',
    'logo' 						        => 'Logo',
    'app_logo'                          => 'App Logo',
    'dark_logo' 						=> 'Dark Logo',
    'light_logo' 						=> 'Light Logo',
    'favicon' 						    => 'Favicon',
    'email_settings' 				    => 'Email Settings',
    'sms_settings'                      => 'SMS Settings',
    'cache' 				            => 'Cache',
    'clear_cache' 				        => 'Clear Cache',
    'date_format' 				        => 'Date Format',
    'time_format' 				        => 'Time Format',
    'database_backup' 				    => 'Database Backup',
    'change_password' 				    => 'Change Password',
    'save' 				                => 'Save',
    'update' 				            => 'Update',
    'delete' 				            => 'Delete',
    'edit' 				                => 'Edit',
    'dashboard' 				        => 'Dashboard',
    'footer_text' 				        => 'Footer Text',
    'submit' 				            => 'Submit',
    'close' 				            => 'Close',
    'clear' 				            => 'Clear',
    'failed_to_save_record'             => 'Failed to Save Record!!',
    'record_saved_successfully'         => 'Record Saved Successfully!!',
    'record_updated_successfully'       => 'Record Updated Successfully!!',
    'record_deleted_successfully'       => 'Record Deleted Successfully!!',
    'cannot_delete_records'             => "The record cannot be deleted. It's likely used in other parts of the application",
    'invalid_record_id'                 => 'Invalid Record ID: :record_id',

    'colored_logo'                      => 'Colored Logo',

    'language'                          => 'Language',
    'smtp_settings'                     => 'SMTP Settings',
    'host'                              => 'Host',
    'port'                              => 'Port',
    'status'                            => 'Status',

    'app_cache_cleared'                 => 'The application cache has been cleared!',

    'download'                          => 'Download',
    'create_group'                      => 'Create Group',
    'group_name'                        => 'Group Name',
    'group_list'                        => 'Group List',
    'action'                            => 'Action',
    'created_at'                        => 'Created at',
    'create_permission'                 => 'Create Permission',
    'permission_list'                   => 'Permission List',
    'permissions_list'                  => 'Permissions List',
    'permission_name'                   => 'Permission Name',
    'permissions'                       => 'Permissions',
    'roles_list'                        => 'Roles List',
    'create_role'                       => 'Create Role',
    'role_name'                         => 'Role Name',
    'group'                             => 'Group',
    'edit_group'                        => 'Edit Group',
    'select_all'                        => 'Select All',
    'display_name'                      => 'Display Name',
    'name'                              => 'Name',
    'edit_permission'                   => 'Edit Permission',
    'edit_role'                         => 'Edit Role',
    'roles'                             => 'Roles',
    'role'                              => 'Role',
    'email'                             => 'Email',
    'browse'                            => 'Browse',
    'reset'                             => 'Reset',
    'reset_password'                    => 'Reset Password',
    'allowsed_size_of_image'            => 'Allowed JPG, GIF or PNG. Max size of 1MB',
    'optional'                          => 'Optional',
    'options'                           => 'Options',
    'password_updated_successfully'     => 'Password Updated successfully',
    'add'                               => 'Add',
    'first_name'                        => 'First Name',
    'last_name'                         => 'Last Name',
    'full_name'                         => 'Full Name',
    'password'                          => 'Password',
    'confirm_password'                  => 'Confirm Password',
    'mobile'                            => 'Mobile',
    'whatsapp'                          => 'WhatsApp',
    'whatsapp_number'                   => 'WhatsApp Number',
    'address'                           => 'Address',
    'description'                       => 'Description',
    'unit_price'                        => 'Unit Price',
    'something_went_wrong'              => 'Something went wrong',
    'check_custom_log_file'             => '(Check Custom Log File).',
    'twilio'                            => 'Twilio',
    'account_SID'                       => 'Account SID',
    'auth_token'                        => 'Auth Token',
    'twilio_phone_number'               => 'Twilio Phone Number',
    'sms_status'                        => 'SMS Status: :status',
    'vonage'                            => 'Vonage',
    'api_key'                           => 'API Key',
    'api_secret'                        => 'API Secret',
    'company'                           => 'Company',
    'company_details'                   => 'Company Details',
    'company_logo'                      => 'Company Logo',
    'fevicon'                           => 'Favicon',
    'picture'                           => 'Picture',
    'timezone'                          => 'Timezone',
    'encryption'                        => 'Encryption',
    'sign_in'                           => 'Sign in',
    'qty'                               => 'QTY',
    'price'                             => 'Price',
    'discount'                          => 'Discount',
    'total'                             => 'Total',
    'add_row'                           => 'Add Row',
    'record_not_found'                  => 'Record Not Found',
    'grand_total'                       => 'Grand Total',
    'subtotal'                          => 'Sub Total',
    'prefix_codes'                      => 'Prefix Codes',
    'date'                              => 'Date',
    'no_records_found'                  => 'No Records Found!',
    'invoice'                           => 'Invoice',
    'print'                             => 'Print',
    'pdf'                               => 'PDF',
    'export_to_pdf'                     => 'Export to PDF',
    'download_pdf'                      => 'Download PDF',
    'thank_you'                         => 'Thank you',
    'computer_generated_receipt'        => 'Computer Generated Receipt',
    'staff'                             => 'Staff',
    'staff_status'                      => 'Staff Status',
    'note'                              => 'Note',
    'update_status'                     => 'Update Status',
    'your_note'                         => 'Your Note',
    'send'                              => 'Send',
    'back'                              => 'Back',
    'back_to_login'                     => 'Back to Login',
    'slug'                              => 'Slug',
    'direction'                         => 'Direction',
    'short_code'                        => 'Short Code',

    'emoji'                             => 'Emoji',
    'emoji_flag'                        => 'Emoji Flag',
    'update_at'                         => 'Updated at',
    'reports'                           => 'Reports',
    'from_date'                         => 'From Date',
    'to_date'                           => 'To Date',
    'records'                           => 'Records',
    'export'                            => 'Export',
    'excel'                             => 'Excel',

    'app_log'                           => 'App Log',
    'clear_log'                         => 'Clear Log',
    'info'                              => 'Info',
    'clear_log_message'                         => 'The application clears the application error logs files(Laravel & custom log files).',
    'cache_clear_message'                         => 'The application clears the cache, routes, and app configuration settings, optimizing the application.',
    'cleared_error_log_file'                         => 'Log messages cleared successfully!',
    'completed'                         => 'Completed',
    'country_flag'                              => 'Country Flag',
    'select_flag'                               => 'Select Flag',
    'select'                                    => 'Select',
    'invalid_record'                            => 'Invalid Record',
    'demo_mode_restricted'                            => 'This feature is restricted in Demo Mode!',

    'copy'                              => 'Copy',
    'apply'                             => 'Apply',

    'created_by'                        => 'Created by',

    'filter'                            => 'Filter',
    'enter_item_name'                   => 'Enter Item Name',
    'round_off'                         => 'Round Off',
    'price_per_unit'                    => 'Price/Unit',
    'general_settings'                  => 'General Settings',
    'quantity_precision'                  => 'Quantity Precision',
    'number_precision'                  => 'Number Precision',
    'image'                             => 'Image',
    'auto'                              => 'Auto',

    'as_of_date'                        => 'As of Date',
    'save_data_before_close_window'     => 'Save data before closing this window.',

    'error'                             => 'Error',
    'transactions'                      => 'Transactions',
    'transaction_list'                  => 'Transaction List',
    'transaction_type'                  => 'Transaction Type',
    'reference_no'                      => 'Reference No.',
    'browse_file'                       => 'Browse File',
    'download_sample'                   => 'Download Sample',
    'records_not_found'                 => 'Records Not Found!',
    'instructions'                      => 'Instructions',
    'utilities'                         => 'Utilities',
    'import'                            => 'Import',
    'phone'                             => 'Phone',
    'opening_balance'                   => 'Opening Balance',
    'opening_balance_is'                => 'Opening Balance is',
    'state_name'                        => 'State Name',
    'due_date'                          => 'Due Date',
    'paid_amount_should_not_be_less_than_zero'=> 'Paid amount shouldn\'t be less than 0.',
    'state_of_supply'                   => 'State of supply',

    'order_to'                          => 'Order To',
    'order_from'                        => 'Order From',
    'ship_from'                         => 'Ship From',
    'ship_to'                           => 'Ship To',
    'company_settings'                  => 'Company Settings',
    'print_settings'                    => 'Print Settings',
    'show_tax_summary'                  => 'Show Tax Summary',
    'details'                           => 'Details',

    'amount_in_words'                   => 'Bill Amount in Words',
    'amount'                            => 'Amount',
    'terms_and_conditions'              => 'Terms & Conditions',
    'show_terms_and_conditions_on_invoice'=> 'Show Terms & Conditions on Invoice/Bill',
    'bank_details'                      => 'Bank details',
    'authorized_signatory'              => 'Authorized Signatory',
    'signature'                         => 'Signature',
    'show_signature_on_invoice'         => 'Show Signature on Invoice/Bill',
    'you_saved'                         => 'You Saved',
    'bill'                              => 'Bill',
    'bill_from'                         => 'Bill From',
    'view_bill'                         => 'View Bill',
    'no'                                => 'No.',
    'number'                            => 'Number',
    'invoice_or_reference_no'           => 'Invoice/Reference No.',
    'type'                              => 'Type',
    'balance'                           => 'Balance',
    'paid_amount'                       => 'Paid Amount',
    'discount_amount'                   => 'Discount Amount',
    'payment'                           => 'Payment',
    'bill_to'                           => 'Bill To',
    'save_and_print'                    => 'Save & Print',
    'new'                               => 'New',
    'generate'                          => 'Generate',
    'send_email'                        => 'Send Email',
    'send_sms'                          => 'Send SMS',
    'filter_type'                       => 'Filter Type',
    'use_date'                          => 'Use Date',
    'bank_accounts'                     => 'Bank Accounts',
    'other_details'                     => 'Other Details',
    'category'                          => 'Category',
    'total_amount'                      => 'Total Amount',
    'balance_type'                      => 'Balance Type',
    'documentation'                     => 'Documentation',
    'modules'                           => 'Modules',
    'enable_crm'                        => 'Enable Studio CRM',
    /*1.1.2*/
    'time'                              => 'Time',
    'code'                              => 'Code',
    /*1.3*/
    'show_due_payment_on_invoice_or_bill' => 'Show Due Payment on Invoice/Bill',
    'total_due_balance'                 => 'Total Due Balance',
    // 1.4
    'show_discount'                       => 'Enable Discount on Invoice/Bill',
    'allow_negative_stock_billing'        => 'Enable Negative(-) Stock Billing',
    //1.4.1
    'previous_due'                 => 'Previous Due',
    'worth'                 => 'Worth',
    'cost'                 => 'Cost',
    'profit'                 => 'Profit',

    //1.4.4
    'view_invoice'                 => 'View Invoice',
    'status_history'              => 'Status History',
    'status_update_history'              => 'Status Update History',
    'updated_by'              => 'Updated By',
    'load'              => 'Load',
    'contact'              => 'Contact',
    //1.4.7
    'failed_to_delete_records'              => 'Failed to delete records',
    'previous_due_balance'              => 'Previous Due Balance',
    'search'              => 'Search',
    'yes'              => 'Yes',
    'no'              => 'No',
    'location'              => 'Location',


];
