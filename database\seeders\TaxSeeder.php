<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Tax;

class TaxSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $taxes = [
            [
                'name' => 'None',
                'rate' => '0.00',
                'status' => 1,
                'is_deletable' => 0,
            ],
            [
                'name' => '19%',
                'rate' => '19.00',
                'status' => 1,
                'is_deletable' => 1,
            ],
        ];

        foreach ($taxes as $tax) {
            Tax::firstOrCreate(
                ['name' => $tax['name']],
                $tax
            );
        }
    }
}
