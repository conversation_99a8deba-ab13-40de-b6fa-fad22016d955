<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('manufacturing_orders', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('order_number')->unique();
            $table->date('order_date');
            $table->date('delivery_date')->nullable();
            $table->string('designation');
            $table->string('tissus');
            $table->decimal('largeur_fini');
            $table->string('mecanisme');
            $table->decimal('qte_to_billed');
            $table->string('billed_unity');
            $table->decimal('unit_price', 10);
            $table->decimal('extras_fees', 10)->nullable();
            $table->decimal('qty');
            $table->enum('order_status', ['Draft', 'In Progress', 'Completed', 'Delivered'])->default('Draft');
            $table->decimal('fabric_requirement');
            $table->enum('curtain_type', ['Store', 'Rideaux']);
            $table->enum('curtain_finish', ['Wave', 'PP', 'PF']);
            $table->enum('manipulation', ['Moteur', 'Manuelle']);
            $table->enum('height_type', ['simple', 'double']);
            $table->enum('positioning_type', ['lateral', 'central'])->nullable();
            $table->decimal('sous_plafond')->nullable();
            $table->decimal('hauteur_finale')->nullable();
            $table->string('formule_appliquee')->nullable();
            $table->integer('nombre_agrafes')->nullable();
            $table->text('notes')->nullable();
            $table->unsignedBigInteger('created_by')->index('manufacturing_orders_created_by_foreign');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('manufacturing_orders');
    }
};
