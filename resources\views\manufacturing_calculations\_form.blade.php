<!--start form content -->
<div class="row g-3">
    <div class="col-md-4">
        <x-label for="height_type" name="{{ __('manufacturing.height_type') }}" />
        <select class="form-select @error('height_type') is-invalid @enderror" name="height_type" id="height_type" required>
            <option value="">{{ __('manufacturing.select_height_type') }}</option>
            <option value="simple" {{ old('height_type', $calculation->height_type ?? '') == 'simple' ? 'selected' : '' }}>{{ __('manufacturing.simple') }}</option>
            <option value="double" {{ old('height_type', $calculation->height_type ?? '') == 'double' ? 'selected' : '' }}>{{ __('manufacturing.double') }}</option>
        </select>
        @error('height_type')
            <div class="invalid-feedback">{{ $message }}</div>
        @enderror
    </div>

    <div class="col-md-4">
        <x-label for="curtain_finish" name="{{ __('manufacturing.curtain_finish') }}" />
        <select class="form-select @error('curtain_finish') is-invalid @enderror" name="curtain_finish" id="curtain_finish" required>
            <option value="">{{ __('manufacturing.select_curtain_finish') }}</option>
            <option value="Wave" {{ old('curtain_finish', $calculation->curtain_finish ?? '') == 'Wave' ? 'selected' : '' }}>Wave</option>
            <option value="PP" {{ old('curtain_finish', $calculation->curtain_finish ?? '') == 'PP' ? 'selected' : '' }}>PP</option>
            <option value="PF" {{ old('curtain_finish', $calculation->curtain_finish ?? '') == 'PF' ? 'selected' : '' }}>PF</option>
        </select>
        @error('curtain_finish')
            <div class="invalid-feedback">{{ $message }}</div>
        @enderror
    </div>

    <div class="col-md-4" id="manipulation_group">
        <x-label for="manipulation" name="{{ __('manufacturing.manipulation') }}" />
        <select class="form-select @error('manipulation') is-invalid @enderror" name="manipulation" id="manipulation">
            <option value="">{{ __('manufacturing.select_manipulation') }}</option>
            <option value="Moteur" {{ old('manipulation', $calculation->manipulation ?? '') == 'Moteur' ? 'selected' : '' }}>{{ __('manufacturing.manipulation_types.moteur') }}</option>
            <option value="Manuelle" {{ old('manipulation', $calculation->manipulation ?? '') == 'Manuelle' ? 'selected' : '' }}>{{ __('manufacturing.manipulation_types.manuelle') }}</option>
        </select>
        @error('manipulation')
            <div class="invalid-feedback">{{ $message }}</div>
        @enderror
    </div>

    <div class="col-md-4">
        <x-label for="positioning_type" name="{{ __('manufacturing.positioning_type') }}" />
        <select class="form-select @error('positioning_type') is-invalid @enderror" name="positioning_type" id="positioning_type">
            <option value="">{{ __('manufacturing.select_positioning_type') }}</option>
            <option value="lateral" {{ old('positioning_type', $calculation->positioning_type ?? '') == 'lateral' ? 'selected' : '' }}>{{ __('manufacturing.lateral') }}</option>
            <option value="central" {{ old('positioning_type', $calculation->positioning_type ?? '') == 'central' ? 'selected' : '' }}>{{ __('manufacturing.central') }}</option>
        </select>
        <small class="text-muted">{{ __('manufacturing.positioning_help_text') }}</small>
        @error('positioning_type')
            <div class="invalid-feedback">{{ $message }}</div>
        @enderror
    </div>

    <div class="col-md-4">
        <x-label for="height_deduction" name="{{ __('manufacturing.height_deduction_cm') }}" />
        <x-input type="number" step="0.1" name="height_deduction" id="height_deduction"
               :required="true" value="{{ old('height_deduction', $calculation->height_deduction ?? '') }}"/>
        @error('height_deduction')
            <div class="invalid-feedback">{{ $message }}</div>
        @enderror
    </div>

    <div class="col-md-4">
        <x-label for="formula_description" name="{{ __('manufacturing.formula_description') }}" />
        <x-input type="text" name="formula_description" id="formula_description"
               :required="true" value="{{ old('formula_description', $calculation->formula_description ?? '') }}"/>
        @error('formula_description')
            <div class="invalid-feedback">{{ $message }}</div>
        @enderror
    </div>

    <div class="col-md-4" id="agrafes_spacing_group">
        <x-label for="agrafes_spacing" name="{{ __('manufacturing.agrafes_spacing_cm') }}" />
        <x-input type="number" name="agrafes_spacing" id="agrafes_spacing"
               value="{{ old('agrafes_spacing', $calculation->agrafes_spacing ?? '') }}"/>
        <small class="text-muted">{{ __('manufacturing.leave_empty_if_not_applicable') }}</small>
        @error('agrafes_spacing')
            <div class="invalid-feedback">{{ $message }}</div>
        @enderror
    </div>

    @if(isset($calculation))
        <div class="col-md-4">
            <x-label for="is_active" name="{{ __('manufacturing.status') }}" />
            <div class="form-check form-switch">
                <input type="checkbox" name="is_active" id="is_active" class="form-check-input"
                       value="1" {{ old('is_active', $calculation->is_active ?? true) ? 'checked' : '' }}>
                <label class="form-check-label" for="is_active">{{ __('manufacturing.active') }}</label>
            </div>
        </div>
    @endif
</div>

<div class="col-12 mt-3">
    <div class="d-md-flex d-grid align-items-center gap-3">
        <button type="submit" class="btn btn-primary px-4">
            <i class="bx bx-save"></i> {{ isset($calculation) ? __('manufacturing.update') : __('manufacturing.create') }}
        </button>
        <a href="{{ route('manufacturing.manufacturing-calculations.index') }}" class="btn btn-light px-4">
            <i class="bx bx-arrow-back"></i> {{ __('manufacturing.back_to_list') }}
        </a>
    </div>
</div>

@push('scripts')
<script>
    $(document).ready(function() {
        // Show/hide manipulation field based on height type
        $('#height_type').on('change', function() {
            const heightType = $(this).val();
            const manipulationGroup = $('#manipulation_group');
            
            if (heightType === 'double') {
                manipulationGroup.hide();
                $('#manipulation').val('');
            } else {
                manipulationGroup.show();
            }
        }).trigger('change');

        // Show/hide agrafes spacing based on curtain finish
        $('#curtain_finish').on('change', function() {
            const finish = $(this).val();
            const agrafesGroup = $('#agrafes_spacing_group');
            
            if (finish === 'Wave') {
                agrafesGroup.show();
            } else {
                agrafesGroup.hide();
                $('#agrafes_spacing').val('');
            }
        }).trigger('change');
    });
</script>
@endpush 