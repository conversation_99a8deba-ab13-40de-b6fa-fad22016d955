<div class="row g-3">
    <div class="col-md-4">
        <x-label for="order_number" name="{{ __('manufacturing.order_number') }}" />
        <x-input type="text" name="order_number" :required="true" value="{{ $data['prefix_code'] ?? 'MO' }}-{{ sprintf('%05d', $data['count_id'] ?? '00001') }}" disabled readonly/>
    </div>

    <div class="col-md-4">
        <x-label for="order_date" name="{{ __('manufacturing.order_date') }}" />
        <div class="input-group mb-3">
            <x-input type="text" additionalClasses="datepicker" name="order_date" :required="true" value=""/>
            <span class="input-group-text"><i class="bx bx-calendar-alt"></i></span>
        </div>
    </div>

    <div class="col-md-4">
        <x-label for="delivery_date" name="{{ __('manufacturing.delivery_date') }}" />
        <div class="input-group mb-3">
            <x-input type="text" additionalClasses="datepicker" name="delivery_date" :required="true" value=""/>
            <span class="input-group-text"><i class="bx bx-calendar-alt"></i></span>
        </div>
    </div>

    <div class="col-md-4">
        <x-label for="designation" name="{{ __('manufacturing.designation') }}" />
        <x-input type="text" name="designation" :required="true" value=""/>
    </div>

    <div class="col-md-4">
        <x-label for="tissus" name="{{ __('manufacturing.tissus') }}" />
        <x-input type="text" name="tissus" :required="true" value=""/>
    </div>

    <div class="col-md-4">
        <x-label for="mecanisme" name="{{ __('manufacturing.mecanisme') }}" />
        <select class="form-select" name="mecanisme" required>
            <option value="BR">{{ __('manufacturing.mechanism_types.br') }}</option>
            <option value="GHD">{{ __('manufacturing.mechanism_types.ghd') }}</option>
        </select>
    </div>

    <div class="col-md-4">
        <x-label for="order_status" name="{{ __('manufacturing.order_status') }}" />
        <select class="form-select" name="order_status" required>
            <option value="Draft">{{ __('manufacturing.status_options.draft') }}</option>
            <option value="In Progress">{{ __('manufacturing.status_options.in_progress') }}</option>
            <option value="Completed">{{ __('manufacturing.status_options.completed') }}</option>
            <option value="Delivered">{{ __('manufacturing.status_options.delivered') }}</option>
        </select>
    </div>
</div> 