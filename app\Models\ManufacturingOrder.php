<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ManufacturingOrder extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_number',
        'order_date',
        'delivery_date',
        'designation',
        'tissus',
        'largeur_fini',
        'mecanisme',
        'qte_to_billed',
        'billed_unity',
        'unit_price',
        'extras_fees',
        'qty',
        'order_status',
        'fabric_requirement',
        'curtain_type',
        'curtain_finish',
        'manipulation',
        'height_type',
        'positioning_type',
        'sous_plafond',
        'hauteur_finale',
        'formule_appliquee',
        'nombre_agrafes',
        'notes',
        'created_by'
    ];

    protected $casts = [
        'order_date' => 'date',
        'delivery_date' => 'date',
        'largeur_fini' => 'decimal:2',
        'qte_to_billed' => 'decimal:2',
        'unit_price' => 'decimal:2',
        'extras_fees' => 'decimal:2',
        'qty' => 'decimal:2',
        'fabric_requirement' => 'decimal:2',
        'sous_plafond' => 'decimal:2',
        'hauteur_finale' => 'decimal:2',
        'nombre_agrafes' => 'integer',
    ];

    /**
     * Get the user who created the order.
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }
} 