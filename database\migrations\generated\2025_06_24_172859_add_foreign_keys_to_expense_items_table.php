<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('expense_items', function (Blueprint $table) {
            $table->foreign(['expense_id'])->references(['id'])->on('expenses')->onUpdate('no action')->onDelete('cascade');
            $table->foreign(['expense_item_master_id'])->references(['id'])->on('expense_item_master')->onUpdate('no action')->onDelete('cascade');
            $table->foreign(['tax_id'])->references(['id'])->on('taxes')->onUpdate('no action')->onDelete('no action');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('expense_items', function (Blueprint $table) {
            $table->dropForeign('expense_items_expense_id_foreign');
            $table->dropForeign('expense_items_expense_item_master_id_foreign');
            $table->dropForeign('expense_items_tax_id_foreign');
        });
    }
};
