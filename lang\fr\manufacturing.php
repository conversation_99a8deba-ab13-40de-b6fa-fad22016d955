<?php

return [
    'select_type' => ' Sélectionner le type de commande',
    'select_type_placeholder' => 'Sélectionner le type de commande',
    // Page titles
    'manufacturing' => 'Fabrication',
    'manufacturing_orders' => 'Ordres de Fabrication',
    'calculation_rules' => 'Règles de Calcul',
    'create_order' => 'Créer une Commande',
    
    // Form Labels
    'order_number' => 'Numéro de Commande',
    'order_date' => 'Date de Commande',
    'delivery_date' => 'Date de Livraison',
    'designation' => 'Désignation',
    'tissus' => 'Tissus',
    'mecanisme' => 'Mécanisme',
    'order_status' => 'État de la Commande',
    'largeur_fini' => 'Largeur Fini',
    'sous_plafond' => 'Sous Plafond',
    'curtain_type' => 'Type de Rideau',
    'curtain_finish' => 'Finition du Rideau',
    'manipulation' => 'Manipulation',
    'height_type' => 'Type de Hauteur',
    'positioning_type' => 'Type de Positionnement',
    'hauteur_finale' => 'Hauteur Finale',
    'formule_appliquee' => 'Formule Appliquée',
    'nombre_agrafes' => "Nombre d'Agrafes",
    'nombre_agrafes_rule' => "Formule de Calcul des Agrafes",
    'qte_to_billed' => 'Quantité à Facturer',
    'billed_unity' => 'Unité de Facturation',
    'unit_price' => 'Prix Unitaire',
    'extras_fees' => 'Frais Supplémentaires',
    'fabric_requirement' => 'Besoin en Tissu',
    'qty' => 'Quantité',
    'total_amount' => 'Montant Total',
    'notes' => 'Notes',
    'select_positioning_type' => 'Sélectionner le type de positionnement (Optionnel)',
    'lateral' => 'Latéral',
    'central' => 'Central',
    'positioning_help_text' => 'Utilisé pour calculer le nombre d\'agrafes',

    // Select Options
    'status_options' => [
        'draft' => 'Brouillon',
        'in_progress' => 'En Cours',
        'completed' => 'Terminé',
        'delivered' => 'Livré'
    ],
    'curtain_types' => [
        'store' => 'Store',
        'rideaux' => 'Rideaux'
    ],
    'curtain_finishes' => [
        'wave' => 'Vague',
        'pp' => 'PP',
        'pf' => 'PF'
    ],
    'manipulation_types' => [
        'moteur' => 'Moteur',
        'manuelle' => 'Manuelle'
    ],
    'height_types' => [
        'simple' => 'Simple',
        'double' => 'Double'
    ],
    'positioning_types' => [
        'lateral' => 'Latéral',
        'central' => 'Central'
    ],
    'mechanism_types' => [
        'br' => 'BR',
        'ghd' => 'GHD'
    ],

    // Steps
    'basic_info' => 'Informations de Base',
    'dimensions' => 'Dimensions',
    'curtain_details' => 'Détails du Rideau',
    'pricing_additional_info' => 'Finialisation',

    // Buttons
    'next' => 'Suivant',
    'previous' => 'Précédent',
    'submit' => 'Soumettre',
    'close' => 'Fermer',

    // Messages
    'success_create' => 'Ordre de fabrication créé avec succès.',
    'success_update' => 'Ordre de fabrication mis à jour avec succès.',
    'success_delete' => 'Ordre de fabrication supprimé avec succès.',
    'confirm_submit' => 'Êtes-vous sûr de vouloir soumettre cet ordre de fabrication ?',
    'confirm_delete' => 'Êtes-vous sûr de vouloir supprimer cet ordre de fabrication ?',
]; 