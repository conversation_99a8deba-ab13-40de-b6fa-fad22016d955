<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use App\Models\Language;

class LanguageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $now = Carbon::now();

        //commented this syntax because getting error
        //DB::statement('ALTER TABLE languages AUTO_INCREMENT = 1');

        $languages = [
            [
                'code' => 'en',
                'name' => 'English',
                'direction' => 'ltr',
                'emoji' => 'flag-icon-us',
                'status' => 1
            ],
            [
                'code' => 'fr',
                'name' => 'Français',
                'direction' => 'ltr',
                'emoji' => 'flag-icon-fr',
                'status' => 1
            ]
        ];

        foreach ($languages as $language) {
            Language::firstOrCreate(
                ['code' => $language['code']],
                $language
            );
        }
    }
}
