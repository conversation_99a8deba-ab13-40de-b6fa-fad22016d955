<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('parties', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('prefix_code')->nullable();
            $table->string('count_id')->nullable();
            $table->string('party_code')->nullable();
            $table->string('party_type')->nullable();
            $table->boolean('is_wholesale_customer')->default(false);
            $table->boolean('default_party')->default(false);
            $table->string('first_name')->nullable();
            $table->string('last_name')->nullable();
            $table->string('email')->nullable();
            $table->string('mobile')->nullable();
            $table->string('phone')->nullable();
            $table->string('whatsapp')->nullable();
            $table->text('billing_address')->nullable();
            $table->text('shipping_address')->nullable();
            $table->unsignedBigInteger('currency_id')->nullable()->index('parties_currency_id_foreign');
            $table->decimal('exchange_rate', 20, 4)->default(0);
            $table->string('tax_number')->nullable();
            $table->string('tax_type')->nullable();
            $table->unsignedBigInteger('state_id')->nullable()->index('parties_state_id_foreign');
            $table->decimal('to_pay', 20, 4)->default(0);
            $table->decimal('to_receive', 20, 4)->default(0);
            $table->boolean('is_set_credit_limit')->default(false);
            $table->decimal('credit_limit', 20, 4)->default(0);
            $table->unsignedBigInteger('created_by')->nullable()->index('parties_created_by_foreign');
            $table->unsignedBigInteger('updated_by')->nullable()->index('parties_updated_by_foreign');
            $table->boolean('status')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('parties');
    }
};
