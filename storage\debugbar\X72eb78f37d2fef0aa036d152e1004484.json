{"__meta": {"id": "X72eb78f37d2fef0aa036d152e1004484", "datetime": "2025-06-27 17:57:49", "utime": **********.36953, "method": "GET", "uri": "/manufacturing/orders/1/details", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.044731, "end": **********.369545, "duration": 0.3248140811920166, "duration_str": "325ms", "measures": [{"label": "Booting", "start": **********.044731, "relative_start": 0, "end": **********.29014, "relative_end": **********.29014, "duration": 0.2454090118408203, "duration_str": "245ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.29015, "relative_start": 0.24541902542114258, "end": **********.369547, "relative_end": 1.9073486328125e-06, "duration": 0.07939696311950684, "duration_str": "79.4ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 25329568, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET manufacturing/orders/{order}/details", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\ManufacturingOrderController@getDetails", "as": "manufacturing.manufacturing.order.details", "namespace": null, "prefix": "/manufacturing", "where": [], "file": "<a href=\"phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FHttp%2FControllers%2FManufacturingOrderController.php&line=214\" onclick=\"\">app/Http/Controllers/ManufacturingOrderController.php:214-217</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.020200000000000003, "accumulated_duration_str": "20.2ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 17, "namespace": "middleware", "name": "auth", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}], "start": **********.320111, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:168", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "deltapos", "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.326814, "duration": 0.018600000000000002, "duration_str": "18.6ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "deltapos", "start_percent": 0, "width_percent": 92.079}, {"sql": "select * from `manufacturing_orders` where `id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 959}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 41}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": "middleware", "name": "auth", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 57}], "start": **********.352585, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "ImplicitRouteBinding.php:61", "source": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php:61", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FRouting%2FImplicitRouteBinding.php&line=61", "ajax": false, "filename": "ImplicitRouteBinding.php", "line": "61"}, "connection": "deltapos", "start_percent": 92.079, "width_percent": 4.901}, {"sql": "select * from `users` where `users`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ManufacturingOrderController.php", "file": "D:\\www\\delta\\app\\Http\\Controllers\\ManufacturingOrderController.php", "line": 216}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.361336, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "ManufacturingOrderController.php:216", "source": "app/Http/Controllers/ManufacturingOrderController.php:216", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FHttp%2FControllers%2FManufacturingOrderController.php&line=216", "ajax": false, "filename": "ManufacturingOrderController.php", "line": "216"}, "connection": "deltapos", "start_percent": 96.98, "width_percent": 3.02}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\ManufacturingOrder": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FModels%2FManufacturingOrder.php&line=1", "ajax": false, "filename": "ManufacturingOrder.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "DbgoOTCuumHy04EHLVHFGwOg3Jxe3DqOD4DE9hb1", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/users/getimage\"\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1"}, "request": {"telescope": "<a href=\"http://127.0.0.1:8000/_debugbar/telescope/9f41a865-da49-408d-a5c7-476d99ebf329\" target=\"_blank\">View in Telescope</a>", "path_info": "/manufacturing/orders/1/details", "status_code": "<pre class=sf-dump id=sf-dump-74064296 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-74064296\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-916725055 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-916725055\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-531993212 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-531993212\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2108149255 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/manufacturing/order</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">en-GB,en;q=0.9,en-US;q=0.8,fr;q=0.7,ar;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1685 characters\">ph_phc_3ESMmY9SgqEAGBB6sMGK5ayYHkeUuknH2vP6FmWH9RA_posthog=%7B%22distinct_id%22%3A%220196b41d-0793-7e06-86ce-017691734ea0%22%2C%22%24sesid%22%3A%5B1746780823698%2C%220196b41d-078e-7e17-a700-24eed1211756%22%2C1746778523534%5D%7D; theme_mode=eyJpdiI6Ii9XM3FTcWRDTHBmaVg5M0k2cFJiMEE9PSIsInZhbHVlIjoiNlZBU0xFRmZDaEdXVE5JRVMyMFhQcG0waG1PUWdFSDdWb1pCMWx3R0JVank5WXhQMytscW4vTW4yMkZSWlhWNjVISlc3NjNtcGNMckVYK1JXb3FOU3c9PSIsIm1hYyI6ImU2MmRhOThiNGQyNDA3N2UwMGViMjRhMzRhOThkYmJkZDdjMmNjYjhjZDk2MjIxMWZlZjVlNjA4Y2ZkYTkzNGIiLCJ0YWciOiIifQ%3D%3D; language_data=eyJpdiI6InpaMG5kK3BkZ2c3NFZqKy9nVVFRd2c9PSIsInZhbHVlIjoicmw0UjBkdFZZclA3UlljTXVDSE9HWGN6S05VUHdZamlZb2hjajFlcm9uVU1LNFpsZlhQRW5wTTlPNlUrRUJ5VUMzOU5lSnJHY2JBZUIvNC95Ujl5bnFxTWpmVEt5aXY2dXJVemZlbG1LenFtdmZEKzE5RXhsc1hJUE1zVk0wNlJMTEhvTUJXLzVnWHN6d1FnVzh1WnVGUWFRN2xvUyt4aC9pRzVaWTlBMFN4NWhQN1ZSL3dGY0ljZzZOZ1UxbUN6IiwibWFjIjoiYWE2MTZlMmNhMGM3ZjMwNTRhOTZkZjc4ZTZlNzI3NjUzMjY3NDMyNmJlYmViMmY4NjFiZDExMTBjNjMzNWFjNiIsInRhZyI6IiJ9; XSRF-TOKEN=eyJpdiI6InRaTFAyU2tDRnVURE5VeWYxeXRZZEE9PSIsInZhbHVlIjoiYnZJTGwwdUQrK1A4VlZUSzR4T2xKQ3RZRkRWL3dBVFBLMTU3cTQ2NnEzK3A4ZW0zeDcrVVp0Nm1NelNGOVJINFk1YVk2QktNUjlNQzE5L1Z6VUx1TTY3cjdicEkrWnZuOEpIRERaSWlmYTBPQWRPTWdrdXVOUXpib2xyM0l4cXAiLCJtYWMiOiI5MWNmNDNiZjMxZmVkMDk3MjQ2MjQ0NWVkNzUwZjgxOWQ4MTMwOWMwZWU4ZDY2NjdhZTIyYWVlNTVkZDczZTljIiwidGFnIjoiIn0%3D; delta_session=eyJpdiI6InY0WUk4SzVUVzFCODJLQlM0d1NLYnc9PSIsInZhbHVlIjoiZnhoZkhsMmxCRHpvSTNGUkdXRWJYanlSNURsSlpkZ1hwL2NldmpmOVVoWWJnOXZuT1RBRmlITkFvUkRNQmVNbm5zYlQ3RXk1MmFhOHEzM2pFWHRpeFNVaGpmcjFQeFJUeGRmdlBwVEd5T3d4Y01rRmd6c0dKTldDaXBKSi83ZVEiLCJtYWMiOiI2OGVhYWMxM2U2ZjNiMWViNDAzMzEyMWZiNTNhMDRlNzA5MjllZmM5OTIyMGJiMzNjMWQ5NDZmOGVlOTE3Njg0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2108149255\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1203053436 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ph_phc_3ESMmY9SgqEAGBB6sMGK5ayYHkeUuknH2vP6FmWH9RA_posthog</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>theme_mode</span>\" => \"<span class=sf-dump-str title=\"11 characters\">light-theme</span>\"\n  \"<span class=sf-dump-key>language_data</span>\" => \"<span class=sf-dump-str title=\"94 characters\">{&quot;language_code&quot;:&quot;fr&quot;,&quot;language_flag&quot;:&quot;flag-icon-fr&quot;,&quot;direction&quot;:&quot;ltr&quot;,&quot;emoji&quot;:&quot;flag-icon-fr&quot;}</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DbgoOTCuumHy04EHLVHFGwOg3Jxe3DqOD4DE9hb1</span>\"\n  \"<span class=sf-dump-key>delta_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BzceRqun0Ae5OezZzHQypR8L1x1bHb6dsUfkY5Ah</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1203053436\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1013164340 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 16:57:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImJNK0tqWFNuaHlZQnJUTEp4WGR1SkE9PSIsInZhbHVlIjoidVlNemdPbHZ1eTJlVE82Q0paQTVzdnhveGQwVm5pQ1RUbjFlUG1JWTk0SS95bm5GM2FhUURHN1liSmhHVjEvQlpSd3o5TnlQZHdad0VmV0lLZ0xhWmtpZldZUXEyVGQ4dkw4eDc2eUpsTnN1ckh4UEd5c0xXZHFOZ1FtQWRDYy8iLCJtYWMiOiI4Yzg5MjQ2MWUyNmViOThhMjgwNzUyNGIyMTBiZWI2MGRhMWI5MWVlNDkzYzU5YzAxNjVlMjFkYWUzMTFhNzAxIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 18:57:49 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">delta_session=eyJpdiI6IjVPajlpZk1JdkNvUHpPS3ZCOGxjalE9PSIsInZhbHVlIjoiQ1JxYThNdzRyTVN4QXFtckF4ang0UjAxSW1wTTAwenRCZVlwcDlpenpJbG9wWEltdU5pVFVOTitpbndhQzNEcnYyamxmY2g1KzQ2Yk1uYUd6c1dtek9nQlZReFNlSUhPeFFKTURoL3AwYURUcHZXejBNZjAvalk5RlV6RFlETmkiLCJtYWMiOiJhZTdmNjlmMjBhOWEwMDc1ZmM2ODYwMDk4MTQ2ZDY0NjIzZGFkZTU0MTA1N2I2NTExODBlOGYzNzFhNTg1ZmIxIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 18:57:49 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImJNK0tqWFNuaHlZQnJUTEp4WGR1SkE9PSIsInZhbHVlIjoidVlNemdPbHZ1eTJlVE82Q0paQTVzdnhveGQwVm5pQ1RUbjFlUG1JWTk0SS95bm5GM2FhUURHN1liSmhHVjEvQlpSd3o5TnlQZHdad0VmV0lLZ0xhWmtpZldZUXEyVGQ4dkw4eDc2eUpsTnN1ckh4UEd5c0xXZHFOZ1FtQWRDYy8iLCJtYWMiOiI4Yzg5MjQ2MWUyNmViOThhMjgwNzUyNGIyMTBiZWI2MGRhMWI5MWVlNDkzYzU5YzAxNjVlMjFkYWUzMTFhNzAxIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 18:57:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">delta_session=eyJpdiI6IjVPajlpZk1JdkNvUHpPS3ZCOGxjalE9PSIsInZhbHVlIjoiQ1JxYThNdzRyTVN4QXFtckF4ang0UjAxSW1wTTAwenRCZVlwcDlpenpJbG9wWEltdU5pVFVOTitpbndhQzNEcnYyamxmY2g1KzQ2Yk1uYUd6c1dtek9nQlZReFNlSUhPeFFKTURoL3AwYURUcHZXejBNZjAvalk5RlV6RFlETmkiLCJtYWMiOiJhZTdmNjlmMjBhOWEwMDc1ZmM2ODYwMDk4MTQ2ZDY0NjIzZGFkZTU0MTA1N2I2NTExODBlOGYzNzFhNTg1ZmIxIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 18:57:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1013164340\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-673316176 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DbgoOTCuumHy04EHLVHFGwOg3Jxe3DqOD4DE9hb1</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/users/getimage</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-673316176\", {\"maxDepth\":0})</script>\n"}}