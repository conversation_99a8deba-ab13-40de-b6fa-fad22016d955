<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\AppSettings;

class AppSettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        AppSettings::firstOrCreate(
            ['id' => 1],
            [
                'application_name' => 'Webvue',
                'footer_text' => 'Copyright© Webvue 2025',
                'colored_logo' => '684aec0a5b455.png',
                'light_logo' => null,
                'active_sms_api' => null,
                'fevicon' => '684aebf2d04c9.png',
                'language_id' => 4
            ]
        );
    }
} 