
<?php $__env->startSection('title', __('manufacturing.manufacturing_calculation_rules')); ?>

<?php $__env->startSection('content'); ?>
<!--start page wrapper -->
<div class="page-wrapper">
    <div class="page-content">
        <?php if (isset($component)) { $__componentOriginal269900abaed345884ce342681cdc99f6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal269900abaed345884ce342681cdc99f6 = $attributes; } ?>
<?php $component = App\View\Components\Breadcrumb::resolve(['langArray' => [
            __('manufacturing.manufacturing'),
            __('manufacturing.calculation_rules'),
            __('manufacturing.list')
        ]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('breadcrumb'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\Breadcrumb::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal269900abaed345884ce342681cdc99f6)): ?>
<?php $attributes = $__attributesOriginal269900abaed345884ce342681cdc99f6; ?>
<?php unset($__attributesOriginal269900abaed345884ce342681cdc99f6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal269900abaed345884ce342681cdc99f6)): ?>
<?php $component = $__componentOriginal269900abaed345884ce342681cdc99f6; ?>
<?php unset($__componentOriginal269900abaed345884ce342681cdc99f6); ?>
<?php endif; ?>

        <div class="card">
            <div class="card-header px-4 py-3 d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><?php echo e(__('manufacturing.manufacturing_calculation_rules')); ?></h5>
                <a href="<?php echo e(route('manufacturing.manufacturing-calculations.create')); ?>" class="btn btn-primary">
                    <i class="bx bx-plus"></i> <?php echo e(__('manufacturing.add_new_rule')); ?>

                </a>
            </div>
            <div class="card-body p-4">
                <?php if(session('success')): ?>
                    <div class="alert alert-success">
                        <?php echo e(session('success')); ?>

                    </div>
                <?php endif; ?>

                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th><?php echo e(__('manufacturing.height_type')); ?></th>
                                <th><?php echo e(__('manufacturing.curtain_finish')); ?></th>
                                <th><?php echo e(__('manufacturing.manipulation')); ?></th>
                                <th><?php echo e(__('manufacturing.positioning_type')); ?></th>
                                <th><?php echo e(__('manufacturing.height_deduction')); ?></th>
                                <th><?php echo e(__('manufacturing.formula')); ?></th>
                                <th><?php echo e(__('manufacturing.agrafes_spacing')); ?></th>
                                <th><?php echo e(__('manufacturing.status')); ?></th>
                                <th><?php echo e(__('manufacturing.actions')); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $rules; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $rule): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td><?php echo e(ucfirst($rule->height_type)); ?></td>
                                    <td><?php echo e($rule->curtain_finish); ?></td>
                                    <td><?php echo e($rule->manipulation ?? 'N/A'); ?></td>
                                    <td><?php echo e($rule->positioning_type ? __('manufacturing.' . $rule->positioning_type) : 'N/A'); ?></td>
                                    <td><?php echo e($rule->height_deduction); ?> cm</td>
                                    <td><?php echo e($rule->formula_description); ?></td>
                                    <td><?php echo e($rule->agrafes_spacing ? $rule->agrafes_spacing . ' cm' : 'N/A'); ?></td>
                                    <td>
                                        <span class="badge bg-<?php echo e($rule->is_active ? 'success' : 'danger'); ?>">
                                            <?php echo e($rule->is_active ? __('manufacturing.active') : __('manufacturing.inactive')); ?>

                                        </span>
                                    </td>
                                    <td>
                                        <a href="<?php echo e(route('manufacturing.manufacturing-calculations.edit', $rule)); ?>"
                                           class="btn btn-sm btn-primary">
                                            <i class="bx bx-edit"></i> <?php echo e(__('manufacturing.edit')); ?>

                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?> 
<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\www\delta\resources\views/manufacturing_calculations/index.blade.php ENDPATH**/ ?>