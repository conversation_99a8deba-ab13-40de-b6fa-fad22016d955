<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('items', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('prefix_code')->nullable();
            $table->string('count_id');
            $table->string('item_code');
            $table->string('name');
            $table->text('description')->nullable();
            $table->text('hsn')->nullable();
            $table->text('sku')->nullable();
            $table->boolean('is_service')->default(false);
            $table->unsignedBigInteger('item_category_id')->nullable()->index('items_item_category_id_foreign');
            $table->unsignedBigInteger('brand_id')->nullable()->index('items_brand_id_foreign');
            $table->unsignedBigInteger('base_unit_id')->nullable()->index('items_base_unit_id_foreign');
            $table->unsignedBigInteger('secondary_unit_id')->nullable()->index('items_secondary_unit_id_foreign');
            $table->decimal('conversion_rate', 20, 4)->default(0);
            $table->decimal('sale_price', 20, 4)->default(0);
            $table->boolean('is_sale_price_with_tax');
            $table->decimal('sale_price_discount', 20, 4)->default(0);
            $table->string('sale_price_discount_type');
            $table->decimal('wholesale_price', 20, 4)->default(0);
            $table->boolean('is_wholesale_price_with_tax')->default(false);
            $table->decimal('purchase_price', 20, 4)->default(0);
            $table->boolean('is_purchase_price_with_tax');
            $table->decimal('mrp', 20, 4)->default(0);
            $table->unsignedBigInteger('tax_id')->nullable()->index('items_tax_id_foreign');
            $table->string('tracking_type');
            $table->string('item_location')->nullable();
            $table->decimal('min_stock', 20, 4)->default(0);
            $table->decimal('current_stock', 20, 4)->default(0);
            $table->string('image_path')->nullable();
            $table->boolean('status')->default(true);
            $table->unsignedBigInteger('created_by')->nullable()->index('items_created_by_foreign');
            $table->unsignedBigInteger('updated_by')->nullable()->index('items_updated_by_foreign');
            $table->timestamps();
            $table->string('color')->nullable();
            $table->string('material')->nullable();
            $table->string('size')->nullable();
            $table->string('frame_type')->nullable();
            $table->string('frame_shape')->nullable();
            $table->string('frame_style')->nullable();
            $table->string('lens_type')->nullable();
            $table->string('lens_material')->nullable();
            $table->string('lens_coating')->nullable();
            $table->boolean('is_photochromic')->default(false);
            $table->boolean('is_polarized')->default(false);
            $table->string('contact_lens_type')->nullable();
            $table->string('contact_lens_material')->nullable();
            $table->string('water_content')->nullable();
            $table->integer('pack_quantity')->nullable();
            $table->string('lens_code')->nullable();
            $table->string('lens_label')->nullable();
            $table->string('lens_diameter')->nullable();
            $table->string('lens_geo')->nullable();
            $table->string('lens_index')->nullable();
            $table->decimal('purchase_price_ht', 15)->nullable();
            $table->decimal('selling_price_ht', 15)->nullable();
            $table->string('sphere_min')->nullable();
            $table->string('sphere_max')->nullable();
            $table->string('cylinder_min')->nullable();
            $table->string('cylinder_max')->nullable();
            $table->string('addition_min')->nullable();
            $table->string('addition_max')->nullable();
            $table->string('lens_material_type')->nullable();
            $table->string('lens_photo')->nullable();
            $table->string('lens_supplier')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('items');
    }
};
