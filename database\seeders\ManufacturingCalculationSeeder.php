<?php

namespace Database\Seeders;

use App\Models\ManufacturingCalculation;
use Illuminate\Database\Seeder;

class ManufacturingCalculationSeeder extends Seeder
{
    public function run()
    {
        $rules = [
            // Simple height rules
            [
                'height_type' => 'simple',
                'curtain_finish' => 'PP',
                'manipulation' => 'Moteur',
                'height_deduction' => 2,
                'formula_description' => 'Hauteur - 2 cm',
                'agrafes_spacing' => null,
            ],
            [
                'height_type' => 'simple',
                'curtain_finish' => 'PP',
                'manipulation' => 'Manuelle',
                'height_deduction' => 2,
                'formula_description' => 'Hauteur - 2 cm',
                'agrafes_spacing' => null,
            ],
            [
                'height_type' => 'simple',
                'curtain_finish' => 'PF',
                'manipulation' => 'Moteur',
                'height_deduction' => 3,
                'formula_description' => 'Hauteur - 3 cm',
                'agrafes_spacing' => null,
            ],
            [
                'height_type' => 'simple',
                'curtain_finish' => 'PF',
                'manipulation' => 'Manuelle',
                'height_deduction' => 3,
                'formula_description' => 'Hauteur - 3 cm',
                'agrafes_spacing' => null,
            ],
            [
                'height_type' => 'simple',
                'curtain_finish' => 'Wave',
                'manipulation' => 'Moteur',
                'height_deduction' => 4,
                'formula_description' => 'Hauteur - 4 cm',
                'agrafes_spacing' => 10,
            ],
            [
                'height_type' => 'simple',
                'curtain_finish' => 'Wave',
                'manipulation' => 'Manuelle',
                'height_deduction' => 1,
                'formula_description' => 'Hauteur - 1 cm',
                'agrafes_spacing' => 10,
            ],

            // Double height rules
            [
                'height_type' => 'double',
                'curtain_finish' => 'PP',
                'manipulation' => null,
                'height_deduction' => 3,
                'formula_description' => 'Hauteur - 3 cm (Double)',
                'agrafes_spacing' => null,
            ],
            [
                'height_type' => 'double',
                'curtain_finish' => 'PF',
                'manipulation' => null,
                'height_deduction' => 2.5,
                'formula_description' => 'Hauteur - 2,5 cm (Double)',
                'agrafes_spacing' => null,
            ],
            [
                'height_type' => 'double',
                'curtain_finish' => 'Wave',
                'manipulation' => null,
                'height_deduction' => 6,
                'formula_description' => 'Hauteur - 6 cm (Double)',
                'agrafes_spacing' => 8,
            ],
        ];

        foreach ($rules as $rule) {
            ManufacturingCalculation::create($rule);
        }
    }
} 