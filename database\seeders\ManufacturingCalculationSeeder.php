<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\ManufacturingCalculation;

class ManufacturingCalculationSeeder extends Seeder
{
    public function run()
    {
        $rules = [
            // Simple Height - PP
            [
                'height_type' => 'simple',
                'curtain_finish' => 'PP',
                'manipulation' => 'Moteur',
                'height_deduction' => 2,
                'formula_description' => 'hauteur_finale = sous_plafond - 2',
                'is_active' => true
            ],
            [
                'height_type' => 'simple',
                'curtain_finish' => 'PP',
                'manipulation' => 'Manuelle',
                'height_deduction' => 2,
                'formula_description' => 'hauteur_finale = sous_plafond - 2',
                'is_active' => true
            ],
            
            // Simple Height - PF
            [
                'height_type' => 'simple',
                'curtain_finish' => 'PF',
                'manipulation' => 'Moteur',
                'height_deduction' => 3,
                'formula_description' => 'hauteur_finale = sous_plafond - 3',
                'is_active' => true
            ],
            [
                'height_type' => 'simple',
                'curtain_finish' => 'PF',
                'manipulation' => 'Manuelle',
                'height_deduction' => 3,
                'formula_description' => 'hauteur_finale = sous_plafond - 3',
                'is_active' => true
            ],
            
            // Simple Height - Wave
            [
                'height_type' => 'simple',
                'curtain_finish' => 'Wave',
                'manipulation' => 'Moteur',
                'height_deduction' => 4,
                'formula_description' => 'hauteur_finale = sous_plafond - 4',
                'agrafes_spacing' => 10,
                'is_active' => true
            ],
            [
                'height_type' => 'simple',
                'curtain_finish' => 'Wave',
                'manipulation' => 'Manuelle',
                'height_deduction' => 1,
                'formula_description' => 'hauteur_finale = sous_plafond - 1',
                'agrafes_spacing' => 10,
                'is_active' => true
            ],
            
            // Double Height
            [
                'height_type' => 'double',
                'curtain_finish' => 'PP',
                'manipulation' => null,
                'height_deduction' => 3,
                'formula_description' => 'hauteur_finale = sous_plafond - 3',
                'is_active' => true
            ],
            [
                'height_type' => 'double',
                'curtain_finish' => 'PF',
                'manipulation' => null,
                'height_deduction' => 2.5,
                'formula_description' => 'hauteur_finale = sous_plafond - 2.5',
                'is_active' => true
            ],
            [
                'height_type' => 'double',
                'curtain_finish' => 'Wave',
                'manipulation' => null,
                'height_deduction' => 6,
                'formula_description' => 'hauteur_finale = sous_plafond - 6',
                'agrafes_spacing' => 8,
                'is_active' => true
            ],
        ];

        foreach ($rules as $rule) {
            ManufacturingCalculation::create($rule);
        }
    }
} 