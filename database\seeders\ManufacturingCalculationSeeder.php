<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\ManufacturingCalculation;

class ManufacturingCalculationSeeder extends Seeder
{
    public function run()
    {
        // Clear existing rules first
        ManufacturingCalculation::truncate();

        $rules = [
            // Simple Height - PP - Lateral
            [
                'height_type' => 'simple',
                'curtain_finish' => 'PP',
                'manipulation' => 'Moteur',
                'positioning_type' => 'lateral',
                'height_deduction' => 2,
                'formula_description' => 'hauteur_finale = sous_plafond - 2 (PP/Moteur/Latéral)',
                'is_active' => true
            ],
            [
                'height_type' => 'simple',
                'curtain_finish' => 'PP',
                'manipulation' => 'Moteur',
                'positioning_type' => 'central',
                'height_deduction' => 3,
                'formula_description' => 'hauteur_finale = sous_plafond - 3 (PP/Moteur/Central)',
                'is_active' => true
            ],
            [
                'height_type' => 'simple',
                'curtain_finish' => 'PP',
                'manipulation' => 'Manuelle',
                'positioning_type' => 'lateral',
                'height_deduction' => 2,
                'formula_description' => 'hauteur_finale = sous_plafond - 2 (PP/Manuelle/Latéral)',
                'is_active' => true
            ],
            [
                'height_type' => 'simple',
                'curtain_finish' => 'PP',
                'manipulation' => 'Manuelle',
                'positioning_type' => 'central',
                'height_deduction' => 2.5,
                'formula_description' => 'hauteur_finale = sous_plafond - 2.5 (PP/Manuelle/Central)',
                'is_active' => true
            ],

            // Simple Height - PF
            [
                'height_type' => 'simple',
                'curtain_finish' => 'PF',
                'manipulation' => 'Moteur',
                'positioning_type' => 'lateral',
                'height_deduction' => 3,
                'formula_description' => 'hauteur_finale = sous_plafond - 3 (PF/Moteur/Latéral)',
                'is_active' => true
            ],
            [
                'height_type' => 'simple',
                'curtain_finish' => 'PF',
                'manipulation' => 'Moteur',
                'positioning_type' => 'central',
                'height_deduction' => 4,
                'formula_description' => 'hauteur_finale = sous_plafond - 4 (PF/Moteur/Central)',
                'is_active' => true
            ],
            [
                'height_type' => 'simple',
                'curtain_finish' => 'PF',
                'manipulation' => 'Manuelle',
                'positioning_type' => 'lateral',
                'height_deduction' => 3,
                'formula_description' => 'hauteur_finale = sous_plafond - 3 (PF/Manuelle/Latéral)',
                'is_active' => true
            ],
            [
                'height_type' => 'simple',
                'curtain_finish' => 'PF',
                'manipulation' => 'Manuelle',
                'positioning_type' => 'central',
                'height_deduction' => 3.5,
                'formula_description' => 'hauteur_finale = sous_plafond - 3.5 (PF/Manuelle/Central)',
                'is_active' => true
            ],

            // Simple Height - Wave
            [
                'height_type' => 'simple',
                'curtain_finish' => 'Wave',
                'manipulation' => 'Moteur',
                'positioning_type' => 'lateral',
                'height_deduction' => 4,
                'formula_description' => 'hauteur_finale = sous_plafond - 4 (Wave/Moteur/Latéral)',
                'agrafes_spacing' => 10,
                'is_active' => true
            ],
            [
                'height_type' => 'simple',
                'curtain_finish' => 'Wave',
                'manipulation' => 'Moteur',
                'positioning_type' => 'central',
                'height_deduction' => 5,
                'formula_description' => 'hauteur_finale = sous_plafond - 5 (Wave/Moteur/Central)',
                'agrafes_spacing' => 8,
                'is_active' => true
            ],
            [
                'height_type' => 'simple',
                'curtain_finish' => 'Wave',
                'manipulation' => 'Manuelle',
                'positioning_type' => 'lateral',
                'height_deduction' => 1,
                'formula_description' => 'hauteur_finale = sous_plafond - 1 (Wave/Manuelle/Latéral)',
                'agrafes_spacing' => 10,
                'is_active' => true
            ],
            [
                'height_type' => 'simple',
                'curtain_finish' => 'Wave',
                'manipulation' => 'Manuelle',
                'positioning_type' => 'central',
                'height_deduction' => 2,
                'formula_description' => 'hauteur_finale = sous_plafond - 2 (Wave/Manuelle/Central)',
                'agrafes_spacing' => 8,
                'is_active' => true
            ],

            // Double Height
            [
                'height_type' => 'double',
                'curtain_finish' => 'PP',
                'manipulation' => null,
                'positioning_type' => 'lateral',
                'height_deduction' => 3,
                'formula_description' => 'hauteur_finale = sous_plafond - 3 (Double/PP/Latéral)',
                'is_active' => true
            ],
            [
                'height_type' => 'double',
                'curtain_finish' => 'PP',
                'manipulation' => null,
                'positioning_type' => 'central',
                'height_deduction' => 4,
                'formula_description' => 'hauteur_finale = sous_plafond - 4 (Double/PP/Central)',
                'is_active' => true
            ],
            [
                'height_type' => 'double',
                'curtain_finish' => 'PF',
                'manipulation' => null,
                'positioning_type' => 'lateral',
                'height_deduction' => 2.5,
                'formula_description' => 'hauteur_finale = sous_plafond - 2.5 (Double/PF/Latéral)',
                'is_active' => true
            ],
            [
                'height_type' => 'double',
                'curtain_finish' => 'PF',
                'manipulation' => null,
                'positioning_type' => 'central',
                'height_deduction' => 3.5,
                'formula_description' => 'hauteur_finale = sous_plafond - 3.5 (Double/PF/Central)',
                'is_active' => true
            ],
            [
                'height_type' => 'double',
                'curtain_finish' => 'Wave',
                'manipulation' => null,
                'positioning_type' => 'lateral',
                'height_deduction' => 6,
                'formula_description' => 'hauteur_finale = sous_plafond - 6 (Double/Wave/Latéral)',
                'agrafes_spacing' => 8,
                'is_active' => true
            ],
            [
                'height_type' => 'double',
                'curtain_finish' => 'Wave',
                'manipulation' => null,
                'positioning_type' => 'central',
                'height_deduction' => 7,
                'formula_description' => 'hauteur_finale = sous_plafond - 7 (Double/Wave/Central)',
                'agrafes_spacing' => 6,
                'is_active' => true
            ],
        ];

        foreach ($rules as $rule) {
            ManufacturingCalculation::create($rule);
        }
    }
} 