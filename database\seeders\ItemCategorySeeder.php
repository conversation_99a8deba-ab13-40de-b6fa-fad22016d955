<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Items\ItemCategory;

class ItemCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'General',
                'description' => null,
                'status' => 1,
                'is_deletable' => 0
            ],
            [
                'name' => 'CADRE',
                'description' => null,
                'status' => 1,
                'is_deletable' => 1
            ],
            [
                'name' => 'Verre',
                'description' => null,
                'status' => 1,
                'is_deletable' => 1
            ]
        ];

        foreach ($categories as $category) {
            ItemCategory::firstOrCreate(
                ['name' => $category['name']],
                $category
            );
        }
    }
}
