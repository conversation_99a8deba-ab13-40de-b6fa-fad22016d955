<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up()
    {
        // Drop the table if it exists
        Schema::dropIfExists('manufacturing_calculations');

        Schema::create('manufacturing_calculations', function (Blueprint $table) {
            $table->id();
            $table->enum('height_type', ['simple', 'double']);
            $table->enum('curtain_finish', ['Wave', 'PP', 'PF']);
            $table->enum('manipulation', ['Moteur', 'Manuelle'])->nullable();
            $table->decimal('height_deduction', 8, 2);
            $table->string('formula_description');
            $table->integer('agrafes_spacing')->nullable(); // Space between agrafes in cm
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            // Use a shorter name for the unique constraint
            $table->unique(['height_type', 'curtain_finish', 'manipulation'], 'manuf_calc_unique');
        });
    }

    public function down()
    {
        Schema::dropIfExists('manufacturing_calculations');
    }
}; 