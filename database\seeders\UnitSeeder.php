<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Unit;

class UnitSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $units = [
            [
                'name' => 'None',
                'short_code' => 'None',
                'status' => 1,
                'is_deletable' => 0,
            ],
            [
                'name' => 'Box',
                'short_code' => 'Box',
                'status' => 1,
                'is_deletable' => 1,
            ],
            [
                'name' => 'Pieces',
                'short_code' => 'Pcs',
                'status' => 1,
                'is_deletable' => 1,
            ],
            [
                'name' => 'Bag',
                'short_code' => 'Bgs',
                'status' => 1,
                'is_deletable' => 1,
            ],
            [
                'name' => 'Bottles',
                'short_code' => 'Btl',
                'status' => 1,
                'is_deletable' => 1,
            ],
            [
                'name' => 'Kilogram',
                'short_code' => 'Kgs',
                'status' => 1,
                'is_deletable' => 1,
            ],
            [
                'name' => 'Grams',
                'short_code' => 'Gms',
                'status' => 1,
                'is_deletable' => 1,
            ],
        ];

        foreach ($units as $unit) {
            Unit::firstOrCreate(
                ['name' => $unit['name']],
                $unit
            );
        }
    }
}
