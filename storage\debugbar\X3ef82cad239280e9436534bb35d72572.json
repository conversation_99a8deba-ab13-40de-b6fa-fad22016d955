{"__meta": {"id": "X3ef82cad239280e9436534bb35d72572", "datetime": "2025-06-27 17:55:51", "utime": **********.139928, "method": "POST", "uri": "/manufacturing/order", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.648916, "end": **********.139971, "duration": 0.4910550117492676, "duration_str": "491ms", "measures": [{"label": "Booting", "start": **********.648916, "relative_start": 0, "end": **********.949172, "relative_end": **********.949172, "duration": 0.30025601387023926, "duration_str": "300ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.949189, "relative_start": 0.30027294158935547, "end": **********.139975, "relative_end": 4.0531158447265625e-06, "duration": 0.19078612327575684, "duration_str": "191ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 27627264, "peak_usage_str": "26MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST manufacturing/order", "middleware": "web, auth, can:manufacturing.order.create", "as": "manufacturing.order.store", "controller": "App\\Http\\Controllers\\ManufacturingOrderController@store", "namespace": null, "prefix": "/manufacturing", "where": [], "file": "<a href=\"phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FHttp%2FControllers%2FManufacturingOrderController.php&line=47\" onclick=\"\">app/Http/Controllers/ManufacturingOrderController.php:47-84</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.04021, "accumulated_duration_str": "40.21ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 17, "namespace": "middleware", "name": "auth", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}], "start": **********.982664, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:168", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "deltapos", "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.992188, "duration": 0.02875, "duration_str": "28.75ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "deltapos", "start_percent": 0, "width_percent": 71.5}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 1 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["1", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\www\\delta\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\www\\delta\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 193}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\www\\delta\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 244}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\www\\delta\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 136}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.051493, "duration": 0.00266, "duration_str": "2.66ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:305", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:305", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=305", "ajax": false, "filename": "HasPermissions.php", "line": "305"}, "connection": "deltapos", "start_percent": 71.5, "width_percent": 6.615}, {"sql": "select count(*) as aggregate from `manufacturing_orders` where `order_number` = 'MO00001'", "type": "query", "params": [], "bindings": ["MO00001"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 948}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 658}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 457}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 492}], "start": **********.094176, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "deltapos", "start_percent": 78.115, "width_percent": 2.562}, {"sql": "insert into `manufacturing_orders` (`order_number`, `order_date`, `delivery_date`, `designation`, `tissus`, `largeur_fini`, `mecanisme`, `qte_to_billed`, `billed_unity`, `unit_price`, `extras_fees`, `qty`, `order_status`, `fabric_requirement`, `curtain_type`, `curtain_finish`, `manipulation`, `height_type`, `positioning_type`, `sous_plafond`, `hauteur_finale`, `formule_appliquee`, `nombre_agrafes`, `notes`, `created_by`, `updated_at`, `created_at`) values ('MO00001', '2025-01-01 00:00:00', '2025-01-01 00:00:00', '1250', '21254', '220', 'BR', '30', 'ml', '20', '30', '10', 'Draft', '20', 'Rideaux', 'Wave', 'Moteur', 'simple', 'lateral', '360', '356.00', 'hauteur_finale = sous_plafond - 4 (Wave/Moteur/Latéral)', '36', '', 1, '2025-06-27 17:55:51', '2025-06-27 17:55:51')", "type": "query", "params": [], "bindings": ["MO00001", "2025-01-01 00:00:00", "2025-01-01 00:00:00", "1250", "21254", "220", "BR", "30", "ml", "20", "30", "10", "Draft", "20", "<PERSON><PERSON>", "Wave", "<PERSON><PERSON><PERSON>", "simple", "lateral", "360", "356.00", "hauteur_finale = sous_plafond - 4 (Wave/Moteur/Lat&eacute;ral)", "36", "", "1", "2025-06-27 17:55:51", "2025-06-27 17:55:51"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/ManufacturingOrderController.php", "file": "D:\\www\\delta\\app\\Http\\Controllers\\ManufacturingOrderController.php", "line": 79}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.1004739, "duration": 0.00777, "duration_str": "7.77ms", "memory": 0, "memory_str": null, "filename": "ManufacturingOrderController.php:79", "source": "app/Http/Controllers/ManufacturingOrderController.php:79", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FHttp%2FControllers%2FManufacturingOrderController.php&line=79", "ajax": false, "filename": "ManufacturingOrderController.php", "line": "79"}, "connection": "deltapos", "start_percent": 80.676, "width_percent": 19.324}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 167, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 168, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manufacturing.order.create,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1790691739 data-indent-pad=\"  \"><span class=sf-dump-note>manufacturing.order.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"26 characters\">manufacturing.order.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1790691739\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.080326, "xdebug_link": null}]}, "session": {"_token": "DbgoOTCuumHy04EHLVHFGwOg3Jxe3DqOD4DE9hb1", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/users/getimage\"\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "success": "Ordre de fabrication créé avec succès.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"telescope": "<a href=\"http://127.0.0.1:8000/_debugbar/telescope/9f41a7b1-7648-45c5-884a-b846e16fa9f4\" target=\"_blank\">View in Telescope</a>", "path_info": "/manufacturing/order", "status_code": "<pre class=sf-dump id=sf-dump-883667613 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-883667613\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1741104371 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1741104371\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-148659254 data-indent-pad=\"  \"><span class=sf-dump-note>array:29</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DbgoOTCuumHy04EHLVHFGwOg3Jxe3DqOD4DE9hb1</span>\"\n  \"<span class=sf-dump-key>curtain_type</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Rideaux</span>\"\n  \"<span class=sf-dump-key>row_count</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>operation</span>\" => \"<span class=sf-dump-str title=\"4 characters\">save</span>\"\n  \"<span class=sf-dump-key>order_number</span>\" => \"<span class=sf-dump-str title=\"7 characters\">MO00001</span>\"\n  \"<span class=sf-dump-key>order_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-01-01</span>\"\n  \"<span class=sf-dump-key>delivery_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-01-01</span>\"\n  \"<span class=sf-dump-key>designation</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1250</span>\"\n  \"<span class=sf-dump-key>tissus</span>\" => \"<span class=sf-dump-str title=\"5 characters\">21254</span>\"\n  \"<span class=sf-dump-key>mecanisme</span>\" => \"<span class=sf-dump-str title=\"2 characters\">BR</span>\"\n  \"<span class=sf-dump-key>order_status</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Draft</span>\"\n  \"<span class=sf-dump-key>largeur_fini</span>\" => \"<span class=sf-dump-str title=\"3 characters\">220</span>\"\n  \"<span class=sf-dump-key>sous_plafond</span>\" => \"<span class=sf-dump-str title=\"3 characters\">360</span>\"\n  \"<span class=sf-dump-key>curtain_finish</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Wave</span>\"\n  \"<span class=sf-dump-key>manipulation</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Moteur</span>\"\n  \"<span class=sf-dump-key>height_type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">simple</span>\"\n  \"<span class=sf-dump-key>positioning_type</span>\" => \"<span class=sf-dump-str title=\"7 characters\">lateral</span>\"\n  \"<span class=sf-dump-key>hauteur_finale</span>\" => \"<span class=sf-dump-str title=\"6 characters\">356.00</span>\"\n  \"<span class=sf-dump-key>formule_appliquee</span>\" => \"<span class=sf-dump-str title=\"55 characters\">hauteur_finale = sous_plafond - 4 (Wave/Moteur/Lat&#233;ral)</span>\"\n  \"<span class=sf-dump-key>nombre_agrafes</span>\" => \"<span class=sf-dump-str title=\"2 characters\">36</span>\"\n  \"<span class=sf-dump-key>nombre_agrafes_rule</span>\" => \"<span class=sf-dump-str title=\"46 characters\">Hauteur Finale (356.00) &#247; Espacement (10) = 36</span>\"\n  \"<span class=sf-dump-key>qte_to_billed</span>\" => \"<span class=sf-dump-str title=\"2 characters\">30</span>\"\n  \"<span class=sf-dump-key>billed_unity</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ml</span>\"\n  \"<span class=sf-dump-key>unit_price</span>\" => \"<span class=sf-dump-str title=\"2 characters\">20</span>\"\n  \"<span class=sf-dump-key>extras_fees</span>\" => \"<span class=sf-dump-str title=\"2 characters\">30</span>\"\n  \"<span class=sf-dump-key>fabric_requirement</span>\" => \"<span class=sf-dump-str title=\"2 characters\">20</span>\"\n  \"<span class=sf-dump-key>qty</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>total_amount</span>\" => \"<span class=sf-dump-str title=\"6 characters\">630.00</span>\"\n  \"<span class=sf-dump-key>notes</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-148659254\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2003661091 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">3154</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundary3dIgqbSD5gPT3iys</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">http://127.0.0.1:8000/manufacturing/order/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">en-GB,en;q=0.9,en-US;q=0.8,fr;q=0.7,ar;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1685 characters\">ph_phc_3ESMmY9SgqEAGBB6sMGK5ayYHkeUuknH2vP6FmWH9RA_posthog=%7B%22distinct_id%22%3A%220196b41d-0793-7e06-86ce-017691734ea0%22%2C%22%24sesid%22%3A%5B1746780823698%2C%220196b41d-078e-7e17-a700-24eed1211756%22%2C1746778523534%5D%7D; theme_mode=eyJpdiI6Ii9XM3FTcWRDTHBmaVg5M0k2cFJiMEE9PSIsInZhbHVlIjoiNlZBU0xFRmZDaEdXVE5JRVMyMFhQcG0waG1PUWdFSDdWb1pCMWx3R0JVank5WXhQMytscW4vTW4yMkZSWlhWNjVISlc3NjNtcGNMckVYK1JXb3FOU3c9PSIsIm1hYyI6ImU2MmRhOThiNGQyNDA3N2UwMGViMjRhMzRhOThkYmJkZDdjMmNjYjhjZDk2MjIxMWZlZjVlNjA4Y2ZkYTkzNGIiLCJ0YWciOiIifQ%3D%3D; language_data=eyJpdiI6InpaMG5kK3BkZ2c3NFZqKy9nVVFRd2c9PSIsInZhbHVlIjoicmw0UjBkdFZZclA3UlljTXVDSE9HWGN6S05VUHdZamlZb2hjajFlcm9uVU1LNFpsZlhQRW5wTTlPNlUrRUJ5VUMzOU5lSnJHY2JBZUIvNC95Ujl5bnFxTWpmVEt5aXY2dXJVemZlbG1LenFtdmZEKzE5RXhsc1hJUE1zVk0wNlJMTEhvTUJXLzVnWHN6d1FnVzh1WnVGUWFRN2xvUyt4aC9pRzVaWTlBMFN4NWhQN1ZSL3dGY0ljZzZOZ1UxbUN6IiwibWFjIjoiYWE2MTZlMmNhMGM3ZjMwNTRhOTZkZjc4ZTZlNzI3NjUzMjY3NDMyNmJlYmViMmY4NjFiZDExMTBjNjMzNWFjNiIsInRhZyI6IiJ9; XSRF-TOKEN=eyJpdiI6IkphUUNkNVB6cnQvRDNQaUhCVEFKaVE9PSIsInZhbHVlIjoiS2cyQVlQN3A1M2pnbERFZUN6YlY3NlQwdmxtU0NQdnRyMkJ6VVJ2RkM1TTVrZVpyNkk2RDdyajd4bDI2SkdZUFpoV0wrSS9zRDF5YXhTSUk5dk1FNlExbDNCM1UzaGZ4VTcwczNjWnpqR2hMeHdFZVAyeXNwYlo5NEZuSTBWZWoiLCJtYWMiOiI2ZjZlMGRmMzQ4YTEyYTNhMjdjZDU4NDgyMTQ0MmQ0Y2I2NGIxNThiNTc3OGZlZmQ0Yzg0OTllNmQ5YzM5NTdhIiwidGFnIjoiIn0%3D; delta_session=eyJpdiI6IlpzbHpBenhIQTZwbkxvSGs1SEoyeWc9PSIsInZhbHVlIjoiSlJkUnEySysvSEttWWtCRnVvZVBPM3llSWxad2svVzBlU3ZLWHBURDlEMjd0VjJ6dzgweXBIUlZNdDRGYTdpSFRPeWtVTkhhMkdSdTNnTmtHdWF5Q2V1NSs2Zm5IcFZDZXdJUTdGQ21reHVmcjNyN3V6aDJIZENxbmlodDQ3OFciLCJtYWMiOiJhZDRkZjFiMWIyOWM5N2Q1OGNmODE2MGM4MTNiZjUxZjM4ZmQ1NWM4NDZhMWJjZDkzZGE4MzQwMDE0ZDg5ZjVlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2003661091\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1752389613 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ph_phc_3ESMmY9SgqEAGBB6sMGK5ayYHkeUuknH2vP6FmWH9RA_posthog</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>theme_mode</span>\" => \"<span class=sf-dump-str title=\"11 characters\">light-theme</span>\"\n  \"<span class=sf-dump-key>language_data</span>\" => \"<span class=sf-dump-str title=\"94 characters\">{&quot;language_code&quot;:&quot;fr&quot;,&quot;language_flag&quot;:&quot;flag-icon-fr&quot;,&quot;direction&quot;:&quot;ltr&quot;,&quot;emoji&quot;:&quot;flag-icon-fr&quot;}</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DbgoOTCuumHy04EHLVHFGwOg3Jxe3DqOD4DE9hb1</span>\"\n  \"<span class=sf-dump-key>delta_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BzceRqun0Ae5OezZzHQypR8L1x1bHb6dsUfkY5Ah</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1752389613\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-331460092 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 16:55:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/manufacturing/order</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjA4Yi9pZWtWeWIzb21CTm9xckUzdHc9PSIsInZhbHVlIjoiTEZFTkpueE1HVDBhdGlEWWg3S2R4TTlUczhHOGJhWng4UXVySEVkSUJKeVAzOUxUZE9BM0dPZEZ3TGRhSzBwMkJTR3BlSnhPTzZBdHZjQXNIZ2hkeThQR1FaTVBWaEZONHAyak5MVGw2YTN1R1E3a0VDZHdLamVZWGI0ZWZlazUiLCJtYWMiOiJhNTJjODEwMzJiMzQ1Y2I1NWNjNjg1MzljYzU5YWJjNjI1NTE5MDEwNWU2ZWNhZDNhN2Y3ZGViNjgwNGMxZGE4IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 18:55:51 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">delta_session=eyJpdiI6Ik1qVnBjVjVrQmh6WGt0elRGeDFJZVE9PSIsInZhbHVlIjoiNDFpWUt1YUFzaktlMFJ2NCtXYmRXM0sxbWo5TkxHbnVWWS81WnQ5d3hodDNWczVDSTNQRWtEV2xGWmZyUS9oajFMOHJyYlJEZXZlQ0ZDajJ6TE95eitMRzdoZ2crMXROc1JUemNDYXgrNjZFb1dsMXM0TnUyQlNEbTdNWjNCS2giLCJtYWMiOiJlMWNjNDQ1OTMwM2VjMjEyOWM4MGI4YTg1MjI0MDQ3NDgwMjBlOGEyNjU2ZGYyZDkzYWFhN2VkNzhlYWZjYWVjIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 18:55:51 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjA4Yi9pZWtWeWIzb21CTm9xckUzdHc9PSIsInZhbHVlIjoiTEZFTkpueE1HVDBhdGlEWWg3S2R4TTlUczhHOGJhWng4UXVySEVkSUJKeVAzOUxUZE9BM0dPZEZ3TGRhSzBwMkJTR3BlSnhPTzZBdHZjQXNIZ2hkeThQR1FaTVBWaEZONHAyak5MVGw2YTN1R1E3a0VDZHdLamVZWGI0ZWZlazUiLCJtYWMiOiJhNTJjODEwMzJiMzQ1Y2I1NWNjNjg1MzljYzU5YWJjNjI1NTE5MDEwNWU2ZWNhZDNhN2Y3ZGViNjgwNGMxZGE4IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 18:55:51 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">delta_session=eyJpdiI6Ik1qVnBjVjVrQmh6WGt0elRGeDFJZVE9PSIsInZhbHVlIjoiNDFpWUt1YUFzaktlMFJ2NCtXYmRXM0sxbWo5TkxHbnVWWS81WnQ5d3hodDNWczVDSTNQRWtEV2xGWmZyUS9oajFMOHJyYlJEZXZlQ0ZDajJ6TE95eitMRzdoZ2crMXROc1JUemNDYXgrNjZFb1dsMXM0TnUyQlNEbTdNWjNCS2giLCJtYWMiOiJlMWNjNDQ1OTMwM2VjMjEyOWM4MGI4YTg1MjI0MDQ3NDgwMjBlOGEyNjU2ZGYyZDkzYWFhN2VkNzhlYWZjYWVjIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 18:55:51 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-331460092\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-422877788 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DbgoOTCuumHy04EHLVHFGwOg3Jxe3DqOD4DE9hb1</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/users/getimage</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Ordre de fabrication cr&#233;&#233; avec succ&#232;s.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-422877788\", {\"maxDepth\":0})</script>\n"}}