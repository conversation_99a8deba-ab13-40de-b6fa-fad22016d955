$(document).ready(function() {
    let storeStepper = null;
    let rideauxStepper = null;

    // Initialize steppers
    function initializeSteppers() {
        const storeStepperEl = document.querySelector('#store-form .bs-stepper');
        const rideauxStepperEl = document.querySelector('#rideaux-form .bs-stepper');

        if (storeStepperEl) {
            storeStepper = new Stepper(storeStepperEl, {
                linear: false,
                animation: false
            });
        }

        if (rideauxStepperEl) {
            rideauxStepper = new Stepper(rideauxStepperEl, {
                linear: false,
                animation: false
            });

            // Add event listener for step changes
            rideauxStepperEl.addEventListener('shown.bs-stepper', function (event) {
                // If moving to pricing step (step 4, index 3), trigger calculation
                if (event.detail.indexStep === 3) {
                    calculateHauteurFinale();
                }
            });
        }
    }

    // Initialize datepickers
    function initializeDatepickers() {
        $('.datepicker').flatpickr({
            dateFormat: "Y-m-d",
            allowInput: true
        });
    }

    // Handle form type selection
    $('#select_type').on('change', function() {
        const selectedType = $(this).val();
        
        // Hide both forms initially
        $('#store-form, #rideaux-form').addClass('d-none');
        
        if (selectedType === 'Store') {
            $('#store-form').removeClass('d-none');
            if (!storeStepper) {
                initializeSteppers();
            }
        } else if (selectedType === 'Rideaux') {
            $('#rideaux-form').removeClass('d-none');
            if (!rideauxStepper) {
                initializeSteppers();
            }
        }
    });

    // Store form navigation
    $('.btn-next-store').on('click', function() {
        if (storeStepper._currentIndex === 1) {
            // Update hauteur finale when moving from dimensions to pricing
            const sousPlafond = parseFloat($('#store-form #sous_plafond').val()) || 0;
            $('#store_hauteur_finale').val(sousPlafond.toFixed(2));
        }
        storeStepper.next();
    });

    $('.btn-previous-store').on('click', function() {
        storeStepper.previous();
    });

    // Rideaux form navigation
    $('.btn-next-rideaux').on('click', function() {
        if (rideauxStepper._currentIndex === 2) {
            // Update calculations when moving to pricing step
            calculateHauteurFinale();
        }
        rideauxStepper.next();
    });

    $('.btn-previous-rideaux').on('click', function() {
        rideauxStepper.previous();
    });

    // Store form calculations
    $('#store-form #sous_plafond').on('change', function() {
        const sousPlafond = parseFloat($(this).val()) || 0;
        $('#store_hauteur_finale').val(sousPlafond.toFixed(2));
    });

    $('#store-form #store_qte_to_billed, #store-form #store_unit_price, #store-form #store_extras_fees').on('input', function() {
        calculateTotalAmount('store');
    });

    // Rideaux form calculations
    $('#rideaux-form #sous_plafond, #rideaux-form select[name="height_type"], #rideaux-form select[name="curtain_finish"], #rideaux-form select[name="manipulation"], #rideaux-form select[name="positioning_type"]').on('change', function() {
        calculateHauteurFinale();
    });

    $('#rideaux-form #rideaux_qte_to_billed, #rideaux-form #rideaux_unit_price, #rideaux-form #rideaux_extras_fees').on('input', function() {
        calculateTotalAmount('rideaux');
    });

    function calculateHauteurFinale() {
        var heightType = $('#rideaux_height_type').val();
        var sousPlafond = parseFloat($('#rideaux-form input[name="sous_plafond"]').val()) || 0;
        var curtainFinish = $('#rideaux-form select[name="curtain_finish"]').val();
        var manipulation = $('#rideaux-form select[name="manipulation"]').val();
        var positioningType = $('#rideaux-form select[name="positioning_type"]').val();
        var baseUrl = $('#rideaux_base_url').val();
        
        console.log('Debug values:', {
            heightType: heightType,
            sousPlafond: sousPlafond,
            curtainFinish: curtainFinish,
            manipulation: manipulation,
            positioningType: positioningType,
            baseUrl: baseUrl
        });
        
        if (!heightType || !sousPlafond || !curtainFinish || !manipulation) {
            console.log('Missing fields:', {
                heightType: !heightType,
                sousPlafond: !sousPlafond,
                curtainFinish: !curtainFinish,
                manipulation: !manipulation
            });
            console.log('Missing required fields for calculation');
            return;
        }

        $.ajax({
            url: baseUrl + '/manufacturing/calculations/get-rule',
            type: 'POST',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            data: {
                height_type: heightType,
                curtain_finish: curtainFinish,
                manipulation: manipulation,
                positioning_type: positioningType
            },
            success: function(response) {
                console.log('Response:', response);
                if (response.rule) {
                    console.log('Calculation rule found:', response.rule);
                    var hauteurFinale = sousPlafond - response.rule.height_deduction;

                    // Debug: Check if elements exist
                    console.log('Elements check:', {
                        hauteur_finale_exists: $('#rideaux_hauteur_finale').length > 0,
                        formule_appliquee_exists: $('#rideaux_formule_appliquee').length > 0,
                        nombre_agrafes_exists: $('#rideaux_nombre_agrafes').length > 0
                    });

                    $('#rideaux_hauteur_finale').val(hauteurFinale.toFixed(2));
                    $('#rideaux_formule_appliquee').val(response.rule.formula_description);

                    if (response.rule.agrafes_spacing && curtainFinish === 'Wave') {
                        var nombreAgrafes = Math.ceil(hauteurFinale / response.rule.agrafes_spacing);
                        $('#rideaux_nombre_agrafes').val(nombreAgrafes);
                        $('#rideaux_nombre_agrafes_rule').val(`Hauteur Finale (${hauteurFinale.toFixed(2)}) ÷ Espacement (${response.rule.agrafes_spacing}) = ${nombreAgrafes}`);
                        $('#nombre_agrafes_group').removeClass('d-none');
                        $('#nombre_agrafes_rule_group').removeClass('d-none');
                    } else {
                        $('#rideaux_nombre_agrafes').val('');
                        $('#rideaux_nombre_agrafes_rule').val('');
                        $('#nombre_agrafes_group').addClass('d-none');
                        $('#nombre_agrafes_rule_group').addClass('d-none');
                    }

                    console.log('Values set:', {
                        hauteur_finale: $('#rideaux_hauteur_finale').val(),
                        formule_appliquee: $('#rideaux_formule_appliquee').val(),
                        nombre_agrafes: $('#rideaux_nombre_agrafes').val()
                    });
                } else {
                    console.log('No calculation rule found');
                    // Clear fields if no rule is found
                    $('#rideaux_hauteur_finale').val('');
                    $('#rideaux_formule_appliquee').val('');
                    $('#rideaux_nombre_agrafes').val('');
                }
            },
            error: function(xhr, status, error) {
                console.error('Error getting calculation rule:', {
                    status: status,
                    error: error,
                    response: xhr.responseText,
                    url: baseUrl + '/manufacturing/calculations/get-rule'
                });
                // Show error message
                Swal.fire({
                    title: 'Error!',
                    text: 'Failed to get calculation rule. Please try again.',
                    icon: 'error'
                });
            }
        });
    }

    function calculateTotalAmount(formType) {
        const prefix = formType === 'store' ? 'store_' : 'rideaux_';
        const qteToBeilled = parseFloat($(`#${prefix}qte_to_billed`).val()) || 0;
        const unitPrice = parseFloat($(`#${prefix}unit_price`).val()) || 0;
        const extrasFees = parseFloat($(`#${prefix}extras_fees`).val()) || 0;
        
        const totalAmount = (qteToBeilled * unitPrice) + extrasFees;
        $(`#${prefix}total_amount`).val(totalAmount.toFixed(2));
    }

    // Form submission
    $('#submit_store_form').on('click', function(e) {
        e.preventDefault();
        if (validateForm('store')) {
            submitForm('store');
        }
    });

    $('#submit_rideaux_form').on('click', function(e) {
        e.preventDefault();
        if (validateForm('rideaux')) {
            submitForm('rideaux');
        }
    });

    function validateForm(formType) {
        const form = formType === 'store' ? '#storeOrderForm' : '#rideauxOrderForm';
        let isValid = true;
        let errors = [];

        // Add your validation logic here
        $(`${form} [required]`).each(function() {
            if (!$(this).val()) {
                isValid = false;
                errors.push(`${$(this).prev('label').text()} is required`);
            }
        });

        if (!isValid) {
            Swal.fire({
                title: 'Validation Error',
                html: errors.join('<br>'),
                icon: 'error'
            });
        }

        return isValid;
    }

    function submitForm(formType) {
        const form = formType === 'store' ? '#storeOrderForm' : '#rideauxOrderForm';
        const baseUrlId = formType === 'store' ? '#store_base_url' : '#rideaux_base_url';

        // Generate order number if not already set
        if (!$(`${form} input[name="order_number"]`).val()) {
            const prefixCode = $('#prefix_code').val() || 'MO';
            const countId = $('#count_id').val();
            const orderNumber = prefixCode + countId;
            $(`${form} input[name="order_number"]`).val(orderNumber);
        }

        Swal.fire({
            title: 'Confirm Submission',
            text: 'Are you sure you want to submit this manufacturing order?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Yes, submit',
            cancelButtonText: 'No, cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                const formData = new FormData($(form)[0]);
                
                $.ajax({
                    url: $(form).attr('action'),
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.success) {
                            Swal.fire({
                                title: 'Success!',
                                text: response.message || 'Manufacturing order created successfully.',
                                icon: 'success'
                            }).then(() => {
                                window.location.href = $(baseUrlId).val() + '/manufacturing/orders';
                            });
                        } else {
                            Swal.fire({
                                title: 'Error!',
                                text: response.message || 'Something went wrong.',
                                icon: 'error'
                            });
                        }
                    },
                    error: function(xhr) {
                        console.log('AJAX Error:', xhr);
                        console.log('Status:', xhr.status);
                        console.log('Response Text:', xhr.responseText);
                        console.log('Response JSON:', xhr.responseJSON);

                        let errorMessage = 'Something went wrong.';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage = xhr.responseJSON.message;
                        } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                            errorMessage = Object.values(xhr.responseJSON.errors).flat().join('\n');
                        } else if (xhr.status === 500) {
                            errorMessage = 'Server error (500). Please check the server logs.';
                        } else if (xhr.status === 422) {
                            errorMessage = 'Validation error. Please check all required fields.';
                        }

                        Swal.fire({
                            title: 'Error!',
                            text: errorMessage,
                            icon: 'error'
                        });
                    }
                });
            }
        });
    }

    // Initialize components
    initializeDatepickers();
}); 