<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('items', function (Blueprint $table) {
            $table->foreign(['base_unit_id'])->references(['id'])->on('units')->onUpdate('no action')->onDelete('no action');
            $table->foreign(['brand_id'])->references(['id'])->on('brands')->onUpdate('no action')->onDelete('no action');
            $table->foreign(['created_by'])->references(['id'])->on('users')->onUpdate('no action')->onDelete('no action');
            $table->foreign(['item_category_id'])->references(['id'])->on('item_categories')->onUpdate('no action')->onDelete('no action');
            $table->foreign(['secondary_unit_id'])->references(['id'])->on('units')->onUpdate('no action')->onDelete('no action');
            $table->foreign(['tax_id'])->references(['id'])->on('taxes')->onUpdate('no action')->onDelete('no action');
            $table->foreign(['updated_by'])->references(['id'])->on('users')->onUpdate('no action')->onDelete('no action');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('items', function (Blueprint $table) {
            $table->dropForeign('items_base_unit_id_foreign');
            $table->dropForeign('items_brand_id_foreign');
            $table->dropForeign('items_created_by_foreign');
            $table->dropForeign('items_item_category_id_foreign');
            $table->dropForeign('items_secondary_unit_id_foreign');
            $table->dropForeign('items_tax_id_foreign');
            $table->dropForeign('items_updated_by_foreign');
        });
    }
};
