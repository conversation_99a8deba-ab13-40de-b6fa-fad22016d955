{"__meta": {"id": "X5a49e0e7c0569a91cbc3afe81d088163", "datetime": "2025-06-27 16:14:34", "utime": **********.697077, "method": "GET", "uri": "/manufacturing/order/create", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 4, "messages": [{"message": "[16:14:34] LOG.warning: str_contains(): Passing null to parameter #1 ($haystack) of type string is deprecated in D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 268", "message_html": null, "is_string": false, "label": "warning", "time": **********.305425, "xdebug_link": null, "collector": "log"}, {"message": "[16:14:34] LOG.warning: str_contains(): Passing null to parameter #1 ($haystack) of type string is deprecated in D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 268", "message_html": null, "is_string": false, "label": "warning", "time": **********.306361, "xdebug_link": null, "collector": "log"}, {"message": "[16:14:34] LOG.warning: str_contains(): Passing null to parameter #1 ($haystack) of type string is deprecated in D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 268", "message_html": null, "is_string": false, "label": "warning", "time": **********.306546, "xdebug_link": null, "collector": "log"}, {"message": "[16:14:34] LOG.warning: str_contains(): Passing null to parameter #1 ($haystack) of type string is deprecated in D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 268", "message_html": null, "is_string": false, "label": "warning", "time": **********.306655, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1751037273.829938, "end": **********.697128, "duration": 0.8671901226043701, "duration_str": "867ms", "measures": [{"label": "Booting", "start": 1751037273.829938, "relative_start": 0, "end": **********.190334, "relative_end": **********.190334, "duration": 0.360396146774292, "duration_str": "360ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.190376, "relative_start": 0.36043810844421387, "end": **********.697133, "relative_end": 5.0067901611328125e-06, "duration": 0.5067570209503174, "duration_str": "507ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 27689304, "peak_usage_str": "26MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 98, "templates": [{"name": "1x manufacturing_orders.create", "param_count": null, "params": [], "start": **********.331594, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/manufacturing_orders/create.blade.phpmanufacturing_orders.create", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fmanufacturing_orders%2Fcreate.blade.php&line=1", "ajax": false, "filename": "create.blade.php", "line": "?"}, "render_count": 1, "name_original": "manufacturing_orders.create"}, {"name": "1x components.breadcrumb", "param_count": null, "params": [], "start": **********.340071, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/components/breadcrumb.blade.phpcomponents.breadcrumb", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fcomponents%2Fbreadcrumb.blade.php&line=1", "ajax": false, "filename": "breadcrumb.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.breadcrumb"}, {"name": "43x components.label", "param_count": null, "params": [], "start": **********.34462, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/components/label.blade.phpcomponents.label", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fcomponents%2Flabel.blade.php&line=1", "ajax": false, "filename": "label.blade.php", "line": "?"}, "render_count": 43, "name_original": "components.label"}, {"name": "1x manufacturing_orders._store_basic_info", "param_count": null, "params": [], "start": **********.346066, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/manufacturing_orders/_store_basic_info.blade.phpmanufacturing_orders._store_basic_info", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fmanufacturing_orders%2F_store_basic_info.blade.php&line=1", "ajax": false, "filename": "_store_basic_info.blade.php", "line": "?"}, "render_count": 1, "name_original": "manufacturing_orders._store_basic_info"}, {"name": "33x components.input", "param_count": null, "params": [], "start": **********.349488, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/components/input.blade.phpcomponents.input", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fcomponents%2Finput.blade.php&line=1", "ajax": false, "filename": "input.blade.php", "line": "?"}, "render_count": 33, "name_original": "components.input"}, {"name": "1x manufacturing_orders._store_dimensions", "param_count": null, "params": [], "start": **********.365908, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/manufacturing_orders/_store_dimensions.blade.phpmanufacturing_orders._store_dimensions", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fmanufacturing_orders%2F_store_dimensions.blade.php&line=1", "ajax": false, "filename": "_store_dimensions.blade.php", "line": "?"}, "render_count": 1, "name_original": "manufacturing_orders._store_dimensions"}, {"name": "1x manufacturing_orders._store_pricing", "param_count": null, "params": [], "start": **********.373049, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/manufacturing_orders/_store_pricing.blade.phpmanufacturing_orders._store_pricing", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fmanufacturing_orders%2F_store_pricing.blade.php&line=1", "ajax": false, "filename": "_store_pricing.blade.php", "line": "?"}, "render_count": 1, "name_original": "manufacturing_orders._store_pricing"}, {"name": "2x components.textarea", "param_count": null, "params": [], "start": **********.393598, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/components/textarea.blade.phpcomponents.textarea", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fcomponents%2Ftextarea.blade.php&line=1", "ajax": false, "filename": "textarea.blade.php", "line": "?"}, "render_count": 2, "name_original": "components.textarea"}, {"name": "2x components.anchor-tag", "param_count": null, "params": [], "start": **********.394419, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/components/anchor-tag.blade.phpcomponents.anchor-tag", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fcomponents%2Fanchor-tag.blade.php&line=1", "ajax": false, "filename": "anchor-tag.blade.php", "line": "?"}, "render_count": 2, "name_original": "components.anchor-tag"}, {"name": "1x manufacturing_orders._rideaux_basic_info", "param_count": null, "params": [], "start": **********.395279, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/manufacturing_orders/_rideaux_basic_info.blade.phpmanufacturing_orders._rideaux_basic_info", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fmanufacturing_orders%2F_rideaux_basic_info.blade.php&line=1", "ajax": false, "filename": "_rideaux_basic_info.blade.php", "line": "?"}, "render_count": 1, "name_original": "manufacturing_orders._rideaux_basic_info"}, {"name": "1x manufacturing_orders._rideaux_dimensions", "param_count": null, "params": [], "start": **********.411156, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/manufacturing_orders/_rideaux_dimensions.blade.phpmanufacturing_orders._rideaux_dimensions", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fmanufacturing_orders%2F_rideaux_dimensions.blade.php&line=1", "ajax": false, "filename": "_rideaux_dimensions.blade.php", "line": "?"}, "render_count": 1, "name_original": "manufacturing_orders._rideaux_dimensions"}, {"name": "1x manufacturing_orders._rideaux_details", "param_count": null, "params": [], "start": **********.417626, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/manufacturing_orders/_rideaux_details.blade.phpmanufacturing_orders._rideaux_details", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fmanufacturing_orders%2F_rideaux_details.blade.php&line=1", "ajax": false, "filename": "_rideaux_details.blade.php", "line": "?"}, "render_count": 1, "name_original": "manufacturing_orders._rideaux_details"}, {"name": "1x manufacturing_orders._rideaux_pricing", "param_count": null, "params": [], "start": **********.425903, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/manufacturing_orders/_rideaux_pricing.blade.phpmanufacturing_orders._rideaux_pricing", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fmanufacturing_orders%2F_rideaux_pricing.blade.php&line=1", "ajax": false, "filename": "_rideaux_pricing.blade.php", "line": "?"}, "render_count": 1, "name_original": "manufacturing_orders._rideaux_pricing"}, {"name": "1x layouts.app", "param_count": null, "params": [], "start": **********.461626, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/layouts/app.blade.phplayouts.app", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.app"}, {"name": "1x layouts.head", "param_count": null, "params": [], "start": **********.462977, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/layouts/head.blade.phplayouts.head", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Flayouts%2Fhead.blade.php&line=1", "ajax": false, "filename": "head.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.head"}, {"name": "1x layouts.page-loader", "param_count": null, "params": [], "start": **********.465058, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/layouts/page-loader.blade.phplayouts.page-loader", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Flayouts%2Fpage-loader.blade.php&line=1", "ajax": false, "filename": "page-loader.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.page-loader"}, {"name": "1x layouts.navigation", "param_count": null, "params": [], "start": **********.466219, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/layouts/navigation.blade.phplayouts.navigation", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Flayouts%2Fnavigation.blade.php&line=1", "ajax": false, "filename": "navigation.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.navigation"}, {"name": "1x layouts.header", "param_count": null, "params": [], "start": **********.63761, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/layouts/header.blade.phplayouts.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Flayouts%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.header"}, {"name": "1x components.header-shortcut-menu", "param_count": null, "params": [], "start": **********.644214, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/components/header-shortcut-menu.blade.phpcomponents.header-shortcut-menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fcomponents%2Fheader-shortcut-menu.blade.php&line=1", "ajax": false, "filename": "header-shortcut-menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.header-shortcut-menu"}, {"name": "1x components.flag-toggle", "param_count": null, "params": [], "start": **********.66601, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/components/flag-toggle.blade.phpcomponents.flag-toggle", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fcomponents%2Fflag-toggle.blade.php&line=1", "ajax": false, "filename": "flag-toggle.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.flag-toggle"}, {"name": "1x layouts.footer", "param_count": null, "params": [], "start": **********.676987, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/layouts/footer.blade.phplayouts.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Flayouts%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.footer"}, {"name": "1x layouts.script", "param_count": null, "params": [], "start": **********.67856, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/layouts/script.blade.phplayouts.script", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Flayouts%2Fscript.blade.php&line=1", "ajax": false, "filename": "script.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.script"}]}, "route": {"uri": "GET manufacturing/order/create", "middleware": "web, auth", "as": "manufacturing.order.create", "controller": "App\\Http\\Controllers\\ManufacturingOrderController@create", "namespace": null, "prefix": "/manufacturing", "where": [], "file": "<a href=\"phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FHttp%2FControllers%2FManufacturingOrderController.php&line=22\" onclick=\"\">app/Http/Controllers/ManufacturingOrderController.php:22-34</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.01969, "accumulated_duration_str": "19.69ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 17, "namespace": "middleware", "name": "auth", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}], "start": **********.260612, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:168", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "deltapos", "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.2792768, "duration": 0.01358, "duration_str": "13.58ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "deltapos", "start_percent": 0, "width_percent": 68.969}, {"sql": "select * from `manufacturing_orders` order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ManufacturingOrderController.php", "file": "D:\\www\\delta\\app\\Http\\Controllers\\ManufacturingOrderController.php", "line": 25}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.308028, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "ManufacturingOrderController.php:25", "source": "app/Http/Controllers/ManufacturingOrderController.php:25", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FHttp%2FControllers%2FManufacturingOrderController.php&line=25", "ajax": false, "filename": "ManufacturingOrderController.php", "line": "25"}, "connection": "deltapos", "start_percent": 68.969, "width_percent": 7.161}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 1 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["1", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\www\\delta\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\www\\delta\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 193}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\www\\delta\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 244}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\www\\delta\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 136}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.495667, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:305", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:305", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=305", "ajax": false, "filename": "HasPermissions.php", "line": "305"}, "connection": "deltapos", "start_percent": 76.13, "width_percent": 6.856}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\www\\delta\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\www\\delta\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 291}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\www\\delta\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 193}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\www\\delta\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 244}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\www\\delta\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 136}], "start": **********.500092, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:188", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:188", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=188", "ajax": false, "filename": "HasRoles.php", "line": "188"}, "connection": "deltapos", "start_percent": 82.986, "width_percent": 6.958}, {"sql": "select `id`, `name`, `code`, `emoji` from `languages` where `status` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/View/Components/FlagToggle.php", "file": "D:\\www\\delta\\app\\View\\Components\\FlagToggle.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 961}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.662396, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "FlagToggle.php:43", "source": "app/View/Components/FlagToggle.php:43", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FView%2FComponents%2FFlagToggle.php&line=43", "ajax": false, "filename": "FlagToggle.php", "line": "43"}, "connection": "deltapos", "start_percent": 89.944, "width_percent": 5.13}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "view", "name": "layouts.header", "file": "D:\\www\\delta\\resources\\views/layouts/header.blade.php", "line": 81}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.671719, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "layouts.header:81", "source": "view::layouts.header:81", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Flayouts%2Fheader.blade.php&line=81", "ajax": false, "filename": "header.blade.php", "line": "81"}, "connection": "deltapos", "start_percent": 95.074, "width_percent": 4.926}]}, "models": {"data": {"App\\Models\\Language": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 7, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 110, "messages": [{"message": "[ability => customer.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2081553570 data-indent-pad=\"  \"><span class=sf-dump-note>customer.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">customer.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2081553570\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.510038, "xdebug_link": null}, {"message": "[ability => customer.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-43119597 data-indent-pad=\"  \"><span class=sf-dump-note>customer.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">customer.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-43119597\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.51341, "xdebug_link": null}, {"message": "[ability => supplier.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-729294245 data-indent-pad=\"  \"><span class=sf-dump-note>supplier.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">supplier.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-729294245\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.515184, "xdebug_link": null}, {"message": "[ability => sale.invoice.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-438058 data-indent-pad=\"  \"><span class=sf-dump-note>sale.invoice.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">sale.invoice.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-438058\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.51666, "xdebug_link": null}, {"message": "[ability => sale.invoice.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1776403539 data-indent-pad=\"  \"><span class=sf-dump-note>sale.invoice.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">sale.invoice.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1776403539\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.518076, "xdebug_link": null}, {"message": "[ability => sale.invoice.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1918414605 data-indent-pad=\"  \"><span class=sf-dump-note>sale.invoice.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">sale.invoice.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1918414605\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.519504, "xdebug_link": null}, {"message": "[ability => sale.quotation.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1963072836 data-indent-pad=\"  \"><span class=sf-dump-note>sale.quotation.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">sale.quotation.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1963072836\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.522169, "xdebug_link": null}, {"message": "[ability => sale.invoice.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1645193164 data-indent-pad=\"  \"><span class=sf-dump-note>sale.invoice.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">sale.invoice.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1645193164\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.523519, "xdebug_link": null}, {"message": "[ability => sale.order.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1720228546 data-indent-pad=\"  \"><span class=sf-dump-note>sale.order.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">sale.order.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1720228546\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.527755, "xdebug_link": null}, {"message": "[ability => sale.return.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1831301917 data-indent-pad=\"  \"><span class=sf-dump-note>sale.return.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">sale.return.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1831301917\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.528934, "xdebug_link": null}, {"message": "[ability => purchase.bill.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1876846320 data-indent-pad=\"  \"><span class=sf-dump-note>purchase.bill.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">purchase.bill.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1876846320\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.529929, "xdebug_link": null}, {"message": "[ability => purchase.bill.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1290098865 data-indent-pad=\"  \"><span class=sf-dump-note>purchase.bill.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">purchase.bill.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1290098865\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.531626, "xdebug_link": null}, {"message": "[ability => purchase.bill.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-437411323 data-indent-pad=\"  \"><span class=sf-dump-note>purchase.bill.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">purchase.bill.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-437411323\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.53325, "xdebug_link": null}, {"message": "[ability => purchase.order.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-894153943 data-indent-pad=\"  \"><span class=sf-dump-note>purchase.order.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">purchase.order.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-894153943\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.534588, "xdebug_link": null}, {"message": "[ability => purchase.return.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1574269125 data-indent-pad=\"  \"><span class=sf-dump-note>purchase.return.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">purchase.return.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1574269125\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.538152, "xdebug_link": null}, {"message": "[ability => item.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-448236005 data-indent-pad=\"  \"><span class=sf-dump-note>item.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">item.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-448236005\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.539619, "xdebug_link": null}, {"message": "[ability => item.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1592572963 data-indent-pad=\"  \"><span class=sf-dump-note>item.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">item.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1592572963\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.542891, "xdebug_link": null}, {"message": "[ability => item.category.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-642017421 data-indent-pad=\"  \"><span class=sf-dump-note>item.category.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">item.category.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-642017421\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.544251, "xdebug_link": null}, {"message": "[ability => item.brand.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1709297355 data-indent-pad=\"  \"><span class=sf-dump-note>item.brand.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">item.brand.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1709297355\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.545522, "xdebug_link": null}, {"message": "[ability => expense.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-828241196 data-indent-pad=\"  \"><span class=sf-dump-note>expense.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">expense.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-828241196\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.546175, "xdebug_link": null}, {"message": "[ability => expense.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1799837036 data-indent-pad=\"  \"><span class=sf-dump-note>expense.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">expense.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1799837036\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.547125, "xdebug_link": null}, {"message": "[ability => expense.category.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2066222918 data-indent-pad=\"  \"><span class=sf-dump-note>expense.category.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">expense.category.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2066222918\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.547662, "xdebug_link": null}, {"message": "[\n  ability => expense.subcategory.view,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-727861373 data-indent-pad=\"  \"><span class=sf-dump-note>expense.subcategory.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">expense.subcategory.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-727861373\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.548473, "xdebug_link": null}, {"message": "[ability => transaction.cash.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1013044064 data-indent-pad=\"  \"><span class=sf-dump-note>transaction.cash.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">transaction.cash.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1013044064\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.549135, "xdebug_link": null}, {"message": "[ability => transaction.cash.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1153213032 data-indent-pad=\"  \"><span class=sf-dump-note>transaction.cash.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">transaction.cash.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1153213032\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.54975, "xdebug_link": null}, {"message": "[\n  ability => transaction.cheque.view,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-note>transaction.cheque.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">transaction.cheque.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.550418, "xdebug_link": null}, {"message": "[ability => transaction.bank.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>transaction.bank.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">transaction.bank.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.550971, "xdebug_link": null}, {"message": "[ability => warehouse.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>warehouse.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">warehouse.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.551849, "xdebug_link": null}, {"message": "[ability => warehouse.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1141032049 data-indent-pad=\"  \"><span class=sf-dump-note>warehouse.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">warehouse.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1141032049\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.554504, "xdebug_link": null}, {"message": "[ability => stock_transfer.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-422612371 data-indent-pad=\"  \"><span class=sf-dump-note>stock_transfer.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">stock_transfer.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-422612371\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.557074, "xdebug_link": null}, {"message": "[ability => import.item, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1114586990 data-indent-pad=\"  \"><span class=sf-dump-note>import.item</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">import.item</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1114586990\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.558264, "xdebug_link": null}, {"message": "[ability => import.item, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-618115349 data-indent-pad=\"  \"><span class=sf-dump-note>import.item</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">import.item</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-618115349\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.559129, "xdebug_link": null}, {"message": "[ability => import.party, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1290326859 data-indent-pad=\"  \"><span class=sf-dump-note>import.party</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">import.party</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1290326859\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.559965, "xdebug_link": null}, {"message": "[ability => generate.barcode, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>generate.barcode</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">generate.barcode</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.560729, "xdebug_link": null}, {"message": "[ability => account.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>account.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">account.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.562068, "xdebug_link": null}, {"message": "[ability => account.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-note>account.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">account.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.563244, "xdebug_link": null}, {"message": "[ability => account.group.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>account.group.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">account.group.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.564246, "xdebug_link": null}, {"message": "[ability => profile.edit, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>profile.edit</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">profile.edit</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.564716, "xdebug_link": null}, {"message": "[ability => profile.edit, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-81188155 data-indent-pad=\"  \"><span class=sf-dump-note>profile.edit</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">profile.edit</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-81188155\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.565448, "xdebug_link": null}, {"message": "[ability => user.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-334098177 data-indent-pad=\"  \"><span class=sf-dump-note>user.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">user.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-334098177\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.565871, "xdebug_link": null}, {"message": "[ability => role.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-562492998 data-indent-pad=\"  \"><span class=sf-dump-note>role.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">role.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-562492998\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.566308, "xdebug_link": null}, {"message": "[ability => permission.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-761995790 data-indent-pad=\"  \"><span class=sf-dump-note>permission.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">permission.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-761995790\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.567197, "xdebug_link": null}, {"message": "[ability => sms.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1435702094 data-indent-pad=\"  \"><span class=sf-dump-note>sms.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">sms.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1435702094\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.567578, "xdebug_link": null}, {"message": "[ability => sms.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-360968225 data-indent-pad=\"  \"><span class=sf-dump-note>sms.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">sms.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-360968225\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.569665, "xdebug_link": null}, {"message": "[ability => sms.template.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-175636747 data-indent-pad=\"  \"><span class=sf-dump-note>sms.template.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">sms.template.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-175636747\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.572294, "xdebug_link": null}, {"message": "[ability => email.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-925313348 data-indent-pad=\"  \"><span class=sf-dump-note>email.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">email.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-925313348\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.573631, "xdebug_link": null}, {"message": "[ability => email.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-802742253 data-indent-pad=\"  \"><span class=sf-dump-note>email.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">email.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-802742253\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.574321, "xdebug_link": null}, {"message": "[ability => email.template.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1056300152 data-indent-pad=\"  \"><span class=sf-dump-note>email.template.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">email.template.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1056300152\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.575234, "xdebug_link": null}, {"message": "[ability => report.*, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1078538861 data-indent-pad=\"  \"><span class=sf-dump-note>report.*</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">report.*</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1078538861\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.57708, "xdebug_link": null}, {"message": "[ability => report.profit_and_loss, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-288540290 data-indent-pad=\"  \"><span class=sf-dump-note>report.profit_and_loss</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">report.profit_and_loss</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-288540290\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.577972, "xdebug_link": null}, {"message": "[\n  ability => report.item.transaction.batch,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-765939960 data-indent-pad=\"  \"><span class=sf-dump-note>report.item.transaction.batch</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"29 characters\">report.item.transaction.batch</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-765939960\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.578871, "xdebug_link": null}, {"message": "[\n  ability => report.item.transaction.batch,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1625346630 data-indent-pad=\"  \"><span class=sf-dump-note>report.item.transaction.batch</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"29 characters\">report.item.transaction.batch</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1625346630\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.579647, "xdebug_link": null}, {"message": "[\n  ability => report.item.transaction.serial,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-265884976 data-indent-pad=\"  \"><span class=sf-dump-note>report.item.transaction.serial</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"30 characters\">report.item.transaction.serial</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-265884976\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.580465, "xdebug_link": null}, {"message": "[\n  ability => report.item.transaction.general,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-971060580 data-indent-pad=\"  \"><span class=sf-dump-note>report.item.transaction.general</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"31 characters\">report.item.transaction.general</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-971060580\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.581307, "xdebug_link": null}, {"message": "[ability => report.purchase, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2081296300 data-indent-pad=\"  \"><span class=sf-dump-note>report.purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">report.purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2081296300\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.582231, "xdebug_link": null}, {"message": "[ability => report.purchase, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1987998469 data-indent-pad=\"  \"><span class=sf-dump-note>report.purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">report.purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1987998469\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.582735, "xdebug_link": null}, {"message": "[ability => report.purchase.item, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1336110456 data-indent-pad=\"  \"><span class=sf-dump-note>report.purchase.item</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">report.purchase.item</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1336110456\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.583277, "xdebug_link": null}, {"message": "[\n  ability => report.purchase.payment,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1629012237 data-indent-pad=\"  \"><span class=sf-dump-note>report.purchase.payment</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">report.purchase.payment</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1629012237\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.58384, "xdebug_link": null}, {"message": "[ability => report.sale, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1538287740 data-indent-pad=\"  \"><span class=sf-dump-note>report.sale</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">report.sale</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1538287740\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.58477, "xdebug_link": null}, {"message": "[ability => report.sale, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1052118840 data-indent-pad=\"  \"><span class=sf-dump-note>report.sale</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">report.sale</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1052118840\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.587493, "xdebug_link": null}, {"message": "[ability => report.sale.item, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-258378830 data-indent-pad=\"  \"><span class=sf-dump-note>report.sale.item</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">report.sale.item</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-258378830\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.588617, "xdebug_link": null}, {"message": "[ability => report.sale.payment, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2055413243 data-indent-pad=\"  \"><span class=sf-dump-note>report.sale.payment</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">report.sale.payment</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2055413243\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.589377, "xdebug_link": null}, {"message": "[\n  ability => report.customer.due.payment,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1008644176 data-indent-pad=\"  \"><span class=sf-dump-note>report.customer.due.payment</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"27 characters\">report.customer.due.payment</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1008644176\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.591513, "xdebug_link": null}, {"message": "[\n  ability => report.customer.due.payment,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1008595743 data-indent-pad=\"  \"><span class=sf-dump-note>report.customer.due.payment</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"27 characters\">report.customer.due.payment</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1008595743\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.592617, "xdebug_link": null}, {"message": "[\n  ability => report.supplier.due.payment,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-994633169 data-indent-pad=\"  \"><span class=sf-dump-note>report.supplier.due.payment</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"27 characters\">report.supplier.due.payment</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-994633169\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.593501, "xdebug_link": null}, {"message": "[ability => report.expense, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1325137020 data-indent-pad=\"  \"><span class=sf-dump-note>report.expense</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">report.expense</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1325137020\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.594074, "xdebug_link": null}, {"message": "[ability => report.expense, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1565541801 data-indent-pad=\"  \"><span class=sf-dump-note>report.expense</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">report.expense</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1565541801\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.594609, "xdebug_link": null}, {"message": "[ability => report.expense.item, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-81598305 data-indent-pad=\"  \"><span class=sf-dump-note>report.expense.item</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">report.expense.item</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-81598305\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.595332, "xdebug_link": null}, {"message": "[ability => report.expense.payment, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-358271389 data-indent-pad=\"  \"><span class=sf-dump-note>report.expense.payment</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">report.expense.payment</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-358271389\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.596111, "xdebug_link": null}, {"message": "[\n  ability => report.transaction.cashflow,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-416872963 data-indent-pad=\"  \"><span class=sf-dump-note>report.transaction.cashflow</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"27 characters\">report.transaction.cashflow</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-416872963\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.597124, "xdebug_link": null}, {"message": "[\n  ability => report.transaction.cashflow,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>report.transaction.cashflow</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"27 characters\">report.transaction.cashflow</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.598495, "xdebug_link": null}, {"message": "[\n  ability => report.transaction.bank-statement,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>report.transaction.bank-statement</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"33 characters\">report.transaction.bank-statement</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.599893, "xdebug_link": null}, {"message": "[ability => report.gst*, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>report.gst*</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">report.gst*</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.602418, "xdebug_link": null}, {"message": "[ability => report.gstr1, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-275694959 data-indent-pad=\"  \"><span class=sf-dump-note>report.gstr1</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">report.gstr1</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-275694959\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.605166, "xdebug_link": null}, {"message": "[ability => report.gstr2, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-45034850 data-indent-pad=\"  \"><span class=sf-dump-note>report.gstr2</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">report.gstr2</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-45034850\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.606639, "xdebug_link": null}, {"message": "[ability => report.stock_transfer, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1087442484 data-indent-pad=\"  \"><span class=sf-dump-note>report.stock_transfer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">report.stock_transfer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1087442484\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.609978, "xdebug_link": null}, {"message": "[ability => report.stock_transfer, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1495386578 data-indent-pad=\"  \"><span class=sf-dump-note>report.stock_transfer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">report.stock_transfer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1495386578\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.611294, "xdebug_link": null}, {"message": "[\n  ability => report.stock_transfer.item,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1227003263 data-indent-pad=\"  \"><span class=sf-dump-note>report.stock_transfer.item</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"26 characters\">report.stock_transfer.item</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1227003263\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.612542, "xdebug_link": null}, {"message": "[ability => report.stock_report.*, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1056836495 data-indent-pad=\"  \"><span class=sf-dump-note>report.stock_report.*</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">report.stock_report.*</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1056836495\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.614004, "xdebug_link": null}, {"message": "[\n  ability => report.stock_report.item.batch,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1170348828 data-indent-pad=\"  \"><span class=sf-dump-note>report.stock_report.item.batch</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"30 characters\">report.stock_report.item.batch</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1170348828\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.615174, "xdebug_link": null}, {"message": "[\n  ability => report.stock_report.item.serial,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2117123331 data-indent-pad=\"  \"><span class=sf-dump-note>report.stock_report.item.serial</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"31 characters\">report.stock_report.item.serial</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2117123331\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.616469, "xdebug_link": null}, {"message": "[\n  ability => report.stock_report.item.general,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2128892632 data-indent-pad=\"  \"><span class=sf-dump-note>report.stock_report.item.general</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"32 characters\">report.stock_report.item.general</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2128892632\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.617608, "xdebug_link": null}, {"message": "[ability => report.expired.item, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-115540690 data-indent-pad=\"  \"><span class=sf-dump-note>report.expired.item</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">report.expired.item</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-115540690\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.619638, "xdebug_link": null}, {"message": "[ability => report.reorder.item, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1326424783 data-indent-pad=\"  \"><span class=sf-dump-note>report.reorder.item</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">report.reorder.item</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1326424783\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.621887, "xdebug_link": null}, {"message": "[ability => tax.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1575320889 data-indent-pad=\"  \"><span class=sf-dump-note>tax.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">tax.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1575320889\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.62425, "xdebug_link": null}, {"message": "[ability => app.settings.edit, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1061184695 data-indent-pad=\"  \"><span class=sf-dump-note>app.settings.edit</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">app.settings.edit</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1061184695\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.625053, "xdebug_link": null}, {"message": "[ability => company.edit, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2035253634 data-indent-pad=\"  \"><span class=sf-dump-note>company.edit</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">company.edit</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2035253634\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.625752, "xdebug_link": null}, {"message": "[ability => tax.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1164316096 data-indent-pad=\"  \"><span class=sf-dump-note>tax.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">tax.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1164316096\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.626487, "xdebug_link": null}, {"message": "[ability => payment.type.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1020690056 data-indent-pad=\"  \"><span class=sf-dump-note>payment.type.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">payment.type.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1020690056\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.627963, "xdebug_link": null}, {"message": "[ability => currency.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-35297393 data-indent-pad=\"  \"><span class=sf-dump-note>currency.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">currency.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-35297393\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.630037, "xdebug_link": null}, {"message": "[ability => unit.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-718996598 data-indent-pad=\"  \"><span class=sf-dump-note>unit.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">unit.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-718996598\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.632034, "xdebug_link": null}, {"message": "[ability => language.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1581182987 data-indent-pad=\"  \"><span class=sf-dump-note>language.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">language.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1581182987\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.633421, "xdebug_link": null}, {"message": "[ability => purchase.bill.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1936722979 data-indent-pad=\"  \"><span class=sf-dump-note>purchase.bill.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">purchase.bill.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1936722979\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.640808, "xdebug_link": null}, {"message": "[ability => sale.invoice.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-432511539 data-indent-pad=\"  \"><span class=sf-dump-note>sale.invoice.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">sale.invoice.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-432511539\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.64208, "xdebug_link": null}, {"message": "[ability => sale.invoice.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1465055327 data-indent-pad=\"  \"><span class=sf-dump-note>sale.invoice.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">sale.invoice.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1465055327\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.64282, "xdebug_link": null}, {"message": "[ability => sale.invoice.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-403695572 data-indent-pad=\"  \"><span class=sf-dump-note>sale.invoice.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">sale.invoice.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-403695572\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.646315, "xdebug_link": null}, {"message": "[ability => purchase.bill.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-849795970 data-indent-pad=\"  \"><span class=sf-dump-note>purchase.bill.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">purchase.bill.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-849795970\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.647233, "xdebug_link": null}, {"message": "[ability => expense.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-748995159 data-indent-pad=\"  \"><span class=sf-dump-note>expense.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">expense.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-748995159\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.647766, "xdebug_link": null}, {"message": "[ability => sale.invoice.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-663490495 data-indent-pad=\"  \"><span class=sf-dump-note>sale.invoice.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">sale.invoice.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-663490495\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.648473, "xdebug_link": null}, {"message": "[ability => purchase.bill.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2140832247 data-indent-pad=\"  \"><span class=sf-dump-note>purchase.bill.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">purchase.bill.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2140832247\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.64906, "xdebug_link": null}, {"message": "[ability => purchase.order.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1662213419 data-indent-pad=\"  \"><span class=sf-dump-note>purchase.order.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">purchase.order.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1662213419\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.649646, "xdebug_link": null}, {"message": "[ability => sale.return.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-648903442 data-indent-pad=\"  \"><span class=sf-dump-note>sale.return.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">sale.return.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-648903442\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.650274, "xdebug_link": null}, {"message": "[ability => purchase.return.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1168597409 data-indent-pad=\"  \"><span class=sf-dump-note>purchase.return.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">purchase.return.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1168597409\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.650832, "xdebug_link": null}, {"message": "[ability => stock_transfer.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-501771668 data-indent-pad=\"  \"><span class=sf-dump-note>stock_transfer.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">stock_transfer.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-501771668\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.653135, "xdebug_link": null}, {"message": "[ability => item.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1814242063 data-indent-pad=\"  \"><span class=sf-dump-note>item.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">item.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1814242063\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.655718, "xdebug_link": null}, {"message": "[ability => customer.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2004366439 data-indent-pad=\"  \"><span class=sf-dump-note>customer.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">customer.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2004366439\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.656548, "xdebug_link": null}, {"message": "[ability => supplier.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-435132693 data-indent-pad=\"  \"><span class=sf-dump-note>supplier.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">supplier.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-435132693\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.658087, "xdebug_link": null}, {"message": "[ability => sale.quotation.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1483854633 data-indent-pad=\"  \"><span class=sf-dump-note>sale.quotation.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">sale.quotation.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1483854633\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.661022, "xdebug_link": null}, {"message": "[\n  ability => general.permission.to.apply.discount.to.purchase,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-686754490 data-indent-pad=\"  \"><span class=sf-dump-note>general.permission.to.apply.discount.to.purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"48 characters\">general.permission.to.apply.discount.to.purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-686754490\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.681175, "xdebug_link": null}, {"message": "[\n  ability => general.permission.to.apply.discount.to.sale,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1207636756 data-indent-pad=\"  \"><span class=sf-dump-note>general.permission.to.apply.discount.to.sale</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"44 characters\">general.permission.to.apply.discount.to.sale</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1207636756\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.683068, "xdebug_link": null}]}, "session": {"_token": "HNkZVWy1EvJYmVGpV26q1OhE2BZcLjdiaGB3lTAA", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/manufacturing/order/create\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"telescope": "<a href=\"http://127.0.0.1:8000/_debugbar/telescope/9f418379-8d9b-47fb-acf7-f6d282847e2e\" target=\"_blank\">View in Telescope</a>", "path_info": "/manufacturing/order/create", "status_code": "<pre class=sf-dump id=sf-dump-905320667 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-905320667\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-468871998 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-468871998\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1434827801 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1434827801\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1880853337 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">http://127.0.0.1:8000/manufacturing_orders</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">en-GB,en;q=0.9,en-US;q=0.8,fr;q=0.7,ar;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2047 characters\">ph_phc_3ESMmY9SgqEAGBB6sMGK5ayYHkeUuknH2vP6FmWH9RA_posthog=%7B%22distinct_id%22%3A%220196b41d-0793-7e06-86ce-017691734ea0%22%2C%22%24sesid%22%3A%5B1746780823698%2C%220196b41d-078e-7e17-a700-24eed1211756%22%2C1746778523534%5D%7D; aureuserp_session=eyJpdiI6IlIvOXVBdXl4UFJOWHhsSjA2YWNNeGc9PSIsInZhbHVlIjoiVXFQdlhTd3ZFMXZyV1JwNjczTEJUczB4N3YxZG5uWXNpamRHWXZyb0xIdDdiaXFoL2xOalBmNmtqMVdKZWllamlOWnNxREZvNjV5VWVxOXVGY3Q5MVNqK1ArTVBteDZIY01KTXdoc2xEaDEwY2draUl3Qi9SbHJUUnVOOWNsY1ciLCJtYWMiOiI1OWU4NmE1NjczOTQyZjZjZDM4YTYyMzZhMmZmODg4NGJlMWQ1ZjY3MjRkNzM5ZmRkMTllNDFjMTE1ODYxN2QyIiwidGFnIjoiIn0%3D; language_data=eyJpdiI6IkFLeWYyUVZhc3dlMURKNE1kWHYxUmc9PSIsInZhbHVlIjoiSUtsV1UvZ1FmVFNjRTgvMVZpM3E2YkRTR3FGNXl4akNCQVhRVkU2SlFqTnJoYUlHTml2T0hQb21RMzdhcm0rdm1OZjExWjFPRmhRQ00zTjNuZEZWeU5HNUNxZEsxQUdVbG1yVk1CM2hMY1BiYkZXNjQ1czFIUVgrTXhPd2VvZFloMWZHMlh0d3VtaDVMVDRHWU16Qm9qZkJ6V3JuOWtCK3lZbEJud3ZLNytKaEw2dGxUcnd4d0psQTdER1k5bWMzIiwibWFjIjoiOWU3MGMxNTA5MGE5YjJlZjQ4NzNjZDc1ZTliNWM1NTM1MTQ4NzcwN2QxZTNlN2E3YTZmNGFjMTI3YWZhOThkYiIsInRhZyI6IiJ9; theme_mode=eyJpdiI6Ii9XM3FTcWRDTHBmaVg5M0k2cFJiMEE9PSIsInZhbHVlIjoiNlZBU0xFRmZDaEdXVE5JRVMyMFhQcG0waG1PUWdFSDdWb1pCMWx3R0JVank5WXhQMytscW4vTW4yMkZSWlhWNjVISlc3NjNtcGNMckVYK1JXb3FOU3c9PSIsIm1hYyI6ImU2MmRhOThiNGQyNDA3N2UwMGViMjRhMzRhOThkYmJkZDdjMmNjYjhjZDk2MjIxMWZlZjVlNjA4Y2ZkYTkzNGIiLCJ0YWciOiIifQ%3D%3D; XSRF-TOKEN=eyJpdiI6InMxeFBGMG96dnNoVkVKTXpjMi85c1E9PSIsInZhbHVlIjoieXkvaUJXMFEvMXBJZVl0MFoxSSt0ZzN2a1lYcnlMRDhkYlZ5a05CTjlMOWVaQ1RuY1kwSlNwNjI1WTkrcHU5eXFja2ZjUkoyMGgwV1BHcE80bTBUdldBT1BvWVJHRkVhaHY4OWgrTXBSYklGNE94WSt3OUdCK2VheW1zYU81WGwiLCJtYWMiOiI1MzMxMjJiNTU5NmYwNTk5MWJlMWRlZmU5ODc3YzBhYmVmYWQ2OTAxMmYxNTQ3ZjE4MDdlNTQxYjdjNzk5NTIwIiwidGFnIjoiIn0%3D; delta_session=eyJpdiI6Ik9vOG9JeG9wMVFSVWhKR2JVT0JNZlE9PSIsInZhbHVlIjoiMmFLNnFFeWUvOW5rQ0VDb3hmVTJEWmlzWkE1RXJ0U0dQZnYrMWxOTWEyS2J1RkYvbFZDTU42b3MxdHNFZ1JOeEswTGtFQU5uR0gxQjdGaUVUMFgrV25ETVh5VDFSMzBEYUszS05WNlNhbFZDNnhZNElXMTY4dWdMazdxUzFmOWkiLCJtYWMiOiIwZTM3ZGU4MWMyN2Q3NDVkNWE5NWNiYjc2MTU4NmY3ZjNjNzhiZmQ2ODljZGVhNWUyMWI5Y2MwZDRlMmI5ZGU5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1880853337\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ph_phc_3ESMmY9SgqEAGBB6sMGK5ayYHkeUuknH2vP6FmWH9RA_posthog</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>aureuserp_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>language_data</span>\" => \"<span class=sf-dump-str title=\"94 characters\">{&quot;language_code&quot;:null,&quot;language_flag&quot;:&quot;flag-icon-us&quot;,&quot;direction&quot;:&quot;ltr&quot;,&quot;emoji&quot;:&quot;flag-icon-us&quot;}</span>\"\n  \"<span class=sf-dump-key>theme_mode</span>\" => \"<span class=sf-dump-str title=\"11 characters\">light-theme</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HNkZVWy1EvJYmVGpV26q1OhE2BZcLjdiaGB3lTAA</span>\"\n  \"<span class=sf-dump-key>delta_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">uHnuvxItO3CJLJzok54Jqf12VJbTwvqHEtEyDXtZ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1347942178 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 15:14:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InBHNHdSNGl1MEUvQ0JrSytVWGhrbUE9PSIsInZhbHVlIjoiTDZwRmRPcTQvcVplaHZ4ZituaE5SQkJ6OS9WQXpOZEQzME9HVnRmcFZNVmZJU2VZZGVhb0htaVRrd2p2dDJqY2FvcGJEOVRYeldYbm5KVDRHYUJaaXFrQU5tWXUxa1VQN09tOXprbUZDMFIvMUtKTHBFSzNWSEUzTGpEd1AvQWYiLCJtYWMiOiIzYzJlZjk3NjI3MDk4Njg3MGUxODRkZDBjYzliNDk0NTI0MjVlMWQ4N2QxYTVmYTk2Nzk2OGM0ZmRmYWRlYTdiIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 17:14:34 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">delta_session=eyJpdiI6ImdnWTkyaDk5YlZKMS9kRlNaWi8vM1E9PSIsInZhbHVlIjoicW55ZXlGanpxQ3VsMHFTVWVteGJXQU1oa1JScXdJbHBLNTI0M1R3c21vZUZtTEIzSk1waHc0em94S2h0MHdNOFNBeVBPalh6c1FUM0ZzZTB0V1lCWFM5N1JZeDluNmFST05Ibk1SenNjdHpqaURXK1lyaGphVnFYWG1YNngrMSsiLCJtYWMiOiJkY2U2NzVlMDYwOTY1Y2E3MGFmYTk3YTM3NDkwMzcxMThmZjQyNGE5NzZjNTg4ODczMmZhNjA0ZTNhMjQ5OGU4IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 17:14:34 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InBHNHdSNGl1MEUvQ0JrSytVWGhrbUE9PSIsInZhbHVlIjoiTDZwRmRPcTQvcVplaHZ4ZituaE5SQkJ6OS9WQXpOZEQzME9HVnRmcFZNVmZJU2VZZGVhb0htaVRrd2p2dDJqY2FvcGJEOVRYeldYbm5KVDRHYUJaaXFrQU5tWXUxa1VQN09tOXprbUZDMFIvMUtKTHBFSzNWSEUzTGpEd1AvQWYiLCJtYWMiOiIzYzJlZjk3NjI3MDk4Njg3MGUxODRkZDBjYzliNDk0NTI0MjVlMWQ4N2QxYTVmYTk2Nzk2OGM0ZmRmYWRlYTdiIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 17:14:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">delta_session=eyJpdiI6ImdnWTkyaDk5YlZKMS9kRlNaWi8vM1E9PSIsInZhbHVlIjoicW55ZXlGanpxQ3VsMHFTVWVteGJXQU1oa1JScXdJbHBLNTI0M1R3c21vZUZtTEIzSk1waHc0em94S2h0MHdNOFNBeVBPalh6c1FUM0ZzZTB0V1lCWFM5N1JZeDluNmFST05Ibk1SenNjdHpqaURXK1lyaGphVnFYWG1YNngrMSsiLCJtYWMiOiJkY2U2NzVlMDYwOTY1Y2E3MGFmYTk3YTM3NDkwMzcxMThmZjQyNGE5NzZjNTg4ODczMmZhNjA0ZTNhMjQ5OGU4IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 17:14:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1347942178\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1026460176 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HNkZVWy1EvJYmVGpV26q1OhE2BZcLjdiaGB3lTAA</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"48 characters\">http://127.0.0.1:8000/manufacturing/order/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1026460176\", {\"maxDepth\":0})</script>\n"}}