<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('expenses', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->date('expense_date');
            $table->string('prefix_code')->nullable();
            $table->string('count_id')->nullable();
            $table->string('expense_code')->nullable();
            $table->unsignedBigInteger('expense_category_id')->index('expenses_expense_category_id_foreign');
            $table->unsignedBigInteger('expense_subcategory_id')->nullable()->index('expenses_expense_subcategory_id_foreign');
            $table->text('note')->nullable();
            $table->decimal('round_off', 20, 4)->default(0);
            $table->decimal('grand_total', 20, 4)->default(0);
            $table->decimal('paid_amount', 20, 4)->default(0);
            $table->unsignedBigInteger('created_by')->nullable()->index('expenses_created_by_foreign');
            $table->unsignedBigInteger('updated_by')->nullable()->index('expenses_updated_by_foreign');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('expenses');
    }
};
