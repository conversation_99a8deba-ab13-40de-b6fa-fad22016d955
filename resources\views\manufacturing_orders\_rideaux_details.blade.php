<div class="row g-3">
    <div class="col-md-4">
        <x-label for="curtain_finish" name="{{ __('manufacturing.curtain_finish') }}" />
        <select class="form-select" name="curtain_finish" required>
            <option value="Wave">{{ __('manufacturing.curtain_finishes.wave') }}</option>
            <option value="PP">{{ __('manufacturing.curtain_finishes.pp') }}</option>
            <option value="PF">{{ __('manufacturing.curtain_finishes.pf') }}</option>
        </select>
    </div>

    <div class="col-md-4">
        <x-label for="manipulation" name="{{ __('manufacturing.manipulation') }}" />
        <select class="form-select" name="manipulation" required>
            <option value="Moteur">{{ __('manufacturing.manipulation_types.moteur') }}</option>
            <option value="Manuelle">{{ __('manufacturing.manipulation_types.manuelle') }}</option>
        </select>
    </div>

    <div class="col-md-4">
        <x-label for="height_type" name="{{ __('manufacturing.height_type') }}" />
        <select class="form-select" name="height_type" id="rideaux_height_type" required>
            <option value="simple">{{ __('manufacturing.height_types.simple') }}</option>
            <option value="double">{{ __('manufacturing.height_types.double') }}</option>
        </select>
    </div>
</div>

<div class="row g-3 mt-3">
    <div class="col-md-6">
        <x-label for="positioning_type" name="{{ __('manufacturing.positioning_type') }}" />
        <select class="form-select" name="positioning_type" id="rideaux_positioning_type">
            <option value="">{{ __('manufacturing.select_positioning_type') }}</option>
            <option value="lateral">{{ __('manufacturing.positioning_types.lateral') }}</option>
            <option value="central">{{ __('manufacturing.positioning_types.central') }}</option>
        </select>
    </div>
</div>