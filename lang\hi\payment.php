<?php

return [

    'info'                      => 'भुगतान जानकारी',
    'type'                      => 'भुगतान प्रकार',
    'types'                     => 'भुगतान प्रकार',
    'payment'                   => 'भुगतान',
    'payments'                  => 'भुगतान',
    'create'                    => 'भुगतान बनाएँ',
    'add'                       => 'भुगतान जोड़ें',
    'details'                   => 'भुगतान विवरण',
    'update'                    => 'भुगतान अपडेट करें',
    'amount'                    => 'रकम',
    'note'                      => 'भुगतान नोट',
    'history'                   => 'भुगतान इतिहास',
    'status'                    => 'भुगतान स्थिति',
    'please_select_payment_type'   => 'कृपया भुगतान प्रकार चुनें',
    'payment_should_not_be_greater_than_grand_total'   => 'भुगतान क्रमिक राशि से अधिक नहीं होना चाहिए',

    'paid'      => 'भुगतान किया गया',
    'balance'   => 'शेष राशि',
    'date'      => 'भुगतान की तारीख',
    'received'  => 'प्राप्त किया गया भुगतान',

    'payment_type' => 'भुगतान प्रकार',
    'add_payment_type' => 'भुगतान प्रकार जोड़ें',
    'make_payment' => 'भुगतान करें',
    'transaction_date' => 'लेनदेन तिथि',
    'receipt_no' => 'रसीद संख्या',

    'summary' => 'भुगतान सारांश',
    'paid_to' => 'भुगतान किया गया',
    'payment_out' => 'भुगतान आउट',
    'payment_in' => 'भुगतान इन',
    'payment_receipt' => 'भुगतान रसीद',
    'previously_paid' => 'पूर्व में भुगतान किया गया',
    'received_from' => 'से प्राप्त',
    'receive_payment' => 'भुगतान प्राप्त करें',
    'cash' => 'नकद',
    'cheque' => 'चेक',



    'missed_to_select_payment_type' => 'भुगतान प्रकार का चयन करने में चूक गए',
    'paid_payment_not_equal_to_grand_total' => 'भुगतान की गई राशि कुल से बराबर होनी चाहिए!',
    'failed_to_update_paid_amount' => 'भुगतान की गई राशि अपडेट करने में विफल!',
    'failed_to_update_account' => 'खाता अपडेट करने में विफल!',
    'failed_to_record_payment_transactions' => 'भुगतान लेनदेन रिकॉर्ड करने में विफल!',
    'failed_to_delete_payment_transactions' => 'भुगतान लेनदेन हटाने में विफल!',
    'no_payment_history_found' => 'कोई भुगतान इतिहास नहीं मिला।',
    'failed_to_record_cheque_payment_transactions' => 'चेक भुगतान लेनदेन रिकॉर्ड करने में विफल!',

    'cash_in_hand' => 'हाथ में नकद',
    'cash_and_bank' => 'नकद और बैंक',
    'cash_adjust' => 'नकद समायोजन',
    'adjustment_type' => 'समायोजन प्रकार',
    'cheques' => 'चेक',
    'cheque_deposit' => 'चेक जमा',
    'deposit_date' => 'जमा तिथि',
    'deposit_to' => 'जमा करने के लिए',
    'cheque_reopened_successfully' => 'चेक सफलतापूर्वक फिर से खोला गया',
    'transfer_cheque' => 'चेक स्थानांतरण',
    'transfer_date' => 'स्थानांतरण तिथि',
    'bank_accounts' => 'बैंक खाते',
    'add_bank' => 'बैंक जोड़ें',
    'create_account' => 'खाता बनाएँ',
    'bank_details' => 'बैंक विवरण',
    'bank_name' => 'बैंक का नाम',
    'account_number' => 'खाता संख्या',
    'bank_code' => 'बैंक कोड/IFSC',
    'update_bank_account' => 'बैंक खाता अपडेट करें',
    'bank' => 'बैंक',
    'bank_transactions' => 'बैंक लेनदेन',
    'print_bank_details_on_invoice' => 'इनवॉइस पर बैंक विवरण प्रिंट करें',
    'cash_flow' => 'नकदी प्रवाह',
    'bank_statement' => 'बैंक स्टेटमेंट',
    'cash_in' => 'नकद में',
    'cash_out' => 'नकद बाहर',
    'running_cash' => 'चालू नकद',
    'withdrawal_amount' => 'निकासी राशि',
    'deposit_amount' => 'जमा राशि',
    'you_pay' => 'आप भुगतान करते हैं',
    'you_collect' => 'आप संग्रह करते हैं',
    'adjust_invoices' => 'इनवॉइस समायोजित करें',
    'adjust_bills' => 'बिल समायोजित करें',
    'adjust' => 'समायोजित करें',
    'adjust_amount' => 'समायोजन राशि',
    'due_payments' => 'देय भुगतान',

    'no_balance' => 'कोई शेष नहीं',
    'payment_direction' => 'भुगतान दिशा',
    'payment_receivables' => 'भुगतान प्राप्तियां',
    'payment_paybles' => 'भुगतान देय',

    /*1.1.2*/
    'change_return'                         => 'बदलाव वापसी',
    'due_payment'                           => 'बकाया भुगतान',
    'due_payments'                          => 'बकाया भुगतानों',
    'due_payment_report'                    => 'बकाया भुगतान रिपोर्ट',

    'receipt'                               => 'रसीद',

];
