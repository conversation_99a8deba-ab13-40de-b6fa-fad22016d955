<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cheque_transactions', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->date('transaction_date');
            $table->date('transfer_date')->nullable();
            $table->string('cheque_no')->nullable();
            $table->unsignedBigInteger('payment_transaction_id')->index('cheque_transactions_payment_transaction_id_foreign');
            $table->unsignedBigInteger('payment_type_id')->index('cheque_transactions_payment_type_id_foreign');
            $table->unsignedBigInteger('transfer_to_payment_type_id')->nullable()->index('cheque_transactions_transfer_to_payment_type_id_foreign');
            $table->decimal('amount', 20, 4)->default(0);
            $table->text('note')->nullable();
            $table->unsignedBigInteger('created_by')->nullable()->index('cheque_transactions_created_by_foreign');
            $table->unsignedBigInteger('updated_by')->nullable()->index('cheque_transactions_updated_by_foreign');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cheque_transactions');
    }
};
