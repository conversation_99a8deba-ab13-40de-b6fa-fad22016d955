<?php
namespace App\Http\Controllers;

use App\Models\ManufacturingOrder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ManufacturingOrderController extends Controller
{
    /**
     * Display a listing of the manufacturing orders.
     */
    public function index()
    {
        $orders = ManufacturingOrder::latest()->paginate(10);
        return view('manufacturing_orders.index', compact('orders'));
    }

    /**
     * Show the form for creating a new manufacturing order.
     */
    public function create()
    {
        // Get the next order number
        $lastOrder = ManufacturingOrder::latest()->first();
        $nextOrderNumber = $lastOrder ? (intval(substr($lastOrder->order_number, 3)) + 1) : 1;
        
        $data = [
            'prefix_code' => 'MO',
            'count_id' => str_pad($nextOrderNumber, 5, '0', STR_PAD_LEFT)
        ];

        return view('manufacturing_orders.create', compact('data'));
    }

    /**
     * Store a newly created manufacturing order in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'order_number' => 'required|string|unique:manufacturing_orders,order_number',
            'order_date' => 'required|date',
            'delivery_date' => 'nullable|date',
            'designation' => 'required|string',
            'tissus' => 'required|string',
            'largeur_fini' => 'required|numeric',
            'mecanisme' => 'required|string|in:BR,GHD',
            'qte_to_billed' => 'required|numeric',
            'billed_unity' => 'required|string',
            'unit_price' => 'required|numeric',
            'extras_fees' => 'nullable|numeric',
            'qty' => 'required|numeric',
            'order_status' => 'required|in:Draft,In Progress,Completed,Delivered',
            'fabric_requirement' => 'required|numeric',
            'curtain_type' => 'required|in:Store,Rideaux',
            'curtain_finish' => 'required|in:Wave,PP,PF',
            'manipulation' => 'required|in:Moteur,Manuelle',
            'height_type' => 'required|in:simple,double',
            'sous_plafond' => 'nullable|numeric',
            'hauteur_finale' => 'nullable|numeric',
            'formule_appliquee' => 'nullable|string',
            'nombre_agrafes' => 'nullable|integer',
            'notes' => 'nullable|string',
        ]);

        // Add created_by user ID
        $validated['created_by'] = Auth::id();

        $order = ManufacturingOrder::create($validated);

        return redirect()
            ->route('manufacturing.order.index')
            ->with('success', __('manufacturing.success_create'));
    }

    /**
     * Display the specified manufacturing order.
     */
    public function show(ManufacturingOrder $order)
    {
        return view('manufacturing_orders.show', compact('order'));
    }

    /**
     * Show the form for editing the specified manufacturing order.
     */
    public function edit(ManufacturingOrder $order)
    {
        $data = [
            'prefix_code' => substr($order->order_number, 0, 2),
            'count_id' => substr($order->order_number, 3)
        ];

        return view('manufacturing_orders.edit', compact('order', 'data'));
    }

    /**
     * Update the specified manufacturing order in storage.
     */
    public function update(Request $request, ManufacturingOrder $order)
    {
        $validated = $request->validate([
            'order_number' => 'required|string|unique:manufacturing_orders,order_number,' . $order->id,
            'order_date' => 'required|date',
            'delivery_date' => 'nullable|date',
            'designation' => 'required|string',
            'tissus' => 'required|string',
            'largeur_fini' => 'required|numeric',
            'mecanisme' => 'required|string|in:BR,GHD',
            'qte_to_billed' => 'required|numeric',
            'billed_unity' => 'required|string',
            'unit_price' => 'required|numeric',
            'extras_fees' => 'nullable|numeric',
            'qty' => 'required|numeric',
            'order_status' => 'required|in:Draft,In Progress,Completed,Delivered',
            'fabric_requirement' => 'required|numeric',
            'curtain_type' => 'required|in:Store,Rideaux',
            'curtain_finish' => 'required|in:Wave,PP,PF',
            'manipulation' => 'required|in:Moteur,Manuelle',
            'height_type' => 'required|in:simple,double',
            'sous_plafond' => 'nullable|numeric',
            'hauteur_finale' => 'nullable|numeric',
            'formule_appliquee' => 'nullable|string',
            'nombre_agrafes' => 'nullable|integer',
            'notes' => 'nullable|string',
        ]);

        $order->update($validated);

        return redirect()
            ->route('manufacturing.order.index')
            ->with('success', __('manufacturing.success_update'));
    }

    /**
     * Remove the specified manufacturing order from storage.
     */
    public function destroy(ManufacturingOrder $order)
    {
        $order->delete();

        return redirect()
            ->route('manufacturing.order.index')
            ->with('success', 'Manufacturing order deleted successfully.');
    }
} 