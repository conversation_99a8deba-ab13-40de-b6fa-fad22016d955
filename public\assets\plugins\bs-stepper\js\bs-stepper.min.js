/*!
 * bsStepper v1.7.0 (https://github.com/Johann-<PERSON>/bs-stepper)
 * Copyright 2018 - 2019 Johann-S <<EMAIL>>
 * Licensed under MIT (https://github.com/Johann-S/bs-stepper/blob/master/LICENSE)
 */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t=t||self).Stepper=e()}(this,function(){"use strict";function t(){return(t=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var s in n)Object.prototype.hasOwnProperty.call(n,s)&&(t[s]=n[s])}return t}).apply(this,arguments)}var e=function(t){if(void 0===t)throw new Error("No element passed");var e="string"==typeof t?document.querySelector(t):t;if(!e)throw new Error("No element found");return e},n=function(){function n(n,s){var i=this;void 0===s&&(s={}),this._element=e(n),this._options=t({},{linear:!0,animation:!1,selectors:{steps:".step",trigger:".step-trigger",stepper:".bs-stepper"}},s),this._currentIndex=0,this._stepsContents=[],this.options.selectors.steps&&this.options.selectors.trigger&&(this._steps=[].slice.call(this._element.querySelectorAll(this.options.selectors.steps))),this._steps&&this._steps.length&&(this._steps=this._steps.map(function(t){return{step:t,trigger:t.querySelector(i.options.selectors.trigger),content:null}}),this._steps.forEach(function(t){t.content=i._element.querySelector('[role="tabpanel"][aria-labelledby="'+t.trigger.getAttribute("id")+'"]')}),this._steps.forEach(function(t){t.trigger.addEventListener("click",function(e){e.preventDefault(),i._currentIndex=i._steps.indexOf(t),i._show()})})),this.options.animation&&this._steps&&this._steps.forEach(function(t){var e=t.content;e&&e.classList.add("fade")})}var s=n.prototype;return s._show=function(){var t=this;this._steps.forEach(function(e,n){var s=e.trigger,i=e.content;if(n===t._currentIndex){var r=s.getAttribute("aria-selected");r&&"true"!==r&&(s.setAttribute("aria-selected","true"),i&&(i.classList.add("active"),i.removeAttribute("tabindex"))),s.classList.add("active"),i&&i.classList.add("active")}else{var o=s.getAttribute("aria-selected");o&&"false"!==o&&(s.setAttribute("aria-selected","false"),i&&(i.classList.remove("active"),i.setAttribute("tabindex","-1"))),s.classList.remove("active"),i&&i.classList.remove("active")}}),this._currentIndex===this._steps.length-1?this.options.animation&&this._steps.forEach(function(e){var n=e.content;n&&n.classList.remove("fade")}):this.options.animation&&this._steps.forEach(function(e){var n=e.content;n&&n.classList.add("fade")})},s.next=function(){this._currentIndex<this._steps.length-1&&(this._currentIndex++,this.options.linear&&this._steps.forEach(function(t,e){e<this._currentIndex&&(t.trigger.classList.add("done"),t.content&&t.content.classList.add("done"))}.bind(this)),this._show())},s.previous=function(){this._currentIndex>0&&(this._currentIndex--,this.options.linear&&this._steps.forEach(function(t,e){e>this._currentIndex&&(t.trigger.classList.remove("done"),t.content&&t.content.classList.remove("done"))}.bind(this)),this._show())},s.to=function(t){var e=this._steps[t];e&&(this._currentIndex=this._steps.indexOf(e),this._show())},s.reset=function(){this._currentIndex=0,this._steps.forEach(function(t){t.trigger.classList.remove("done"),t.trigger.classList.remove("active"),t.content&&(t.content.classList.remove("done"),t.content.classList.remove("active"))}),this._show()},s.destroy=function(){this._steps=null,this._currentIndex=0,this._stepsContents=null,this._element=null,this._options=null},n}();return n});
//# sourceMappingURL=bs-stepper.min.js.map
