<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sale_orders', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->date('order_date');
            $table->date('due_date')->nullable();
            $table->string('prefix_code')->nullable();
            $table->string('count_id')->nullable();
            $table->string('order_code')->nullable();
            $table->string('order_status');
            $table->unsignedBigInteger('party_id')->index('sale_orders_party_id_foreign');
            $table->unsignedBigInteger('state_id')->nullable()->index('sale_orders_state_id_foreign');
            $table->text('note')->nullable();
            $table->decimal('round_off', 20, 4)->default(0);
            $table->decimal('grand_total', 20, 4)->default(0);
            $table->decimal('paid_amount', 20, 4)->default(0);
            $table->unsignedBigInteger('currency_id')->nullable()->index('sale_orders_currency_id_foreign');
            $table->decimal('exchange_rate', 20, 4)->default(0);
            $table->unsignedBigInteger('created_by')->nullable()->index('sale_orders_created_by_foreign');
            $table->unsignedBigInteger('updated_by')->nullable()->index('sale_orders_updated_by_foreign');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sale_orders');
    }
};
