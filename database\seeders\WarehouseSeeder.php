<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Warehouse;

class WarehouseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $warehouses = [
            [
                'name' => 'Main',
                'description' => null,
                'status' => 1,
                'is_deletable' => 0,
            ],
            [
                'name' => 'SFAX VILLE',
                'description' => 'SFAX VILLE',
                'status' => 1,
                'is_deletable' => 1,
            ],
        ];

        foreach ($warehouses as $warehouse) {
            Warehouse::firstOrCreate(
                ['name' => $warehouse['name']],
                $warehouse
            );
        }
    }
}
