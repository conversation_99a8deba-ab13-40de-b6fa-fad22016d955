<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('party_payments', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->date('transaction_date');
            $table->unsignedBigInteger('payment_type_id')->index('party_payments_payment_type_id_foreign');
            $table->enum('payment_direction', ['receive', 'pay']);
            $table->unsignedBigInteger('party_id')->index('party_payments_party_id_foreign');
            $table->decimal('amount', 20, 4)->default(0);
            $table->string('reference_no')->nullable();
            $table->text('note')->nullable();
            $table->unsignedBigInteger('created_by')->nullable()->index('party_payments_created_by_foreign');
            $table->unsignedBigInteger('updated_by')->nullable()->index('party_payments_updated_by_foreign');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('party_payments');
    }
};
