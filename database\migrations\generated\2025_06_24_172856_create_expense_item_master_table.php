<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('expense_item_master', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('name');
            $table->text('description')->nullable();
            $table->decimal('unit_price', 20, 4)->default(0);
            $table->unsignedBigInteger('tax_id')->nullable()->index('expense_item_master_tax_id_foreign');
            $table->string('tax_type')->default('inclusive');
            $table->unsignedBigInteger('created_by')->nullable()->index('expense_item_master_created_by_foreign');
            $table->unsignedBigInteger('updated_by')->nullable()->index('expense_item_master_updated_by_foreign');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('expense_item_master');
    }
};
