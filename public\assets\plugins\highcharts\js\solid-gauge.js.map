{"version": 3, "file": "solid-gauge.js.map", "lineCount": 16, "mappings": "A;;;;;;;;;AAUC,SAAS,CAACA,CAAD,CAAU,CACM,QAAtB,GAAI,MAAOC,OAAX,EAAkCA,MAAAC,QAAlC,EACIF,CAAA,CAAQ,SAAR,CACA,CADqBA,CACrB,CAAAC,MAAAC,QAAA,CAAiBF,CAFrB,EAG6B,UAAtB,GAAI,MAAOG,OAAX,EAAoCA,MAAAC,IAApC,CACHD,MAAA,CAAO,gCAAP,CAAyC,CAAC,YAAD,CAAe,4BAAf,CAAzC,CAAuF,QAAS,CAACE,CAAD,CAAa,CACzGL,CAAA,CAAQK,CAAR,CACAL,EAAAK,WAAA,CAAqBA,CACrB,OAAOL,EAHkG,CAA7G,CADG,CAOHA,CAAA,CAA8B,WAAtB,GAAA,MAAOK,WAAP,CAAoCA,UAApC,CAAiDC,IAAAA,EAAzD,CAXY,CAAnB,CAAA,CAaC,QAAS,CAACD,CAAD,CAAa,CAEpBE,QAASA,EAAe,CAACC,CAAD,CAAMC,CAAN,CAAYC,CAAZ,CAAkBC,CAAlB,CAAsB,CACrCH,CAAAI,eAAA,CAAmBH,CAAnB,CAAL,GACID,CAAA,CAAIC,CAAJ,CADJ,CACgBE,CAAAE,MAAA,CAAS,IAAT,CAAeH,CAAf,CADhB,CAD0C,CAD1CI,CAAAA,CAAWT,CAAA,CAAaA,CAAAS,SAAb,CAAmC,EAMlDP,EAAA,CAAgBO,CAAhB,CAA0B,4BAA1B,CAAwD,CAACA,CAAA,CAAS,eAAT,CAAD,CAA4BA,CAAA,CAAS,iBAAT,CAA5B,CAAyDA,CAAA,CAAS,wBAAT,CAAzD;AAA6FA,CAAA,CAAS,mBAAT,CAA7F,CAAxD,CAAqL,QAAS,CAACC,CAAD,CAAQC,CAAR,CAAWC,CAAX,CAA8BC,CAA9B,CAAiC,CAY3N,IAAIC,EAAQJ,CAAAK,MAAZ,CACIC,EAAQH,CAAAG,MADZ,CAEIC,EAASJ,CAAAI,OAFb,CAGIC,EAAWL,CAAAK,SAHf,CAIIC,EAAQN,CAAAM,MAJZ,CAKIC,EAAOP,CAAAO,KALX,CAMIC,EAAOR,CAAAQ,KACPC,EAAAA,CAAaT,CAAAS,WACbC,EAAAA,CAAOV,CAAAU,KAmCXA,EAAA,CAzBeZ,CAAAa,SAyBVC,UAAAC,QAAL,CAAiC,KAAjC,CAAwC,QAAS,CAACC,CAAD,CAAUC,CAAV,CAAaC,CAAb,CAAgBC,CAAhB,CAAmBC,CAAnB,CAAsBC,CAAtB,CAA+B,CAExE5B,CAAAA,CADMuB,CACC,CAAIC,CAAJ,CACPC,CADO,CAEPC,CAFO,CAGPC,CAHO,CAIPC,CAJO,CAKPA,EAAAC,QAAJ,GAEQC,CAGJ,GAJQF,CAAAG,EAIR,EAJqBL,CAIrB,GAHmBE,CAAAI,OAGnB,EAHqC,CAGrC,GAH2C,CAG3C,CAFIC,CAEJ,CAFoBjC,CAAA,CAAK,CAAL,CAEpB,CADIkC,CACJ,CADoBlC,CAAA,CAAK,CAAL,CACpB,CAAyB,GAAzB,GAAIiC,CAAA,CAAc,CAAd,CAAJ,EAAqD,GAArD,GAAgCC,CAAA,CAAc,CAAd,CAAhC,GACoGC,CAGhG,CAH6G,CAAC,GAAD,CAAML,CAAN,CAAcA,CAAd,CAAsB,CAAtB,CAAyB,CAAzB,CAA4B,CAA5B,CAApGG,CAAAG,CAAc,CAAdA,CAAoG,CAA7EH,CAAAI,CAAc,CAAdA,CAA6E,CAG7G,CADArC,CAAA,CAAK,CAAL,CACA,CAHgKsC,CAAC,GAADA,CAAMR,CAANQ,CAAcR,CAAdQ,CAAsB,CAAtBA,CAAyB,CAAzBA,CAA4B,CAA5BA,CAAzGJ,CAAAK,CAAc,CAAdA,CAAyGD,CAAlFJ,CAAAM,CAAc,CAAdA,CAAkFF,CAGhK,CAAAtC,CAAA,CAAK,CAAL,CAAA,CAAUmC,CAJd,CALJ,CAYA,OAAOnC,EAnBqE,CAAhF,CAwBA,KAAIyC,CACH,UAAS,CAACA,CAAD,CAAiB,CAkBvB,IAAIC,EAAU,CACNC,gBAAiBA,QAAS,CAACC,CAAD,CAAc,CAAA,IAChCC,EAAQ,IAAAA,MADwB,CAE5CC,CAF4C,CAG5CC,EAAe,CAH6B,CAI5CnB,EAAU,IAAAA,QACN,KAAAkB,YAAA,CAAmBA,CAAnB,CAAiC,EACjCF,EAAAE,YAAAE,QAAA,CAAgC,QAAS,CAACC,CAAD;AAAYC,CAAZ,CAAe,CAEpDD,CAAA,CAAYlC,CAAA,CAAMkC,CAAN,CACZH,EAAAK,KAAA,CAAiBF,CAAjB,CACKA,EAAAvC,MAAL,GACmC,UAA/B,GAAIkB,CAAAwB,eAAJ,EACIC,CAGA,CAHSR,CAAAjB,QAAAyB,OAGT,CAFAJ,CAAAvC,MAEA,CAFkB2C,CAAA,CAAON,CAAA,EAAP,CAElB,CAAIA,CAAJ,GAAqBM,CAAAC,OAArB,GACIP,CADJ,CACmB,CADnB,CAJJ,EASIE,CAAAvC,MATJ,CASsBA,CAAA,CAAMkB,CAAA2B,SAAN,CAAAC,QAAA,CAAgC9C,CAAA,CAAMkB,CAAA6B,SAAN,CAAhC,CAAyDP,CAAzD,EAA8DN,CAAAE,YAAAQ,OAA9D,CAA+F,CAA/F,EAV1B,CAJoD,CAAxD,CANwC,CADlC,CA0BVI,UAAWA,QAAS,CAACd,CAAD,CAAc,CAC9B,IAAAe,MAAA,CAAaf,CAAAe,MAAb,EAAkC,CAC9B,CAAC,CAAD,CAAI,IAAA/B,QAAA2B,SAAJ,CAD8B,CAE9B,CAAC,CAAD,CAAI,IAAA3B,QAAA6B,SAAJ,CAF8B,CAIlC,KAAAE,MAAAX,QAAA,CAAmB,QAAS,CAACY,CAAD,CAAO,CAC/BA,CAAAlD,MAAA,CAAaA,CAAA,CAAMkD,CAAA,CAAK,CAAL,CAAN,CADkB,CAAnC,CAL8B,CA1BxB,CAoCVC,QAASA,QAAS,CAACC,CAAD,CAAQC,CAAR,CAAe,CAAA,IAEzBJ,EAAQ,IAAAA,MAFiB,CAMzBb,EAAc,IAAAA,YANW,CAQzBI,CACJ,IAAIJ,CAAJ,CAEI,IADAI,CACA,CADIJ,CAAAQ,OACJ,CAAOJ,CAAA,EAAP,CAAA,CAAY,CACR,IAAAD,EAAYH,CAAA,CAAYI,CAAZ,CACZ,KAAAc,EAAOf,CAAAe,KACPC,EAAA,CAAKhB,CAAAgB,GACL,KAAqB,WAArB,GAAK,MAAOD,EAAZ,EAAoCF,CAApC,EAA6CE,CAA7C,IACmB,WADnB;AACK,MAAOC,EADZ,EACkCH,CADlC,EAC2CG,CAD3C,EACgD,CAC5C,IAAAvD,EAAQuC,CAAAvC,MACJqD,EAAJ,GACIA,CAAAd,UADJ,CACsBC,CADtB,CAGA,MAL4C,CALxC,CAFhB,IAgBK,CACG,IAAAgB,YAAJ,GACIJ,CADJ,CACY,IAAAK,QAAA,CAAaL,CAAb,CADZ,CAGAM,EAAA,CAAM,CAAN,EAAY,IAAAC,IAAZ,CAAuBP,CAAvB,GAAiC,IAAAO,IAAjC,CAA4C,IAAAC,IAA5C,CAEA,KADApB,CACA,CADIS,CAAAL,OACJ,CAAOJ,CAAA,EAAP,EACQ,EAAAkB,CAAA,CAAMT,CAAA,CAAMT,CAAN,CAAA,CAAS,CAAT,CAAN,CADR,CAAA,EAKAc,CAAA,CAAOL,CAAA,CAAMT,CAAN,CAAP,EAAmBS,CAAA,CAAMT,CAAN,CAAU,CAAV,CACnBe,EAAA,CAAKN,CAAA,CAAMT,CAAN,CAAU,CAAV,CAAL,EAAqBc,CAErBI,EAAA,CAAO,CAAP,EAAYH,CAAA,CAAG,CAAH,CAAZ,CAAoBG,CAApB,GAA6BH,CAAA,CAAG,CAAH,CAA7B,CACID,CAAA,CAAK,CAAL,CADJ,EACgB,CADhB,CAEAtD,EAAA,CAAQsD,CAAAtD,MAAA8C,QAAA,CAAmBS,CAAAvD,MAAnB,CAA6B0D,CAA7B,CAhBP,CAkBL,MAAO1D,EA3CsB,CApCvB,CA6Fd+B,EAAA8B,KAAA,CAHAA,QAAa,CAACC,CAAD,CAAO,CAChB3D,CAAA,CAAO2D,CAAP,CAAa9B,CAAb,CADgB,CA5GG,CAA1B,CAAD,CAgHGD,CAhHH,GAgHsBA,CAhHtB,CAgHuC,EAhHvC,EAkNAvB,EAAA,CAAW,YAAX,CAAyB,OAAzB,CApFwBuD,CA8EhBC,aAAc,CAAA,CA9EED,CA+EhBE,WAAY,CACRlD,EAAG,CADK,CA/EIgD,CAoFxB,CAAqD,CACjDG,iBAAkBpE,CAAAqE,cAD+B,CAIjDC,UAAWA,QAAS,EAAG,CACnB,IAAIN,EAAO,IAAAO,MACXtC,EAAA8B,KAAA,CAAoBC,CAApB,CAEI,EAACA,CAAA1B,YAAL,EAAyB0B,CAAA5C,QAAAkB,YAAzB,EACI0B,CAAA7B,gBAAA,CAAqB6B,CAAA5C,QAArB,CAEJ4C;CAAAd,UAAA,CAAec,CAAA5C,QAAf,CAEArB,EAAAyE,YAAAC,MAAA5D,UAAAyD,UAAAI,KAAA,CAA6C,IAA7C,CATmB,CAJ0B,CAgBjDC,WAAYA,QAAS,EAAG,CAAA,IAChBC,EAAS,IADO,CAEhBL,EAAQK,CAAAL,MAFQ,CAGhBM,EAASN,CAAAM,OAHO,CAIhBzD,EAAUwD,CAAAxD,QAJM,CAKhB0D,EAAWF,CAAAvC,MAAAyC,SALK,CAMhBC,EAAY3D,CAAA2D,UANI,CAOhBC,EAAe1E,CAAA,CAASyE,CAAT,CAAA,CACXA,CADW,CACC,GADD,CACOE,IAAAC,GADP,CAEX,CATY,CAUhBC,CAEA7E,EAAA,CAASc,CAAAgE,UAAT,CAAJ,GACID,CADJ,CACwBZ,CAAAc,cADxB,CAC8Cd,CAAAD,UAAA,CAAgBlD,CAAAgE,UAAhB,CAAmC,IAAnC,CAAyC,IAAzC,CAA+C,IAA/C,CAAqD,CAAA,CAArD,CAD9C,CAGA,KAAAD,kBAAA,CAAyB3E,CAAA,CAAK2E,CAAL,CAAwBZ,CAAAc,cAAxB,CACzBT,EAAAU,OAAA9C,QAAA,CAAsB,QAAS,CAACe,CAAD,CAAQ,CAEnC,GAAI,CAACA,CAAAgC,OAAL,CAAmB,CAAA,IACXC,EAAUjC,CAAAiC,QADC,CAEXC,EAAYlB,CAAAc,cAAZI,CACIlB,CAAAD,UAAA,CAAgBf,CAAAtC,EAAhB,CACJ,IADI,CAEJ,IAFI,CAGJ,IAHI,CAIJ,CAAA,CAJI,CAHO,CAQXyE,EAAWjF,CAAA,CAAKD,CAAA,CAAK+C,CAAAnC,QAAAsE,OAAL,CAChBtE,CAAAsE,OADgB,CACA,GADA,CAAL,CAAXA,CACwBb,CAAA,CAAO,CAAP,CADxBa,CACqC,GAT1B,CAUXC,EAAgBlF,CAAA,CAAKD,CAAA,CAAK+C,CAAAnC,QAAAuE,YAAL;AACrBvE,CAAAuE,YADqB,CACA,EADA,CAAL,CAAhBA,CAC4Bd,CAAA,CAAO,CAAP,CAD5Bc,CACyC,GAX9B,CAcXtC,EAAUkB,CAAAlB,QAAA,CAAcE,CAAAtC,EAAd,CACVsC,CADU,CAdC,CAgBXqC,EAAeX,IAAAnB,IAAA,CAASS,CAAAc,cAAT,CACfd,CAAAsB,YADe,CAhBJ,CAkBXC,EAAeb,IAAApB,IAAA,CAASU,CAAAc,cAAT,CACfd,CAAAsB,YADe,CAIH,OAAhB,GAAIxC,CAAJ,GACIA,CADJ,CACcE,CAAArD,MADd,EAC6B0E,CAAA1E,MAD7B,EAC6C,MAD7C,CAGgB,OAAhB,GAAImD,CAAJ,GACIE,CAAArD,MADJ,CACkBmD,CADlB,CAIAoC,EAAA,CAAWrF,CAAA,CAAMqF,CAAN,CAAgBG,CAAhB,CAA+BZ,CAA/B,CAA6Cc,CAA7C,CAA4Dd,CAA5D,CAEU,EAAA,CAArB,GAAI5D,CAAAT,KAAJ,GACI8E,CADJ,CACerF,CAAA,CAAMqF,CAAN,CAAgBG,CAAhB,CAA8BE,CAA9B,CADf,CAGAC,EAAA,CAAWd,IAAAnB,IAAA,CAAS2B,CAAT,CAAmBb,CAAAO,kBAAnB,CACXa,EAAA,CAAWf,IAAApB,IAAA,CAAS4B,CAAT,CAAmBb,CAAAO,kBAAnB,CACPa,EAAJ,CAAeD,CAAf,CAA0B,CAA1B,CAA8Bd,IAAAC,GAA9B,GACIc,CADJ,CACeD,CADf,CAC0B,CAD1B,CAC8Bd,IAAAC,GAD9B,CAGA3B,EAAA0C,UAAA,CAAkBA,CAAlB,CAA8B,CAC1BjF,EAAG6D,CAAA,CAAO,CAAP,CADuB,CAE1B5D,EAAG4D,CAAA,CAAO,CAAP,CAFuB,CAG1BtD,EAAGmE,CAHuB,CAI1BlE,OAAQmE,CAJkB,CAK1BO,MAAOH,CALmB,CAM1BI,IAAKH,CANqB,CAO1B3E,QAASD,CAAAC,QAPiB,CAS9BkC,EAAA6C,OAAA,CAAeV,CACXF,EAAJ,EACIa,CAEA,CAFIJ,CAAAI,EAEJ,CADAb,CAAAc,QAAA,CAAgBjG,CAAA,CAAO,CAAEkG,KAAMlD,CAAR,CAAP,CAA0B4C,CAA1B,CAAhB,CACA,CAAII,CAAJ,GACIJ,CAAAI,EADJ,CACkBA,CADlB,CAHJ,EAQI9C,CAAAiC,QARJ,CAQoBA,CARpB,CAQ8BV,CAAA0B,IAAA,CAAaP,CAAb,CAAAQ,KAAA,CAChB,CACNF,KAAMlD,CADA;AAEN,aAAc,CAFR,CADgB,CAAAqD,IAAA,CAKjB9B,CAAA+B,MALiB,CAOzB/B,EAAAvC,MAAAuE,WAAL,GAC4B,QAMxB,GANIxF,CAAAyF,QAMJ,EALIrB,CAAAiB,KAAA,CAAa,CACT,iBAAkB,OADT,CAET,kBAAmB,OAFV,CAAb,CAKJ,CAAAjB,CAAAiB,KAAA,CAAa,CACTK,OAAQ1F,CAAA2F,YAARD,EAA+B,MADtB,CAET,eAAgB1F,CAAA4F,YAAhB,EAAuC,CAF9B,CAAb,CAPJ,CAYIxB,EAAJ,EACIA,CAAAyB,SAAA,CAAiB1D,CAAA2D,aAAA,EAAjB,CAAuC,CAAA,CAAvC,CA7EW,CAFgB,CAAvC,CAhBoB,CAhByB,CAqHjDZ,QAASA,QAAS,CAACvC,CAAD,CAAO,CAChBA,CAAL,GACI,IAAAsB,cACA,CADqB,IAAAF,kBACrB,CAAApF,CAAAyE,YAAA2C,IAAAtG,UAAAyF,QAAA5B,KAAA,CAAyC,IAAzC,CAA+CX,CAA/C,CAFJ,CADqB,CArHwB,CAArD,CA2MA,GAEA,OAAO9B,EA/eoN,CAA/N,CAifA3C,EAAA,CAAgBO,CAAhB,CAA0B,oCAA1B,CAAgE,EAAhE,CAAoE,QAAS,EAAG,EAAhF,CAxfoB,CAbvB;", "sources": ["solid-gauge.src.js"], "names": ["factory", "module", "exports", "define", "amd", "Highcharts", "undefined", "_registerModule", "obj", "path", "args", "fn", "hasOwnProperty", "apply", "_modules", "Color", "H", "LegendSymbolMixin", "U", "color", "parse", "clamp", "extend", "isNumber", "merge", "pick", "pInt", "seriesType", "wrap", "<PERSON><PERSON><PERSON>", "prototype", "symbols", "proceed", "x", "y", "w", "h", "options", "rounded", "smallR", "r", "innerR", "outerArcStart", "innerArcStart", "roundStart", "x1", "y1", "roundEnd", "x2", "y2", "SolidGaugeAxis", "methods", "initDataClasses", "userOptions", "chart", "dataClasses", "colorCounter", "for<PERSON>ach", "dataClass", "i", "push", "dataClassColor", "colors", "length", "minColor", "tweenTo", "maxColor", "initStops", "stops", "stop", "toColor", "value", "point", "from", "to", "logarithmic", "val2lin", "pos", "max", "min", "init", "axis", "solidGaugeOptions", "colorByPoint", "dataLabels", "drawLegendSymbol", "drawRectangle", "translate", "yAxis", "seriesTypes", "gauge", "call", "drawPoints", "series", "center", "renderer", "overshoot", "overshootVal", "Math", "PI", "thresholdAngleRad", "threshold", "startAngleRad", "points", "isNull", "graphic", "rotation", "radius", "innerRadius", "axisMinAngle", "endAngleRad", "axisMaxAngle", "minAngle", "maxAngle", "shapeArgs", "start", "end", "startR", "d", "animate", "fill", "arc", "attr", "add", "group", "styledMode", "linecap", "stroke", "borderColor", "borderWidth", "addClass", "getClassName", "pie"]}