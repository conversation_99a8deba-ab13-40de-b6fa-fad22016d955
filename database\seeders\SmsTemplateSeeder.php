<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\SmsTemplate;

class SmsTemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $templates = [
            [
                'name' => 'SALE INVOICE',
                'content' => "Dear [Customer Name],\n\nYour invoice for [Invoice Number] is attached.\n\nTotal: [Total Amount]\nPaid: [Paid Amount]\nDue: [Balance Amount]\n\nContact us at [Your Mobile Number] or [Your Email Address] for questions.\n\nThanks,\n[Your Company Name]",
                'keys' => "[Invoice Number]\n\n[Customer Name]\n\n[Sale Date]\n\n[Due Date]\n\n[Total Amount]\n\n[Paid Amount]\n\n[Balance Amount]\n\n[Your Email Address]\n\n[Your Mobile Number]\n\n[Your Company Name]",
                'delete_flag' => 1
            ],
            [
                'name' => 'SALE ORDER',
                'content' => "Dear [Customer Name],\n\nYour sale order details for [Order Number] are attached.\n\nTotal: [Total Amount]\nPaid: [Paid Amount]\nDue: [Balance Amount]\n\nContact us at [Your Mobile Number] or [Your Email Address] for questions.\n\nThanks,\n[Your Company Name]",
                'keys' => "[Order Number]\n\n[Customer Name]\n\n[Order Date]\n\n[Due Date]\n\n[Total Amount]\n\n[Paid Amount]\n\n[Balance Amount]\n\n[Your Email Address]\n\n[Your Mobile Number]\n\n[Your Company Name]",
                'delete_flag' => 1
            ],
            [
                'name' => 'SALE RETURN',
                'content' => "Dear [Customer Name],\n\nYour sale return details for [Return Number] are attached.\n\nTotal: [Total Amount]\nReturned: [Return Amount]\nDue: [Balance Amount]\n\nContact us at [Your Mobile Number] or [Your Email Address] for questions.\n\nThanks,\n[Your Company Name]",
                'keys' => "[Return Number]\n\n[Customer Name]\n\n[Return Date]\n\n[Total Amount]\n\n[Return Amount]\n\n[Balance Amount]\n\n[Your Email Address]\n\n[Your Mobile Number]\n\n[Your Company Name]",
                'delete_flag' => 1
            ],
            [
                'name' => 'PURCHASE BILL',
                'content' => "Dear [Supplier Name],\n\nPlease find attached the invoice for your recent purchase.\n\nBill Details:\n\n   Bill Number: [Bill Number]\n   Purchase Date: [Purchase Date]\n   Total: [Total Amount]\n   Paid Amount: [Paid Amount]\n   Bill Balance: [Balance Amount]\n\nIf you have any questions or require further assistance, please don't hesitate to contact us at [Your Email Address] or [Your Mobile Number].\n\nThank you for your business.\n\nSincerely,\n[Your Company Name]\n[Your Mobile Number]",
                'keys' => "[Bill Number]\n\n[Supplier Name]\n\n[Purchase Date]\n\n[Total Amount]\n\n[Paid Amount]\n\n[Balance Amount]\n\n[Your Email Address]\n\n[Your Mobile Number]\n\n[Your Company Name]",
                'delete_flag' => 1
            ],
            [
                'name' => 'PURCHASE ORDER',
                'content' => "Dear [Supplier Name],\n\nYour Purchase order details for [Order Number] are attached.\n\nTotal: [Total Amount]\nPaid: [Paid Amount]\nDue: [Balance Amount]\n\nContact us at [Your Mobile Number] or [Your Email Address] for questions.\n\nThanks,\n[Your Company Name]",
                'keys' => "[Order Number]\n\n[Customer Name]\n\n[Order Date]\n\n[Due Date]\n\n[Total Amount]\n\n[Paid Amount]\n\n[Balance Amount]\n\n[Your Email Address]\n\n[Your Mobile Number]\n\n[Your Company Name]",
                'delete_flag' => 1
            ],
            [
                'name' => 'PURCHASE RETURN',
                'content' => "Dear [Supplier Name],\n\nYour purchase return details for [Return Number] are attached.\n\nTotal: [Total Amount]\nReturned: [Return Amount]\nDue: [Balance Amount]\n\nContact us at [Your Mobile Number] or [Your Email Address] for questions.\n\nThanks,\n[Your Company Name]",
                'keys' => "[Return Number]\n\n[Supplier Name]\n\n[Return Date]\n\n[Total Amount]\n\n[Return Amount]\n\n[Balance Amount]\n\n[Your Email Address]\n\n[Your Mobile Number]\n\n[Your Company Name]",
                'delete_flag' => 1
            ],
            [
                'name' => 'QUOTATION',
                'content' => "Dear [Customer Name],\n\nYour Quotation details for [Quotation Number] are attached.\n\nTotal: [Total Amount]\n\nContact us at [Your Mobile Number] or [Your Email Address] for questions.\n\nThanks,\n[Your Company Name]",
                'keys' => "[Quotation Number]\n\n[Customer Name]\n\n[Quotation Date]\n\n[Total Amount]\n\n[Balance Amount]\n\n[Your Email Address]\n\n[Your Mobile Number]\n\n[Your Company Name]",
                'delete_flag' => 1
            ]
        ];

        foreach ($templates as $template) {
            SmsTemplate::firstOrCreate(
                ['name' => $template['name']],
                $template
            );
        }
    }
} 