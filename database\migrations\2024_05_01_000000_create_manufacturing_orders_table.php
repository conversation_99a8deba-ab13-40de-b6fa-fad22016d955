<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up()
    {
        Schema::create('manufacturing_orders', function (Blueprint $table) {
            $table->id();
            $table->string('order_number')->unique();
            $table->date('order_date');
            $table->date('delivery_date')->nullable();
            $table->string('designation');
            $table->string('tissus');
            $table->decimal('largeur_fini', 8, 2);
            $table->string('mecanisme');
            $table->decimal('qte_to_billed', 8, 2);
            $table->string('billed_unity');
            $table->decimal('unit_price', 10, 2);
            $table->decimal('extras_fees', 10, 2)->nullable();
            $table->decimal('qty', 8, 2);
            $table->enum('order_status', ['Draft', 'In Progress', 'Completed', 'Delivered'])->default('Draft');
            $table->decimal('fabric_requirement', 8, 2);
            $table->enum('curtain_type', ['Store', 'Rideaux']);
            $table->enum('curtain_finish', ['Wave', 'PP', 'PF']);
            $table->enum('manipulation', ['Moteur', 'Manuelle']);
            $table->enum('height_type', ['simple', 'double']);
            $table->enum('positioning_type', ['lateral', 'central'])->nullable();
            $table->decimal('sous_plafond', 8, 2)->nullable();
            $table->decimal('hauteur_finale', 8, 2)->nullable();
            $table->string('formule_appliquee')->nullable();
            $table->integer('nombre_agrafes')->nullable();
            $table->text('notes')->nullable();
            $table->unsignedBigInteger('created_by');
            $table->timestamps();
            $table->foreign('created_by')->references('id')->on('users')->onDelete('cascade');
        });
    }

    public function down()
    {
        Schema::dropIfExists('manufacturing_orders');
    }
}; 