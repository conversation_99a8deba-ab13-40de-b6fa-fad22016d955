<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('item_batch_transactions', function (Blueprint $table) {
            $table->foreign(['item_batch_master_id'])->references(['id'])->on('item_batch_masters')->onUpdate('no action')->onDelete('no action');
            $table->foreign(['item_id'])->references(['id'])->on('items')->onUpdate('no action')->onDelete('cascade');
            $table->foreign(['item_transaction_id'])->references(['id'])->on('item_transactions')->onUpdate('no action')->onDelete('cascade');
            $table->foreign(['warehouse_id'])->references(['id'])->on('warehouses')->onUpdate('no action')->onDelete('no action');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('item_batch_transactions', function (Blueprint $table) {
            $table->dropForeign('item_batch_transactions_item_batch_master_id_foreign');
            $table->dropForeign('item_batch_transactions_item_id_foreign');
            $table->dropForeign('item_batch_transactions_item_transaction_id_foreign');
            $table->dropForeign('item_batch_transactions_warehouse_id_foreign');
        });
    }
};
