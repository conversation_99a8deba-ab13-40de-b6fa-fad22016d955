<?php

return [
    // Page titles
    'manufacturing' => 'Manufacturing',
    'manufacturing_orders' => 'Manufacturing Orders',
    'create_order' => 'Create Order',
    
    // Form Labels
    'order_number' => 'Order Number',
    'order_date' => 'Order Date',
    'delivery_date' => 'Delivery Date',
    'designation' => 'Designation',
    'tissus' => 'Fabric',
    'mecanisme' => 'Mechanism',
    'order_status' => 'Order Status',
    'largeur_fini' => 'Finished Width',
    'sous_plafond' => 'Under Ceiling',
    'curtain_type' => 'Curtain Type',
    'curtain_finish' => 'Curtain Finish',
    'manipulation' => 'Manipulation',
    'height_type' => 'Height Type',
    'positioning_type' => 'Positioning Type',
    'hauteur_finale' => 'Final Height',
    'formule_appliquee' => 'Applied Formula',
    'nombre_agrafes' => 'Number of Staples',
    'qte_to_billed' => 'Quantity to Bill',
    'billed_unity' => 'Billing Unit',
    'unit_price' => 'Unit Price',
    'extras_fees' => 'Extra Fees',
    'fabric_requirement' => 'Fabric Requirement',
    'qty' => 'Quantity',
    'total_amount' => 'Total Amount',
    'notes' => 'Notes',
    'select_positioning_type' => 'Select positioning type',

    // Select Options
    'status_options' => [
        'draft' => 'Draft',
        'in_progress' => 'In Progress',
        'completed' => 'Completed',
        'delivered' => 'Delivered'
    ],
    'curtain_types' => [
        'store' => 'Store',
        'rideaux' => 'Curtains'
    ],
    'curtain_finishes' => [
        'wave' => 'Wave',
        'pp' => 'PP',
        'pf' => 'PF'
    ],
    'manipulation_types' => [
        'moteur' => 'Motor',
        'manuelle' => 'Manual'
    ],
    'height_types' => [
        'simple' => 'Simple',
        'double' => 'Double'
    ],
    'positioning_types' => [
        'lateral' => 'Lateral',
        'central' => 'Central'
    ],
    'mechanism_types' => [
        'br' => 'BR',
        'ghd' => 'GHD'
    ],

    // Steps
    'basic_info' => 'Basic Info',
    'dimensions' => 'Dimensions',
    'curtain_details' => 'Curtain Details',
    'pricing_additional_info' => 'Pricing & Additional Info',

    // Buttons
    'next' => 'Next',
    'previous' => 'Previous',
    'submit' => 'Submit',
    'close' => 'Close',

    // Messages
    'success_create' => 'Manufacturing order created successfully.',
    'success_update' => 'Manufacturing order updated successfully.',
    'success_delete' => 'Manufacturing order deleted successfully.',
    'confirm_submit' => 'Are you sure you want to submit this manufacturing order?',
    'confirm_delete' => 'Are you sure you want to delete this manufacturing order?',
]; 