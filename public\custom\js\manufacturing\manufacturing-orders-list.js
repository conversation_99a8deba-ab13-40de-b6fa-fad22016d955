$(document).ready(function() {
    let currentPage = 1;
    let filters = {};

    // Initialize date range picker
    $('#dateRange').daterangepicker({
        locale: {
            format: 'YYYY-MM-DD',
            separator: ' - ',
            applyLabel: 'Appliquer',
            cancelLabel: 'Annuler',
            fromLabel: 'De',
            toLabel: 'À',
            customRangeLabel: 'Personnalisé',
            weekLabel: 'S',
            daysOfWeek: ['Di', 'Lu', 'Ma', 'Me', 'Je', 'Ve', 'Sa'],
            monthNames: ['Janvier', 'Février', 'Mars', 'Avril', '<PERSON>', 'Juin',
                        'Ju<PERSON>t', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'],
            firstDay: 1
        },
        autoUpdateInput: false
    });

    $('#dateRange').on('apply.daterangepicker', function(ev, picker) {
        $(this).val(picker.startDate.format('YYYY-MM-DD') + ' - ' + picker.endDate.format('YYYY-MM-DD'));
        filters.dateRange = $(this).val();
        loadOrders(1);
    });

    // Handle filter changes
    $('#statusFilter, #typeFilter').on('change', function() {
        filters.status = $('#statusFilter').val();
        filters.type = $('#typeFilter').val();
        loadOrders(1);
    });

    // Handle search
    let searchTimeout;
    $('#searchInput').on('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            filters.search = $(this).val();
            loadOrders(1);
        }, 500);
    });

    // Reset filters
    $('#resetFilters').on('click', function() {
        $('#dateRange').val('');
        $('#statusFilter').val('');
        $('#typeFilter').val('');
        $('#searchInput').val('');
        filters = {
            dateRange: '',
            status: '',
            type: '',
            search: ''
        };
        loadOrders(1);
    });

    // Load orders with server-side pagination
    function loadOrders(page) {
        currentPage = page;
        $.ajax({
            url: `${baseUrl}/manufacturing/orders/list`,
            type: 'GET',
            data: {
                page: page,
                ...filters
            },
            success: function(response) {
                renderOrders(response.data);
                renderPagination(response.meta);
                updatePaginationInfo(response.meta);
            },
            error: function(xhr) {
                Swal.fire({
                    title: appTranslations.error,
                    text: appTranslations.failed_to_load_orders,
                    icon: 'error'
                });
            }
        });
    }

    // Render orders as cards
    function renderOrders(orders) {
        const ordersList = $('#ordersList');
        ordersList.empty();

        orders.forEach(order => {
            const card = $(`
                <div class="col-12">
                    <div class="card order-card status-${order.order_status.replace(' ', '-')}">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-3">
                                <div class="card-header-content">
                                    <button class="expand-btn" data-id="${order.id}">
                                        <i class="bx bx-chevron-down"></i>
                                    </button>
                                    <div class="order-meta">
                                        <div>
                                            <h6 class="card-title mb-1">#${order.order_number}</h6>
                                            <small class="text-muted">${moment(order.order_date).format('DD MMM YYYY')}</small>
                                        </div>
                                        <div>
                                            <span class="badge bg-${getStatusColor(order.order_status)}">${getTranslatedStatus(order.order_status)}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="action-buttons">
                                    <a href="${baseUrl}/manufacturing/orders/${order.id}/edit" 
                                       class="btn btn-primary" title="${appTranslations.edit}">
                                        <i class="bx bx-edit"></i>
                                    </a>
                                    <button type="button" class="btn btn-info view-details" 
                                            data-id="${order.id}" title="${appTranslations.view}">
                                        <i class="bx bx-show"></i>
                                    </button>
                                    <button type="button" class="btn btn-danger delete-order" 
                                            data-id="${order.id}" title="${appTranslations.delete}">
                                        <i class="bx bx-trash"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="order-info">
                                <div class="order-info-item">
                                    <span class="order-info-label">${appTranslations.designation}</span>
                                    <span class="order-info-value">${order.designation}</span>
                                </div>
                                <div class="order-info-item">
                                    <span class="order-info-label">${appTranslations.tissus}</span>
                                    <span class="order-info-value">${order.tissus}</span>
                                </div>
                                <div class="order-info-item">
                                    <span class="order-info-label">${appTranslations.curtain_type}</span>
                                    <span class="order-info-value">
                                        <i class="bx bx-package"></i> ${order.curtain_type}
                                    </span>
                                </div>
                                <div class="order-info-item">
                                    <span class="order-info-label">${appTranslations.delivery_date}</span>
                                    <span class="order-info-value">
                                        ${order.delivery_date ? moment(order.delivery_date).format('DD MMM YYYY') : appTranslations.not_available}
                                    </span>
                                </div>
                                <div class="order-info-item">
                                    <span class="order-info-label">${appTranslations.created_by}</span>
                                    <span class="order-info-value">
                                        ${order.creator ? order.creator.name : appTranslations.not_available}
                                    </span>
                                </div>
                                <div class="order-info-item">
                                    <span class="order-info-label">${appTranslations.total_amount}</span>
                                    <span class="order-info-value">
                                        ${calculateTotalAmount(order)}
                                    </span>
                                </div>
                            </div>

                            <div class="order-details mt-3" id="details-${order.id}"></div>
                        </div>
                    </div>
                </div>
            `);
            ordersList.append(card);
        });
    }

    // Handle expand/collapse
    $(document).on('click', '.expand-btn', function() {
        const orderId = $(this).data('id');
        const icon = $(this).find('i');
        const detailsDiv = $(`#details-${orderId}`);

        if (icon.hasClass('bx-chevron-down')) {
            // Expand
            $.ajax({
                url: `${baseUrl}/manufacturing/orders/${orderId}/details`,
                type: 'GET',
                success: function(response) {
                    icon.removeClass('bx-chevron-down').addClass('bx-chevron-up');
                    detailsDiv.html(renderOrderDetails(response)).slideDown();
                }
            });
        } else {
            // Collapse
            icon.removeClass('bx-chevron-up').addClass('bx-chevron-down');
            detailsDiv.slideUp();
        }
    });

    // Handle view details
    $(document).on('click', '.view-details', function() {
        const orderId = $(this).data('id');
        $.ajax({
            url: `${baseUrl}/manufacturing/orders/${orderId}/details`,
            type: 'GET',
            success: function(response) {
                $('#orderDetailsContent').html(renderOrderDetails(response));
                $('#orderDetailsModal').modal('show');
            }
        });
    });

    // Handle delete
    $(document).on('click', '.delete-order', function() {
        const orderId = $(this).data('id');
        Swal.fire({
            title: appTranslations.confirm_delete,
            text: appTranslations.delete_warning,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: appTranslations.yes_delete,
            cancelButtonText: appTranslations.cancel
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: `${baseUrl}/manufacturing/orders/${orderId}`,
                    type: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        Swal.fire(
                            appTranslations.deleted,
                            appTranslations.delete_success,
                            'success'
                        );
                        loadOrders(1);
                    },
                    error: function(xhr) {
                        Swal.fire(
                            appTranslations.error,
                            appTranslations.delete_error,
                            'error'
                        );
                    }
                });
            }
        });
    });

    // Render pagination
    function renderPagination(meta) {
        const pagination = $('#pagination');
        pagination.empty();

        if (meta.last_page <= 1) return;

        // Previous button
        if (meta.current_page > 1) {
            pagination.append(`
                <li class="page-item">
                    <a class="page-link" href="#" data-page="${meta.current_page - 1}">
                        ${appTranslations.previous}
                    </a>
                </li>
            `);
        }

        // Page numbers
        const startPage = Math.max(1, meta.current_page - 2);
        const endPage = Math.min(meta.last_page, meta.current_page + 2);

        if (startPage > 1) {
            pagination.append(`<li class="page-item"><a class="page-link" href="#" data-page="1">1</a></li>`);
            if (startPage > 2) {
                pagination.append(`<li class="page-item disabled"><span class="page-link">...</span></li>`);
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            const activeClass = i === meta.current_page ? 'active' : '';
            pagination.append(`
                <li class="page-item ${activeClass}">
                    <a class="page-link" href="#" data-page="${i}">${i}</a>
                </li>
            `);
        }

        if (endPage < meta.last_page) {
            if (endPage < meta.last_page - 1) {
                pagination.append(`<li class="page-item disabled"><span class="page-link">...</span></li>`);
            }
            pagination.append(`<li class="page-item"><a class="page-link" href="#" data-page="${meta.last_page}">${meta.last_page}</a></li>`);
        }

        // Next button
        if (meta.current_page < meta.last_page) {
            pagination.append(`
                <li class="page-item">
                    <a class="page-link" href="#" data-page="${meta.current_page + 1}">
                        ${appTranslations.next}
                    </a>
                </li>
            `);
        }

        // Handle pagination clicks
        pagination.find('a.page-link').on('click', function(e) {
            e.preventDefault();
            const page = $(this).data('page');
            if (page) {
                loadOrders(page);
            }
        });
    }

    // Update pagination info
    function updatePaginationInfo(meta) {
        $('#pagination-info').text(
            `${appTranslations.showing} ${meta.from} ${appTranslations.to} ${meta.to} ${appTranslations.of} ${meta.total} ${appTranslations.entries}`
        );
    }

    // Helper functions
    function getStatusColor(status) {
        const colors = {
            'Draft': 'secondary',
            'In Progress': 'primary',
            'Completed': 'success',
            'Delivered': 'info'
        };
        return colors[status] || 'secondary';
    }

    function getTranslatedStatus(status) {
        return appTranslations.status_translations[status] || status;
    }

    function calculateTotalAmount(order) {
        const qteToBeilled = parseFloat(order.qte_to_billed) || 0;
        const unitPrice = parseFloat(order.unit_price) || 0;
        const extrasFees = parseFloat(order.extras_fees) || 0;
        const total = (qteToBeilled * unitPrice) + extrasFees;
        return total.toFixed(2);
    }

    // Initial load
    loadOrders(1);
}); 