{"__meta": {"id": "X3526435e4e1ffbbf73516706f6c0b1b8", "datetime": "2025-06-27 17:55:11", "utime": **********.9103, "method": "POST", "uri": "/manufacturing/calculations/get-rule", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 21, "messages": [{"message": "[17:55:11] LOG.info: getRule request: {\n    \"height_type\": \"simple\",\n    \"curtain_finish\": \"Wave\",\n    \"manipulation\": \"Moteur\",\n    \"positioning_type\": \"lateral\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.897487, "xdebug_link": null, "collector": "log"}, {"message": "[17:55:11] LOG.info: Available rules count: {\n    \"count\": 18\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.899875, "xdebug_link": null, "collector": "log"}, {"message": "[17:55:11] LOG.info: Available rule: {\n    \"id\": 1,\n    \"height_type\": \"simple\",\n    \"curtain_finish\": \"PP\",\n    \"manipulation\": \"Moteur\",\n    \"positioning_type\": \"lateral\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.900017, "xdebug_link": null, "collector": "log"}, {"message": "[17:55:11] LOG.info: Available rule: {\n    \"id\": 2,\n    \"height_type\": \"simple\",\n    \"curtain_finish\": \"PP\",\n    \"manipulation\": \"Moteur\",\n    \"positioning_type\": \"central\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.900087, "xdebug_link": null, "collector": "log"}, {"message": "[17:55:11] LOG.info: Available rule: {\n    \"id\": 3,\n    \"height_type\": \"simple\",\n    \"curtain_finish\": \"PP\",\n    \"manipulation\": \"<PERSON><PERSON>\",\n    \"positioning_type\": \"lateral\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.900148, "xdebug_link": null, "collector": "log"}, {"message": "[17:55:11] LOG.info: Available rule: {\n    \"id\": 4,\n    \"height_type\": \"simple\",\n    \"curtain_finish\": \"PP\",\n    \"manipulation\": \"<PERSON><PERSON>\",\n    \"positioning_type\": \"central\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.900208, "xdebug_link": null, "collector": "log"}, {"message": "[17:55:11] LOG.info: Available rule: {\n    \"id\": 5,\n    \"height_type\": \"simple\",\n    \"curtain_finish\": \"PF\",\n    \"manipulation\": \"Moteur\",\n    \"positioning_type\": \"lateral\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.900267, "xdebug_link": null, "collector": "log"}, {"message": "[17:55:11] LOG.info: Available rule: {\n    \"id\": 6,\n    \"height_type\": \"simple\",\n    \"curtain_finish\": \"PF\",\n    \"manipulation\": \"Moteur\",\n    \"positioning_type\": \"central\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.900326, "xdebug_link": null, "collector": "log"}, {"message": "[17:55:11] LOG.info: Available rule: {\n    \"id\": 7,\n    \"height_type\": \"simple\",\n    \"curtain_finish\": \"PF\",\n    \"manipulation\": \"<PERSON><PERSON>\",\n    \"positioning_type\": \"lateral\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.900392, "xdebug_link": null, "collector": "log"}, {"message": "[17:55:11] LOG.info: Available rule: {\n    \"id\": 8,\n    \"height_type\": \"simple\",\n    \"curtain_finish\": \"PF\",\n    \"manipulation\": \"<PERSON><PERSON>\",\n    \"positioning_type\": \"central\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.900451, "xdebug_link": null, "collector": "log"}, {"message": "[17:55:11] LOG.info: Available rule: {\n    \"id\": 9,\n    \"height_type\": \"simple\",\n    \"curtain_finish\": \"Wave\",\n    \"manipulation\": \"Moteur\",\n    \"positioning_type\": \"lateral\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.90051, "xdebug_link": null, "collector": "log"}, {"message": "[17:55:11] LOG.info: Available rule: {\n    \"id\": 10,\n    \"height_type\": \"simple\",\n    \"curtain_finish\": \"Wave\",\n    \"manipulation\": \"Moteur\",\n    \"positioning_type\": \"central\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.900568, "xdebug_link": null, "collector": "log"}, {"message": "[17:55:11] LOG.info: Available rule: {\n    \"id\": 11,\n    \"height_type\": \"simple\",\n    \"curtain_finish\": \"Wave\",\n    \"manipulation\": \"<PERSON><PERSON>\",\n    \"positioning_type\": \"lateral\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.900626, "xdebug_link": null, "collector": "log"}, {"message": "[17:55:11] LOG.info: Available rule: {\n    \"id\": 12,\n    \"height_type\": \"simple\",\n    \"curtain_finish\": \"Wave\",\n    \"manipulation\": \"<PERSON><PERSON>\",\n    \"positioning_type\": \"central\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.900697, "xdebug_link": null, "collector": "log"}, {"message": "[17:55:11] LOG.info: Available rule: {\n    \"id\": 13,\n    \"height_type\": \"double\",\n    \"curtain_finish\": \"PP\",\n    \"manipulation\": null,\n    \"positioning_type\": \"lateral\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.900758, "xdebug_link": null, "collector": "log"}, {"message": "[17:55:11] LOG.info: Available rule: {\n    \"id\": 14,\n    \"height_type\": \"double\",\n    \"curtain_finish\": \"PP\",\n    \"manipulation\": null,\n    \"positioning_type\": \"central\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.900858, "xdebug_link": null, "collector": "log"}, {"message": "[17:55:11] LOG.info: Available rule: {\n    \"id\": 15,\n    \"height_type\": \"double\",\n    \"curtain_finish\": \"PF\",\n    \"manipulation\": null,\n    \"positioning_type\": \"lateral\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.900927, "xdebug_link": null, "collector": "log"}, {"message": "[17:55:11] LOG.info: Available rule: {\n    \"id\": 16,\n    \"height_type\": \"double\",\n    \"curtain_finish\": \"PF\",\n    \"manipulation\": null,\n    \"positioning_type\": \"central\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.900987, "xdebug_link": null, "collector": "log"}, {"message": "[17:55:11] LOG.info: Available rule: {\n    \"id\": 17,\n    \"height_type\": \"double\",\n    \"curtain_finish\": \"Wave\",\n    \"manipulation\": null,\n    \"positioning_type\": \"lateral\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.901045, "xdebug_link": null, "collector": "log"}, {"message": "[17:55:11] LOG.info: Available rule: {\n    \"id\": 18,\n    \"height_type\": \"double\",\n    \"curtain_finish\": \"Wave\",\n    \"manipulation\": null,\n    \"positioning_type\": \"central\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.901112, "xdebug_link": null, "collector": "log"}, {"message": "[17:55:11] LOG.info: Found rule: {\n    \"rule\": {\n        \"id\": 9,\n        \"height_type\": \"simple\",\n        \"curtain_finish\": \"Wave\",\n        \"manipulation\": \"Moteur\",\n        \"positioning_type\": \"lateral\",\n        \"height_deduction\": \"4.00\",\n        \"formula_description\": \"hauteur_finale = sous_plafond - 4 (Wave\\/Moteur\\/Lat\\u00e9ral)\",\n        \"agrafes_spacing\": 10,\n        \"is_active\": true,\n        \"created_at\": \"2025-06-27T16:50:33.000000Z\",\n        \"updated_at\": \"2025-06-27T16:50:33.000000Z\"\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.904323, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.640253, "end": **********.91033, "duration": 0.2700769901275635, "duration_str": "270ms", "measures": [{"label": "Booting", "start": **********.640253, "relative_start": 0, "end": **********.817417, "relative_end": **********.817417, "duration": 0.17716383934020996, "duration_str": "177ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.817436, "relative_start": 0.17718291282653809, "end": **********.910332, "relative_end": 1.9073486328125e-06, "duration": 0.0928959846496582, "duration_str": "92.9ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 25993648, "peak_usage_str": "25MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST manufacturing/calculations/get-rule", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\ManufacturingCalculationController@getRule", "as": "manufacturing.manufacturing.calculations.get-rule", "namespace": null, "prefix": "manufacturing/calculations", "where": [], "file": "<a href=\"phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FHttp%2FControllers%2FManufacturingCalculationController.php&line=22\" onclick=\"\">app/Http/Controllers/ManufacturingCalculationController.php:22-71</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.027800000000000002, "accumulated_duration_str": "27.8ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 17, "namespace": "middleware", "name": "auth", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}], "start": **********.845769, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:168", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "deltapos", "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.852557, "duration": 0.02586, "duration_str": "25.86ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "deltapos", "start_percent": 0, "width_percent": 93.022}, {"sql": "select * from `manufacturing_calculations` where `is_active` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ManufacturingCalculationController.php", "file": "D:\\www\\delta\\app\\Http\\Controllers\\ManufacturingCalculationController.php", "line": 41}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.897918, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "ManufacturingCalculationController.php:41", "source": "app/Http/Controllers/ManufacturingCalculationController.php:41", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FHttp%2FControllers%2FManufacturingCalculationController.php&line=41", "ajax": false, "filename": "ManufacturingCalculationController.php", "line": "41"}, "connection": "deltapos", "start_percent": 93.022, "width_percent": 4.281}, {"sql": "select * from `manufacturing_calculations` where `height_type` = 'simple' and `curtain_finish` = 'Wave' and `manipulation` = 'Moteur' and `is_active` = 1 and `positioning_type` = 'lateral' limit 1", "type": "query", "params": [], "bindings": ["simple", "Wave", "<PERSON><PERSON><PERSON>", "1", "lateral"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ManufacturingCalculation.php", "file": "D:\\www\\delta\\app\\Models\\ManufacturingCalculation.php", "line": 43}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ManufacturingCalculationController.php", "file": "D:\\www\\delta\\app\\Http\\Controllers\\ManufacturingCalculationController.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.901306, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "ManufacturingCalculation.php:43", "source": "app/Models/ManufacturingCalculation.php:43", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FModels%2FManufacturingCalculation.php&line=43", "ajax": false, "filename": "ManufacturingCalculation.php", "line": "43"}, "connection": "deltapos", "start_percent": 97.302, "width_percent": 2.698}]}, "models": {"data": {"App\\Models\\ManufacturingCalculation": {"value": 19, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FModels%2FManufacturingCalculation.php&line=1", "ajax": false, "filename": "ManufacturingCalculation.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 20, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "DbgoOTCuumHy04EHLVHFGwOg3Jxe3DqOD4DE9hb1", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/users/getimage\"\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1"}, "request": {"telescope": "<a href=\"http://127.0.0.1:8000/_debugbar/telescope/9f41a775-9669-4573-ac53-141343a47a71\" target=\"_blank\">View in Telescope</a>", "path_info": "/manufacturing/calculations/get-rule", "status_code": "<pre class=sf-dump id=sf-dump-2115016871 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2115016871\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1680493187 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1680493187\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-600081700 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>height_type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">simple</span>\"\n  \"<span class=sf-dump-key>curtain_finish</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Wave</span>\"\n  \"<span class=sf-dump-key>manipulation</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Moteur</span>\"\n  \"<span class=sf-dump-key>positioning_type</span>\" => \"<span class=sf-dump-str title=\"7 characters\">lateral</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-600081700\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-955636183 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">83</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">DbgoOTCuumHy04EHLVHFGwOg3Jxe3DqOD4DE9hb1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">http://127.0.0.1:8000/manufacturing/order/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">en-GB,en;q=0.9,en-US;q=0.8,fr;q=0.7,ar;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1685 characters\">ph_phc_3ESMmY9SgqEAGBB6sMGK5ayYHkeUuknH2vP6FmWH9RA_posthog=%7B%22distinct_id%22%3A%220196b41d-0793-7e06-86ce-017691734ea0%22%2C%22%24sesid%22%3A%5B1746780823698%2C%220196b41d-078e-7e17-a700-24eed1211756%22%2C1746778523534%5D%7D; theme_mode=eyJpdiI6Ii9XM3FTcWRDTHBmaVg5M0k2cFJiMEE9PSIsInZhbHVlIjoiNlZBU0xFRmZDaEdXVE5JRVMyMFhQcG0waG1PUWdFSDdWb1pCMWx3R0JVank5WXhQMytscW4vTW4yMkZSWlhWNjVISlc3NjNtcGNMckVYK1JXb3FOU3c9PSIsIm1hYyI6ImU2MmRhOThiNGQyNDA3N2UwMGViMjRhMzRhOThkYmJkZDdjMmNjYjhjZDk2MjIxMWZlZjVlNjA4Y2ZkYTkzNGIiLCJ0YWciOiIifQ%3D%3D; language_data=eyJpdiI6InpaMG5kK3BkZ2c3NFZqKy9nVVFRd2c9PSIsInZhbHVlIjoicmw0UjBkdFZZclA3UlljTXVDSE9HWGN6S05VUHdZamlZb2hjajFlcm9uVU1LNFpsZlhQRW5wTTlPNlUrRUJ5VUMzOU5lSnJHY2JBZUIvNC95Ujl5bnFxTWpmVEt5aXY2dXJVemZlbG1LenFtdmZEKzE5RXhsc1hJUE1zVk0wNlJMTEhvTUJXLzVnWHN6d1FnVzh1WnVGUWFRN2xvUyt4aC9pRzVaWTlBMFN4NWhQN1ZSL3dGY0ljZzZOZ1UxbUN6IiwibWFjIjoiYWE2MTZlMmNhMGM3ZjMwNTRhOTZkZjc4ZTZlNzI3NjUzMjY3NDMyNmJlYmViMmY4NjFiZDExMTBjNjMzNWFjNiIsInRhZyI6IiJ9; XSRF-TOKEN=eyJpdiI6InNWNmhMVW8wS2FzVjZ0Zk8yOXRBQlE9PSIsInZhbHVlIjoiSHpFWTJ4L09pOFVGQlFHYm1KeGs1VmFYRWxQOEorc1J1L0toNmw5aW9qdkdtZmFETVFxRlBkTlZHamJPcVpvcHhmNjZuTWhYekJTc0NTc0JsdjZhRmY2ZWswNFJlV3UrSjVHalZHb3RVNm50SjdzZWpQUzRrbzE0NmlYY3lnc1IiLCJtYWMiOiI2M2VlYTE0YzMyODk4ODZjMDRlMWFlOTcyNzYxMTFjODg0MjNhOTQxNTg1NDg4NzZkODc2OWVkNDZkY2U3NjIxIiwidGFnIjoiIn0%3D; delta_session=eyJpdiI6IlpJOGF0SCtjcURidGFaemtPbGk2ZkE9PSIsInZhbHVlIjoiYnk5Smk3STBTcHRJbDdRUDNzSS8zQmk1OVFjd1FXZWtXWjZkY3FybTJmNis0ZkJaN20vWitZYmJvRXIyQXhRV2dDUGV1ZFNRL0ZUd3lmLzljbTB4dGQ0TlZlc1piVlphWE1NcmdDV29WTGZldnZGck1UVlB2TzR5aWR6K3MvWjAiLCJtYWMiOiJlY2ZjN2MzYTdlMWEwYTE0Y2MyOTkwMTBmNjU1Y2FlMzUyZmExYzI3MzE5ZmQyOWVkMzg4ZmJmZmU3YzlhZDQxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-955636183\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-844024555 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ph_phc_3ESMmY9SgqEAGBB6sMGK5ayYHkeUuknH2vP6FmWH9RA_posthog</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>theme_mode</span>\" => \"<span class=sf-dump-str title=\"11 characters\">light-theme</span>\"\n  \"<span class=sf-dump-key>language_data</span>\" => \"<span class=sf-dump-str title=\"94 characters\">{&quot;language_code&quot;:&quot;fr&quot;,&quot;language_flag&quot;:&quot;flag-icon-fr&quot;,&quot;direction&quot;:&quot;ltr&quot;,&quot;emoji&quot;:&quot;flag-icon-fr&quot;}</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DbgoOTCuumHy04EHLVHFGwOg3Jxe3DqOD4DE9hb1</span>\"\n  \"<span class=sf-dump-key>delta_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BzceRqun0Ae5OezZzHQypR8L1x1bHb6dsUfkY5Ah</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-844024555\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-904389118 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 16:55:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkphUUNkNVB6cnQvRDNQaUhCVEFKaVE9PSIsInZhbHVlIjoiS2cyQVlQN3A1M2pnbERFZUN6YlY3NlQwdmxtU0NQdnRyMkJ6VVJ2RkM1TTVrZVpyNkk2RDdyajd4bDI2SkdZUFpoV0wrSS9zRDF5YXhTSUk5dk1FNlExbDNCM1UzaGZ4VTcwczNjWnpqR2hMeHdFZVAyeXNwYlo5NEZuSTBWZWoiLCJtYWMiOiI2ZjZlMGRmMzQ4YTEyYTNhMjdjZDU4NDgyMTQ0MmQ0Y2I2NGIxNThiNTc3OGZlZmQ0Yzg0OTllNmQ5YzM5NTdhIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 18:55:11 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">delta_session=eyJpdiI6IlpzbHpBenhIQTZwbkxvSGs1SEoyeWc9PSIsInZhbHVlIjoiSlJkUnEySysvSEttWWtCRnVvZVBPM3llSWxad2svVzBlU3ZLWHBURDlEMjd0VjJ6dzgweXBIUlZNdDRGYTdpSFRPeWtVTkhhMkdSdTNnTmtHdWF5Q2V1NSs2Zm5IcFZDZXdJUTdGQ21reHVmcjNyN3V6aDJIZENxbmlodDQ3OFciLCJtYWMiOiJhZDRkZjFiMWIyOWM5N2Q1OGNmODE2MGM4MTNiZjUxZjM4ZmQ1NWM4NDZhMWJjZDkzZGE4MzQwMDE0ZDg5ZjVlIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 18:55:11 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkphUUNkNVB6cnQvRDNQaUhCVEFKaVE9PSIsInZhbHVlIjoiS2cyQVlQN3A1M2pnbERFZUN6YlY3NlQwdmxtU0NQdnRyMkJ6VVJ2RkM1TTVrZVpyNkk2RDdyajd4bDI2SkdZUFpoV0wrSS9zRDF5YXhTSUk5dk1FNlExbDNCM1UzaGZ4VTcwczNjWnpqR2hMeHdFZVAyeXNwYlo5NEZuSTBWZWoiLCJtYWMiOiI2ZjZlMGRmMzQ4YTEyYTNhMjdjZDU4NDgyMTQ0MmQ0Y2I2NGIxNThiNTc3OGZlZmQ0Yzg0OTllNmQ5YzM5NTdhIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 18:55:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">delta_session=eyJpdiI6IlpzbHpBenhIQTZwbkxvSGs1SEoyeWc9PSIsInZhbHVlIjoiSlJkUnEySysvSEttWWtCRnVvZVBPM3llSWxad2svVzBlU3ZLWHBURDlEMjd0VjJ6dzgweXBIUlZNdDRGYTdpSFRPeWtVTkhhMkdSdTNnTmtHdWF5Q2V1NSs2Zm5IcFZDZXdJUTdGQ21reHVmcjNyN3V6aDJIZENxbmlodDQ3OFciLCJtYWMiOiJhZDRkZjFiMWIyOWM5N2Q1OGNmODE2MGM4MTNiZjUxZjM4ZmQ1NWM4NDZhMWJjZDkzZGE4MzQwMDE0ZDg5ZjVlIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 18:55:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-904389118\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-33825836 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DbgoOTCuumHy04EHLVHFGwOg3Jxe3DqOD4DE9hb1</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/users/getimage</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-33825836\", {\"maxDepth\":0})</script>\n"}}