@extends('layouts.app')
@section('title', __('manufacturing.create_order'))

@section('css')
<style>
    .bs-stepper {
        background-color: #fff;
        box-shadow: 0 2px 6px rgba(0,0,0,.08);
        margin-bottom: 1.5rem;
    }
    .bs-stepper .content {
        padding: 1.5rem;
        margin-top: 0 !important;
    }
    .bs-stepper .bs-stepper-header {
        padding: 0.75rem;
        border-bottom: 1px solid #dee2e6;
    }
    .bs-stepper .line {
        margin: 0 1rem;
    }
    .page-content {
        padding: 1.5rem 1.5rem 0;
    }
    .card-body {
        padding: 0;
    }
</style>
@endsection

@section('content')
<div class="page-wrapper">
    <div class="page-content">
        <x-breadcrumb :langArray="[
            __('manufacturing.manufacturing'),
            __('manufacturing.manufacturing_orders'),
            __('manufacturing.create_order'),
        ]"/>

        <div class="row mb-3">
            <div class="col-12">
                <div class="card">
                    <div class="card-body p-4">
                        <div class="form-group">
                            <x-label for="select_type" name="{{ __('manufacturing.select_type') }}" />
                            <select class="form-select" id="select_type" name="select_type">
                                <option value="">{{ __('manufacturing.select_type_placeholder') }}</option>
                                <option value="Store">{{ __('manufacturing.curtain_types.store') }}</option>
                                <option value="Rideaux">{{ __('manufacturing.curtain_types.rideaux') }}</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Store Form -->
        <div id="store-form" class="row d-none">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="bs-stepper">
                            <div class="bs-stepper-header" role="tablist">
                                <div class="step active" data-target="#store-basic-info">
                                    <button type="button" class="step-trigger" role="tab" aria-controls="store-basic-info" id="store-basic-info-trigger" aria-selected="true">
                                        <span class="bs-stepper-circle">1</span>
                                        <span class="bs-stepper-label">{{ __('manufacturing.basic_info') }}</span>
                                    </button>
                                </div>
                                <div class="line"></div>
                                <div class="step" data-target="#store-dimensions">
                                    <button type="button" class="step-trigger" role="tab" aria-controls="store-dimensions" id="store-dimensions-trigger">
                                        <span class="bs-stepper-circle">2</span>
                                        <span class="bs-stepper-label">{{ __('manufacturing.dimensions') }}</span>
                                    </button>
                                </div>
                                <div class="line"></div>
                                <div class="step" data-target="#store-pricing">
                                    <button type="button" class="step-trigger" role="tab" aria-controls="store-pricing" id="store-pricing-trigger">
                                        <span class="bs-stepper-circle">3</span>
                                        <span class="bs-stepper-label">{{ __('manufacturing.pricing_additional_info') }}</span>
                                    </button>
                                </div>
                            </div>

                            <div class="bs-stepper-content">
                                <form id="storeOrderForm" action="{{ route('manufacturing.order.store') }}" method="POST" enctype="multipart/form-data">
                                    @csrf
                                    <input type="hidden" name="curtain_type" value="Store">
                                    <input type="hidden" name="row_count" value="0">
                                    <input type="hidden" id="store_base_url" value="{{ url('/') }}">
                                    <input type="hidden" id="store_operation" name="operation" value="save">
                                    <input type="hidden" name="order_number" value="">
                                    <input type="hidden" id="prefix_code" value="{{ $data['prefix_code'] }}">
                                    <input type="hidden" id="count_id" value="{{ $data['count_id'] }}">

                                    <!-- Step 1: Basic Info -->
                                    <div id="store-basic-info" class="content active" role="tabpanel" aria-labelledby="store-basic-info-trigger">
                                        @include('manufacturing_orders._store_basic_info')
                                        <div class="mt-4">
                                            <button type="button" class="btn btn-primary btn-next-store">{{ __('manufacturing.next') }}</button>
                                        </div>
                                    </div>

                                    <!-- Step 2: Dimensions -->
                                    <div id="store-dimensions" class="content" role="tabpanel" aria-labelledby="store-dimensions-trigger">
                                        @include('manufacturing_orders._store_dimensions')
                                        <div class="mt-4">
                                            <button type="button" class="btn btn-secondary btn-previous-store">{{ __('manufacturing.previous') }}</button>
                                            <button type="button" class="btn btn-primary btn-next-store">{{ __('manufacturing.next') }}</button>
                                        </div>
                                    </div>

                                    <!-- Step 3: Pricing -->
                                    <div id="store-pricing" class="content" role="tabpanel" aria-labelledby="store-pricing-trigger">
                                        @include('manufacturing_orders._store_pricing')
                                        <div class="mt-4">
                                            <button type="button" class="btn btn-secondary btn-previous-store">{{ __('manufacturing.previous') }}</button>
                                            <button type="button" class="btn btn-primary" id="submit_store_form">{{ __('manufacturing.submit') }}</button>
                                            <x-anchor-tag href="{{ route('manufacturing.order.index') }}" text="{{ __('manufacturing.close') }}" class="btn btn-light" />
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Rideaux Form -->
        <div id="rideaux-form" class="row d-none">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="bs-stepper">
                            <div class="bs-stepper-header" role="tablist">
                                <div class="step active" data-target="#rideaux-basic-info">
                                    <button type="button" class="step-trigger" role="tab" aria-controls="rideaux-basic-info" id="rideaux-basic-info-trigger" aria-selected="true">
                                        <span class="bs-stepper-circle">1</span>
                                        <span class="bs-stepper-label">{{ __('manufacturing.basic_info') }}</span>
                                    </button>
                                </div>
                                <div class="line"></div>
                                <div class="step" data-target="#rideaux-dimensions">
                                    <button type="button" class="step-trigger" role="tab" aria-controls="rideaux-dimensions" id="rideaux-dimensions-trigger">
                                        <span class="bs-stepper-circle">2</span>
                                        <span class="bs-stepper-label">{{ __('manufacturing.dimensions') }}</span>
                                    </button>
                                </div>
                                <div class="line"></div>
                                <div class="step" data-target="#rideaux-details">
                                    <button type="button" class="step-trigger" role="tab" aria-controls="rideaux-details" id="rideaux-details-trigger">
                                        <span class="bs-stepper-circle">3</span>
                                        <span class="bs-stepper-label">{{ __('manufacturing.curtain_details') }}</span>
                                    </button>
                                </div>
                                <div class="line"></div>
                                <div class="step" data-target="#rideaux-pricing">
                                    <button type="button" class="step-trigger" role="tab" aria-controls="rideaux-pricing" id="rideaux-pricing-trigger">
                                        <span class="bs-stepper-circle">4</span>
                                        <span class="bs-stepper-label">{{ __('manufacturing.pricing_additional_info') }}</span>
                                    </button>
                                </div>
                            </div>

                            <div class="bs-stepper-content">
                                <form id="rideauxOrderForm" action="{{ route('manufacturing.order.store') }}" method="POST" enctype="multipart/form-data">
                                    @csrf
                                    <input type="hidden" name="curtain_type" value="Rideaux">
                                    <input type="hidden" name="row_count" value="0">
                                    <input type="hidden" id="rideaux_base_url" value="{{ url('/') }}">
                                    <input type="hidden" id="rideaux_operation" name="operation" value="save">
                                    <input type="hidden" name="order_number" value="">
                                    <input type="hidden" id="prefix_code" value="{{ $data['prefix_code'] }}">
                                    <input type="hidden" id="count_id" value="{{ $data['count_id'] }}">

                                    <!-- Step 1: Basic Info -->
                                    <div id="rideaux-basic-info" class="content active" role="tabpanel" aria-labelledby="rideaux-basic-info-trigger">
                                        @include('manufacturing_orders._rideaux_basic_info')
                                        <div class="mt-4">
                                            <button type="button" class="btn btn-primary btn-next-rideaux">{{ __('manufacturing.next') }}</button>
                                        </div>
                                    </div>

                                    <!-- Step 2: Dimensions -->
                                    <div id="rideaux-dimensions" class="content" role="tabpanel" aria-labelledby="rideaux-dimensions-trigger">
                                        @include('manufacturing_orders._rideaux_dimensions')
                                        <div class="mt-4">
                                            <button type="button" class="btn btn-secondary btn-previous-rideaux">{{ __('manufacturing.previous') }}</button>
                                            <button type="button" class="btn btn-primary btn-next-rideaux">{{ __('manufacturing.next') }}</button>
                                        </div>
                                    </div>

                                    <!-- Step 3: Curtain Details -->
                                    <div id="rideaux-details" class="content" role="tabpanel" aria-labelledby="rideaux-details-trigger">
                                        @include('manufacturing_orders._rideaux_details')
                                        <div class="mt-4">
                                            <button type="button" class="btn btn-secondary btn-previous-rideaux">{{ __('manufacturing.previous') }}</button>
                                            <button type="button" class="btn btn-primary btn-next-rideaux">{{ __('manufacturing.next') }}</button>
                                        </div>
                                    </div>

                                    <!-- Step 4: Pricing -->
                                    <div id="rideaux-pricing" class="content" role="tabpanel" aria-labelledby="rideaux-pricing-trigger">
                                        @include('manufacturing_orders._rideaux_pricing')
                                        <div class="mt-4">
                                            <button type="button" class="btn btn-secondary btn-previous-rideaux">{{ __('manufacturing.previous') }}</button>
                                            <button type="button" class="btn btn-primary" id="submit_rideaux_form">{{ __('manufacturing.submit') }}</button>
                                            <x-anchor-tag href="{{ route('manufacturing.order.index') }}" text="{{ __('manufacturing.close') }}" class="btn btn-light" />
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('js')
<script>
    // Only define baseUrl if it hasn't been defined yet
    if (typeof baseUrl === 'undefined') {
        var baseUrl = '{{ url("/") }}';
    }
</script>
<script src="{{ versionedAsset('custom/js/manufacturing/manufacturing-order.js') }}"></script>
@endsection
