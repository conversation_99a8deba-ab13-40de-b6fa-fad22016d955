<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\PaymentTypes;

class PaymentTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $paymentTypes = [
            [
                'name' => 'Cash',
                'account_number' => null,
                'bank_code' => null,
                'unique_code' => 'CASH',
                'description' => 'Cash Payment',
                'status' => 1,
                'print_bit' => 0
            ],
            [
                'name' => 'Cheque',
                'account_number' => null,
                'bank_code' => null,
                'unique_code' => 'CHEQUE',
                'description' => 'Cheque Payment',
                'status' => 1,
                'print_bit' => 0
            ],
            [
                'name' => 'ZITOUNA',
                'account_number' => null,
                'bank_code' => null,
                'unique_code' => 'ZITOUNA',
                'description' => 'ZITOUNA Bank',
                'status' => 1,
                'print_bit' => 0
            ]
        ];

        foreach ($paymentTypes as $paymentType) {
            PaymentTypes::firstOrCreate(
                ['unique_code' => $paymentType['unique_code']],
                $paymentType
            );
        }
    }
} 