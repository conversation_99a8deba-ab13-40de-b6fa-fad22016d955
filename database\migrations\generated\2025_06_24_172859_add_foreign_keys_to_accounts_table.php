<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('accounts', function (Blueprint $table) {
            $table->foreign(['created_by'])->references(['id'])->on('users')->onUpdate('no action')->onDelete('no action');
            $table->foreign(['expense_category_id'])->references(['id'])->on('expense_categories')->onUpdate('no action')->onDelete('cascade');
            $table->foreign(['group_id'])->references(['id'])->on('account_groups')->onUpdate('no action')->onDelete('no action');
            $table->foreign(['party_id'])->references(['id'])->on('parties')->onUpdate('no action')->onDelete('cascade');
            $table->foreign(['payment_type_bank_id'])->references(['id'])->on('payment_types')->onUpdate('no action')->onDelete('cascade');
            $table->foreign(['updated_by'])->references(['id'])->on('users')->onUpdate('no action')->onDelete('no action');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('accounts', function (Blueprint $table) {
            $table->dropForeign('accounts_created_by_foreign');
            $table->dropForeign('accounts_expense_category_id_foreign');
            $table->dropForeign('accounts_group_id_foreign');
            $table->dropForeign('accounts_party_id_foreign');
            $table->dropForeign('accounts_payment_type_bank_id_foreign');
            $table->dropForeign('accounts_updated_by_foreign');
        });
    }
};
