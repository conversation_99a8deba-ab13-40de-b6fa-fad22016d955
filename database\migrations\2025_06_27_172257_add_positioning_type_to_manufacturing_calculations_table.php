<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('manufacturing_calculations', function (Blueprint $table) {
            // Drop the existing unique constraint first
            $table->dropUnique('manuf_calc_unique');

            // Add positioning_type column
            $table->enum('positioning_type', ['lateral', 'central'])->nullable()->after('manipulation');

            // Create new unique constraint that includes positioning_type
            $table->unique(['height_type', 'curtain_finish', 'manipulation', 'positioning_type'], 'manuf_calc_unique_with_pos');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('manufacturing_calculations', function (Blueprint $table) {
            // Drop the new unique constraint
            $table->dropUnique('manuf_calc_unique_with_pos');

            // Drop the positioning_type column
            $table->dropColumn('positioning_type');

            // Restore the original unique constraint
            $table->unique(['height_type', 'curtain_finish', 'manipulation'], 'manuf_calc_unique');
        });
    }
};
