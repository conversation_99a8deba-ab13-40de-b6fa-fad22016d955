<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('item_serial_quantities', function (Blueprint $table) {
            $table->foreign(['item_id'])->references(['id'])->on('items')->onUpdate('no action')->onDelete('cascade');
            $table->foreign(['item_serial_master_id'])->references(['id'])->on('item_serial_masters')->onUpdate('no action')->onDelete('cascade');
            $table->foreign(['warehouse_id'])->references(['id'])->on('warehouses')->onUpdate('no action')->onDelete('no action');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('item_serial_quantities', function (Blueprint $table) {
            $table->dropForeign('item_serial_quantities_item_id_foreign');
            $table->dropForeign('item_serial_quantities_item_serial_master_id_foreign');
            $table->dropForeign('item_serial_quantities_warehouse_id_foreign');
        });
    }
};
