<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('item_serial_quantities', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('item_id')->index('item_serial_quantities_item_id_foreign');
            $table->unsignedBigInteger('warehouse_id')->index('item_serial_quantities_warehouse_id_foreign');
            $table->unsignedBigInteger('item_serial_master_id')->index('item_serial_quantities_item_serial_master_id_foreign');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('item_serial_quantities');
    }
};
