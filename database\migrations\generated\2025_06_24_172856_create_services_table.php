<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('services', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('prefix_code')->nullable();
            $table->string('count_id')->nullable();
            $table->string('service_code')->nullable();
            $table->string('name');
            $table->text('description')->nullable();
            $table->decimal('unit_price', 20)->default(0);
            $table->unsignedBigInteger('tax_id')->nullable()->index('services_tax_id_foreign');
            $table->string('tax_type')->default('inclusive');
            $table->string('image_path')->nullable();
            $table->boolean('status')->default(true);
            $table->unsignedBigInteger('created_by')->nullable()->index('services_created_by_foreign');
            $table->unsignedBigInteger('updated_by')->nullable()->index('services_updated_by_foreign');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('services');
    }
};
