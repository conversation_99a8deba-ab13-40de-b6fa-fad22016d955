{"__meta": {"id": "Xfb7c1529c6669a6be857fa0fd1c6d760", "datetime": "2025-06-27 15:54:03", "utime": 1751036043.047425, "method": "POST", "uri": "/login", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 4, "messages": [{"message": "[15:54:02] LOG.warning: str_contains(): Passing null to parameter #1 ($haystack) of type string is deprecated in D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 268", "message_html": null, "is_string": false, "label": "warning", "time": **********.901222, "xdebug_link": null, "collector": "log"}, {"message": "[15:54:02] LOG.warning: str_contains(): Passing null to parameter #1 ($haystack) of type string is deprecated in D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 268", "message_html": null, "is_string": false, "label": "warning", "time": **********.901342, "xdebug_link": null, "collector": "log"}, {"message": "[15:54:02] LOG.warning: str_contains(): Passing null to parameter #1 ($haystack) of type string is deprecated in D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 268", "message_html": null, "is_string": false, "label": "warning", "time": **********.901427, "xdebug_link": null, "collector": "log"}, {"message": "[15:54:02] LOG.warning: str_contains(): Passing null to parameter #1 ($haystack) of type string is deprecated in D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 268", "message_html": null, "is_string": false, "label": "warning", "time": **********.901492, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.618953, "end": 1751036043.047462, "duration": 0.428508996963501, "duration_str": "429ms", "measures": [{"label": "Booting", "start": **********.618953, "relative_start": 0, "end": **********.87268, "relative_end": **********.87268, "duration": 0.2537269592285156, "duration_str": "254ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.872693, "relative_start": 0.2537400722503662, "end": 1751036043.047464, "relative_end": 1.9073486328125e-06, "duration": 0.17477083206176758, "duration_str": "175ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 26000136, "peak_usage_str": "25MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST login", "middleware": "web, installation<PERSON><PERSON><PERSON>, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@store", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=27\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:27-39</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.024749999999999998, "accumulated_duration_str": "24.75ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Requests/Auth/LoginRequest.php", "file": "D:\\www\\delta\\app\\Http\\Requests\\Auth\\LoginRequest.php", "line": 38}, {"index": 16, "namespace": null, "name": "app/Http/Requests/Auth/LoginRequest.php", "file": "D:\\www\\delta\\app\\Http\\Requests\\Auth\\LoginRequest.php", "line": 59}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "D:\\www\\delta\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 29}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.928394, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "LoginRequest.php:38", "source": "app/Http/Requests/Auth/LoginRequest.php:38", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FHttp%2FRequests%2FAuth%2FLoginRequest.php&line=38", "ajax": false, "filename": "LoginRequest.php", "line": "38"}, "connection": "deltapos", "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Requests/Auth/LoginRequest.php", "file": "D:\\www\\delta\\app\\Http\\Requests\\Auth\\LoginRequest.php", "line": 38}, {"index": 17, "namespace": null, "name": "app/Http/Requests/Auth/LoginRequest.php", "file": "D:\\www\\delta\\app\\Http\\Requests\\Auth\\LoginRequest.php", "line": 59}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "D:\\www\\delta\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 29}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.937689, "duration": 0.024059999999999998, "duration_str": "24.06ms", "memory": 0, "memory_str": null, "filename": "LoginRequest.php:38", "source": "app/Http/Requests/Auth/LoginRequest.php:38", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FHttp%2FRequests%2FAuth%2FLoginRequest.php&line=38", "ajax": false, "filename": "LoginRequest.php", "line": "38"}, "connection": "deltapos", "start_percent": 0, "width_percent": 97.212}, {"sql": "select * from `users` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 139}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 381}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 340}, {"index": 20, "namespace": null, "name": "app/Http/Requests/Auth/LoginRequest.php", "file": "D:\\www\\delta\\app\\Http\\Requests\\Auth\\LoginRequest.php", "line": 63}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "D:\\www\\delta\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 29}], "start": **********.96548, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:139", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:139", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=139", "ajax": false, "filename": "EloquentUserProvider.php", "line": "139"}, "connection": "deltapos", "start_percent": 97.212, "width_percent": 2.788}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "HNkZVWy1EvJYmVGpV26q1OhE2BZcLjdiaGB3lTAA", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/app/getimage/684aec0a5b455.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1"}, "request": {"telescope": "<a href=\"http://127.0.0.1:8000/_debugbar/telescope/9f417c22-317e-4c91-bba3-99c0aca8bd12\" target=\"_blank\">View in Telescope</a>", "path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1524190327 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1524190327\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-556030698 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iSnE31HdLWjb9pxwIm1DCJlCI99aPSw6mNotXKpu</span>\"\n  \"<span class=sf-dump-key>_method</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-556030698\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1953360228 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">487</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">iSnE31HdLWjb9pxwIm1DCJlCI99aPSw6mNotXKpu</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundary1ix3CwNfHYuZsUjO</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">en-GB,en;q=0.9,en-US;q=0.8,fr;q=0.7,ar;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2047 characters\">ph_phc_3ESMmY9SgqEAGBB6sMGK5ayYHkeUuknH2vP6FmWH9RA_posthog=%7B%22distinct_id%22%3A%220196b41d-0793-7e06-86ce-017691734ea0%22%2C%22%24sesid%22%3A%5B1746780823698%2C%220196b41d-078e-7e17-a700-24eed1211756%22%2C1746778523534%5D%7D; aureuserp_session=eyJpdiI6IlIvOXVBdXl4UFJOWHhsSjA2YWNNeGc9PSIsInZhbHVlIjoiVXFQdlhTd3ZFMXZyV1JwNjczTEJUczB4N3YxZG5uWXNpamRHWXZyb0xIdDdiaXFoL2xOalBmNmtqMVdKZWllamlOWnNxREZvNjV5VWVxOXVGY3Q5MVNqK1ArTVBteDZIY01KTXdoc2xEaDEwY2draUl3Qi9SbHJUUnVOOWNsY1ciLCJtYWMiOiI1OWU4NmE1NjczOTQyZjZjZDM4YTYyMzZhMmZmODg4NGJlMWQ1ZjY3MjRkNzM5ZmRkMTllNDFjMTE1ODYxN2QyIiwidGFnIjoiIn0%3D; language_data=eyJpdiI6IkFLeWYyUVZhc3dlMURKNE1kWHYxUmc9PSIsInZhbHVlIjoiSUtsV1UvZ1FmVFNjRTgvMVZpM3E2YkRTR3FGNXl4akNCQVhRVkU2SlFqTnJoYUlHTml2T0hQb21RMzdhcm0rdm1OZjExWjFPRmhRQ00zTjNuZEZWeU5HNUNxZEsxQUdVbG1yVk1CM2hMY1BiYkZXNjQ1czFIUVgrTXhPd2VvZFloMWZHMlh0d3VtaDVMVDRHWU16Qm9qZkJ6V3JuOWtCK3lZbEJud3ZLNytKaEw2dGxUcnd4d0psQTdER1k5bWMzIiwibWFjIjoiOWU3MGMxNTA5MGE5YjJlZjQ4NzNjZDc1ZTliNWM1NTM1MTQ4NzcwN2QxZTNlN2E3YTZmNGFjMTI3YWZhOThkYiIsInRhZyI6IiJ9; theme_mode=eyJpdiI6Ii9XM3FTcWRDTHBmaVg5M0k2cFJiMEE9PSIsInZhbHVlIjoiNlZBU0xFRmZDaEdXVE5JRVMyMFhQcG0waG1PUWdFSDdWb1pCMWx3R0JVank5WXhQMytscW4vTW4yMkZSWlhWNjVISlc3NjNtcGNMckVYK1JXb3FOU3c9PSIsIm1hYyI6ImU2MmRhOThiNGQyNDA3N2UwMGViMjRhMzRhOThkYmJkZDdjMmNjYjhjZDk2MjIxMWZlZjVlNjA4Y2ZkYTkzNGIiLCJ0YWciOiIifQ%3D%3D; XSRF-TOKEN=eyJpdiI6IjB3OUVsR1JkWDM0Z25VbzZWUkswL2c9PSIsInZhbHVlIjoiODV6V3JmNUFUQmF4aGFhcU1VdkZ1S3JQdmFaVUZvUTc5KzJQMzhwMFgwb0I3Mk5OdHdTditnMWJwT0xjZ0xUL3pDTVRKWUIwWmYvQXJ6eEF5Y1V5N2E3QlBPdDc2c3o1dkFiU3pEMW5sM1MzYmhwbXp4SHZFZytRcTJVZTdKUW4iLCJtYWMiOiI3NmEzYzRlNDE2OTBkOTE5MmMyNjE3ZmI4NzIwZTkwNTFkY2Y0ZTI5NjI5NDY0OWM4OGQzMDg1YTYzY2E4YTdmIiwidGFnIjoiIn0%3D; delta_session=eyJpdiI6ImpDU0NCTUh4dmxJYlBVS2lTY0ZlK0E9PSIsInZhbHVlIjoiL3lmbGNjZjM3Y0s2a0VFeWZHeE85dG9RQTNWd25SaXl5elRrQnI0RE9vY0RMM0RPSEZ6TEtuZ1dlNEhNeE5wV2tCQU5VSk44U2UycnQ2VkkxSzJvM3JLOGNhMnVVU0hlYVIvaTNlOTZieVUyZFJ2dXpLcG5SZm1wYUE0VFh1bTkiLCJtYWMiOiI2NDI5N2U5ODFhNTcyNDNkMTA1NTliZmQyYmEwMGM4YTVkYjM3MzBhYTNmYmE2Y2QwY2Y4NmM4MDdlMjY1ZThhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1953360228\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-708626834 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ph_phc_3ESMmY9SgqEAGBB6sMGK5ayYHkeUuknH2vP6FmWH9RA_posthog</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>aureuserp_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>language_data</span>\" => \"<span class=sf-dump-str title=\"94 characters\">{&quot;language_code&quot;:null,&quot;language_flag&quot;:&quot;flag-icon-us&quot;,&quot;direction&quot;:&quot;ltr&quot;,&quot;emoji&quot;:&quot;flag-icon-us&quot;}</span>\"\n  \"<span class=sf-dump-key>theme_mode</span>\" => \"<span class=sf-dump-str title=\"11 characters\">light-theme</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iSnE31HdLWjb9pxwIm1DCJlCI99aPSw6mNotXKpu</span>\"\n  \"<span class=sf-dump-key>delta_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TnVV7kfA4A6TOw8sFVFdOPCSbxEqNnJE3VHiSuhC</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-708626834\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-177594059 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 14:54:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlRXY2VpamVXVkJWbkdDcDN0NUU3QWc9PSIsInZhbHVlIjoiS3krSWxqNlk2RWVMcFZjSFFKV1RUQ1lVaGQwMjRmM1JTdzhJWUZha0xuTTMzVFkvK3VOWThrMHNNRlpFWmU4VzZkOCtFaFdybnZ3RXFkY0FUeFZhZmJVUWVPbnFIVWtKZ2s1T3FFNWFVeEZEdlY4M1RmbE1QOXV1VnhFeTRyZ0ciLCJtYWMiOiIxZGQ2ODA3Nzk4MThhZmE1MWUyNmI0NzYyNjVjYTVmYjExYzVmZDM0M2FjYjQzNjVkOWJjM2EwNDZiOTViZGY5IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 16:54:03 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">delta_session=eyJpdiI6ImZKK3YwNllKVEZQY2dHdlo5azlvS3c9PSIsInZhbHVlIjoienlka2RKTlZ4NS9UL2R1ZWJvZmJUYUVzcUprc3BuOG5LT3dOYzZIRkhPSG5QTDcvRjNBQmpvYStpQzhlVm1hYXRtZW5LNmhCdVJKd01XeDBtNDUvenlWaERZbGlqT0c3WnZYN0dWeGMxLytWUzk5TUFmdXptZVkxd2ZJV2dQT0wiLCJtYWMiOiJiOTZiNzNmYWY5OGVmMTRiYzUwMjFhMDBkYjg1MzUyYjZjYTk5ODMyNWRhMjM5NTJhODA3MjgxZGU4ZmQzMjIwIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 16:54:03 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlRXY2VpamVXVkJWbkdDcDN0NUU3QWc9PSIsInZhbHVlIjoiS3krSWxqNlk2RWVMcFZjSFFKV1RUQ1lVaGQwMjRmM1JTdzhJWUZha0xuTTMzVFkvK3VOWThrMHNNRlpFWmU4VzZkOCtFaFdybnZ3RXFkY0FUeFZhZmJVUWVPbnFIVWtKZ2s1T3FFNWFVeEZEdlY4M1RmbE1QOXV1VnhFeTRyZ0ciLCJtYWMiOiIxZGQ2ODA3Nzk4MThhZmE1MWUyNmI0NzYyNjVjYTVmYjExYzVmZDM0M2FjYjQzNjVkOWJjM2EwNDZiOTViZGY5IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 16:54:03 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">delta_session=eyJpdiI6ImZKK3YwNllKVEZQY2dHdlo5azlvS3c9PSIsInZhbHVlIjoienlka2RKTlZ4NS9UL2R1ZWJvZmJUYUVzcUprc3BuOG5LT3dOYzZIRkhPSG5QTDcvRjNBQmpvYStpQzhlVm1hYXRtZW5LNmhCdVJKd01XeDBtNDUvenlWaERZbGlqT0c3WnZYN0dWeGMxLytWUzk5TUFmdXptZVkxd2ZJV2dQT0wiLCJtYWMiOiJiOTZiNzNmYWY5OGVmMTRiYzUwMjFhMDBkYjg1MzUyYjZjYTk5ODMyNWRhMjM5NTJhODA3MjgxZGU4ZmQzMjIwIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 16:54:03 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-177594059\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-667608325 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HNkZVWy1EvJYmVGpV26q1OhE2BZcLjdiaGB3lTAA</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"52 characters\">http://127.0.0.1:8000/app/getimage/684aec0a5b455.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-667608325\", {\"maxDepth\":0})</script>\n"}}