<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\EmailTemplate;

class EmailTemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $templates = [
            [
                'name' => 'SALE INVOICE',
                'subject' => 'Invoice #[Invoice Number] - [Customer Name]',
                'content' => "Dear [Customer Name],\n\nPlease find attached the invoice for your recent sale.\n\nInvoice Details:\n\n   Invoice Number: [Invoice Number]\n   Sale Date: [Sale Date]\n   Due Date: [Due Date]\n   Total: [Total Amount]\n   Paid Amount: [Paid Amount]\n   Invoice Balance: [Balance Amount]\n\nIf you have any questions or require further assistance, please don't hesitate to contact us at [Your Email Address] or [Your Mobile Number].\n\nThank you for your business.\n\nSincerely,\n[Your Company Name]\n[Your Mobile Number]",
                'keys' => "[Invoice Number]\n\n[Customer Name]\n\n[Sale Date]\n\n[Due Date]\n\n[Total Amount]\n\n[Paid Amount]\n\n[Balance Amount]\n\n[Your Email Address]\n\n[Your Mobile Number]\n\n[Your Company Name]",
                'delete_flag' => 1,
            ],
            [
                'name' => 'SALE ORDER',
                'subject' => 'Sale Order #[Order Number] - [Customer Name]',
                'content' => "Dear [Customer Name],\n\nPlease find attached the details of sale order.\n\nOrder Details:\n\n    Order Number: [Order Number]\n\n    Order Date: [Order Date]\n\n    Due Date: [Due Date]\n\n    Total: [Total Amount]\n\n    Paid Amount: [Paid Amount]\n\n    Invoice Balance: [Balance Amount]\n\nIf you have any questions or require further assistance, please don't hesitate to contact us at [Your Email Address] or [Your Mobile Number].\n\nThank you for your business.\n\nSincerely,\n\n[Your Company Name]\n\n[Your Mobile Number]",
                'keys' => "[Order Number]\n\n[Customer Name]\n\n[Order Date]\n\n[Due Date]\n\n[Total Amount]\n\n[Paid Amount]\n\n[Balance Amount]\n\n[Your Email Address]\n\n[Your Mobile Number]\n\n[Your Company Name]",
                'delete_flag' => 1,
            ],
            [
                'name' => 'SALE RETURN',
                'subject' => 'Sale Return/Credit Note #[Return Number] - [Customer Name]',
                'content' => "Dear [Customer Name],\n\nPlease find attached the details of sale return/credit note.\n\nSale Return Details:\n\n    Return Number: [Return Number]\n\n    Return Date: [Return Date]\n\n    Total: [Total Amount]\n\n    Return Amount: [Return Amount]\n\n    Balance: [Balance Amount]\n\nIf you have any questions or require further assistance, please don't hesitate to contact us at [Your Email Address] or [Your Mobile Number].\n\nThank you for your business.\n\nSincerely,\n\n[Your Company Name]\n\n[Your Mobile Number]",
                'keys' => "[Return Number]\n\n[Customer Name]\n\n[Return Date]\n\n[Total Amount]\n\n[Return Amount]\n\n[Balance Amount]\n\n[Your Email Address]\n\n[Your Mobile Number]\n\n[Your Company Name]",
                'delete_flag' => 1,
            ],
            [
                'name' => 'PURCHASE BILL',
                'subject' => 'Purchase Bill #[Bill Number] - [Supplier Name]',
                'content' => "Dear [Supplier Name],\n\nPlease find attached the bill for your recent purchase.\n\nBill Details:\n\n   Bill Number: [Bill Number]\n   Purchase Date: [Purchase Date]\n   Total: [Total Amount]\n   Paid Amount: [Paid Amount]\n   Bill Balance: [Balance Amount]\n\nIf you have any questions or require further assistance, please don't hesitate to contact us at [Your Email Address] or [Your Mobile Number].\n\nThank you for your business.\n\nSincerely,\n[Your Company Name]\n[Your Mobile Number]",
                'keys' => "[Bill Number]\n\n[Supplier Name]\n\n[Purchase Date]\n\n[Total Amount]\n\n[Paid Amount]\n\n[Balance Amount]\n\n[Your Email Address]\n\n[Your Mobile Number]\n\n[Your Company Name]",
                'delete_flag' => 1,
            ],
            [
                'name' => 'PURCHASE ORDER',
                'subject' => 'Purchase Order #[Order Number] - [Supplier Name]',
                'content' => "Dear [Supplier Name],\n\nPlease find attached the details of purchase order.\n\nPurchase Order Details:\n\n    Order Number: [Order Number]\n\n    Order Date: [Order Date]\n\n    Due Date: [Due Date]\n\n    Total: [Total Amount]\n\n    Paid Amount: [Paid Amount]\n\n    Bill Balance: [Balance Amount]\n\nIf you have any questions or require further assistance, please don't hesitate to contact us at [Your Email Address] or [Your Mobile Number].\n\nThank you for your business.\n\nSincerely,\n\n[Your Company Name]\n\n[Your Mobile Number]",
                'keys' => "[Order Number]\n\n[Customer Name]\n\n[Order Date]\n\n[Due Date]\n\n[Total Amount]\n\n[Paid Amount]\n\n[Balance Amount]\n\n[Your Email Address]\n\n[Your Mobile Number]\n\n[Your Company Name]",
                'delete_flag' => 1,
            ],
            [
                'name' => 'PURCHASE RETURN',
                'subject' => 'Purchase Return/Debit Note #[Return Number] - [Supplier Name]',
                'content' => "Dear [Supplier Name],\n\nPlease find attached the details of purchase return/credit note.\n\nPurchase Return Details:\n\n    Return Number: [Return Number]\n\n    Return Date: [Return Date]\n\n    Total: [Total Amount]\n\n    Return Amount: [Return Amount]\n\n    Balance: [Balance Amount]\n\nIf you have any questions or require further assistance, please don't hesitate to contact us at [Your Email Address] or [Your Mobile Number].\n\nThank you for your business.\n\nSincerely,\n\n[Your Company Name]\n\n[Your Mobile Number]",
                'keys' => "[Return Number]\n\n[Supplier Name]\n\n[Return Date]\n\n[Total Amount]\n\n[Return Amount]\n\n[Balance Amount]\n\n[Your Email Address]\n\n[Your Mobile Number]\n\n[Your Company Name]",
                'delete_flag' => 1,
            ],
            [
                'name' => 'QUOTATION',
                'subject' => 'Quotation #[Quotation Number] - [Customer Name]',
                'content' => "Dear [Customer Name],\n\nPlease find attached the details of Quotation.\n\nQuotation Details:\n\n    Quotation Number: [Quotation Number]\n\n    Quotation Date: [Quotation Date]\n\n    Total: [Total Amount]\n\n\nIf you have any questions or require further assistance, please don't hesitate to contact us at [Your Email Address] or [Your Mobile Number].\n\nThank you for your business.\n\nSincerely,\n\n[Your Company Name]\n\n[Your Mobile Number]",
                'keys' => "[Quotation Number]\n\n[Customer Name]\n\n[Quotation Date]\n\n[Total Amount]\n\n[Balance Amount]\n\n[Your Email Address]\n\n[Your Mobile Number]\n\n[Your Company Name]",
                'delete_flag' => 1,
            ],
        ];

        foreach ($templates as $template) {
            EmailTemplate::firstOrCreate(
                ['name' => $template['name']],
                $template
            );
        }
    }
} 