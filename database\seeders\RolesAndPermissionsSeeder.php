<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Illuminate\Support\Facades\DB;

class RolesAndPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Clear cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create Permissions
        $permissions = [
            [
                'name' => 'customer.create',
                'guard_name' => 'web',
                'permission_group_id' => 1,
                'status' => 1,
                'display_name' => 'Create'
            ],
            [
                'name' => 'customer.edit',
                'guard_name' => 'web',
                'permission_group_id' => 1,
                'status' => 1,
                'display_name' => 'Edit'
            ],
            [
                'name' => 'customer.view',
                'guard_name' => 'web',
                'permission_group_id' => 1,
                'status' => 1,
                'display_name' => 'View'
            ],
            [
                'name' => 'customer.delete',
                'guard_name' => 'web',
                'permission_group_id' => 1,
                'status' => 1,
                'display_name' => 'Delete'
            ],
            [
                'name' => 'tax.create',
                'guard_name' => 'web',
                'permission_group_id' => 2,
                'status' => 1,
                'display_name' => 'Create'
            ],
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(
                ['name' => $permission['name']],
                $permission
            );
        }

        // Create Roles
        $adminRole = Role::firstOrCreate(
            ['name' => 'Admin'],
            [
                'name' => 'Admin',
                'guard_name' => 'web',
                'status' => 1,
            ]
        );

        // Give all permissions to admin role
        $adminRole->givePermissionTo(Permission::all());
    }
}
