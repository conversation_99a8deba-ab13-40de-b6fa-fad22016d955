<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Currency;

class CurrencySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $currencies = [
            [
                'name' => 'US Dollar',
                'symbol' => '$',
                'code' => 'USD',
                'exchange_rate' => '3.000000',
                'is_company_currency' => 0,
            ],
            [
                'name' => 'TND',
                'symbol' => 'TND',
                'code' => 'TND',
                'exchange_rate' => '1.000000',
                'is_company_currency' => 1,
            ],
        ];

        foreach ($currencies as $currency) {
            Currency::firstOrCreate(
                ['code' => $currency['code']],
                $currency
            );
        }
    }
} 