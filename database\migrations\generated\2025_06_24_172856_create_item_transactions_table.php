<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('item_transactions', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('transaction_type');
            $table->unsignedBigInteger('transaction_id');
            $table->string('unique_code');
            $table->date('transaction_date');
            $table->unsignedBigInteger('warehouse_id')->index('item_transactions_warehouse_id_foreign');
            $table->unsignedBigInteger('item_id')->index('item_transactions_item_id_foreign');
            $table->text('description')->nullable();
            $table->string('tracking_type');
            $table->unsignedBigInteger('unit_id')->index('item_transactions_unit_id_foreign');
            $table->decimal('mrp', 20, 4)->default(0);
            $table->decimal('quantity', 20, 4)->default(0);
            $table->decimal('unit_price', 20, 4)->default(0);
            $table->decimal('discount', 20, 4)->default(0);
            $table->decimal('discount_amount', 20, 4)->default(0);
            $table->string('discount_type')->default('percentage');
            $table->unsignedBigInteger('tax_id')->nullable()->index('item_transactions_tax_id_foreign');
            $table->string('tax_type')->default('inclusive');
            $table->decimal('tax_amount', 20, 4)->default(0);
            $table->decimal('total', 20, 4)->default(0)->comment('Including (Discount) - (with or without Tax) ');
            $table->unsignedBigInteger('created_by')->nullable()->index('item_transactions_created_by_foreign');
            $table->unsignedBigInteger('updated_by')->nullable()->index('item_transactions_updated_by_foreign');
            $table->timestamps();

            $table->index(['transaction_type', 'transaction_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('item_transactions');
    }
};
