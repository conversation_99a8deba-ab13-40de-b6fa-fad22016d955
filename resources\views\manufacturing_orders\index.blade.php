@extends('layouts.app')
@section('title', __('manufacturing.manufacturing_orders'))

@section('css')
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css">
<style>
    .order-card {
        transition: all 0.3s ease;
        border-left: 4px solid #ddd;
        margin-bottom: 1rem;
    }
    .order-card:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    .order-card.status-Draft { border-left-color: #6c757d; }
    .order-card.status-In-Progress { border-left-color: #0d6efd; }
    .order-card.status-Completed { border-left-color: #198754; }
    .order-card.status-Delivered { border-left-color: #0dcaf0; }
    
    .order-card .card-body {
        padding: 1.25rem;
    }

    .order-info {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .order-info-item {
        display: flex;
        flex-direction: column;
    }

    .order-info-label {
        font-size: 0.875rem;
        color: #6c757d;
        margin-bottom: 0.25rem;
    }

    .order-info-value {
        font-weight: 500;
    }
    
    .expand-btn {
        transition: transform 0.3s ease;
        cursor: pointer;
        padding: 0.375rem 0.75rem;
        border: none;
        background: none;
    }
    .expand-btn:hover {
        background-color: #f8f9fa;
        border-radius: 0.25rem;
    }
    .expand-btn.expanded i {
        transform: rotate(180deg);
    }
    
    .order-details {
        display: none;
        background-color: #f8f9fa;
        border-radius: 0.25rem;
        margin-top: 1rem;
        padding: 1rem;
    }

    .filters-section {
        background: #fff;
        padding: 1rem;
        border-radius: 0.25rem;
        margin-bottom: 1rem;
    }

    .badge {
        font-size: 0.875rem;
        padding: 0.5em 0.75em;
    }

    .action-buttons {
        display: flex;
        gap: 0.5rem;
    }

    .action-buttons .btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        padding: 0;
    }

    .action-buttons .btn i {
        font-size: 1.25rem;
    }

    .card-header-content {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .order-meta {
        display: flex;
        align-items: center;
        gap: 2rem;
    }
</style>
@endsection

@section('content')
<div class="page-wrapper">
    <div class="page-content">
        <x-breadcrumb :langArray="[
            __('manufacturing.manufacturing'),
            __('manufacturing.manufacturing_orders'),
            __('manufacturing.list')
        ]"/>
        
        <div class="card">
            <div class="card-header px-4 py-3 d-flex justify-content-between align-items-center">
                <h5 class="mb-0">{{ __('manufacturing.manufacturing_orders') }}</h5>
                <a href="{{ route('manufacturing.order.create') }}" class="btn btn-primary">
                    <i class="bx bx-plus"></i> {{ __('manufacturing.create_order') }}
                </a>
            </div>
            <div class="card-body p-4">
                <!-- Filters Section -->
                <div class="filters-section mb-4">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <label class="form-label">{{ __('manufacturing.date_range') }}</label>
                            <input type="text" class="form-control" id="dateRange">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">{{ __('manufacturing.status') }}</label>
                            <select class="form-select" id="statusFilter">
                                <option value="">{{ __('manufacturing.all_statuses') }}</option>
                                <option value="Draft">{{ __('manufacturing.status_draft') }}</option>
                                <option value="In Progress">{{ __('manufacturing.status_in_progress') }}</option>
                                <option value="Completed">{{ __('manufacturing.status_completed') }}</option>
                                <option value="Delivered">{{ __('manufacturing.status_delivered') }}</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">{{ __('manufacturing.type') }}</label>
                            <select class="form-select" id="typeFilter">
                                <option value="">{{ __('manufacturing.all_types') }}</option>
                                <option value="Store">{{ __('manufacturing.curtain_types.store') }}</option>
                                <option value="Rideaux">{{ __('manufacturing.curtain_types.rideaux') }}</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">{{ __('manufacturing.search') }}</label>
                            <input type="text" class="form-control" id="searchInput" placeholder="{{ __('manufacturing.search_placeholder') }}">
                        </div>
                    </div>
                </div>

                <!-- Orders List -->
                <div id="ordersList" class="row g-3">
                    <!-- Orders will be dynamically inserted here -->
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center mt-4">
                    <div id="pagination-info"></div>
                    <nav>
                        <ul class="pagination" id="pagination"></ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Order Details Modal -->
<div class="modal fade" id="orderDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('manufacturing.order_details') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="orderDetailsContent">
            </div>
        </div>
    </div>
</div>
@endsection

@section('js')
<!-- Moment.js from CDN (required by daterangepicker) -->
<script type="text/javascript" src="https://cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
<!-- Date Range Picker from CDN -->
<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
<!-- List.js from CDN -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/list.js/2.3.1/list.min.js"></script>
<!-- Define base URL and translations -->
<script>
    if (typeof baseUrl === 'undefined') {
        var baseUrl = '{{ url("/") }}';
    }

    // Global translations object
    window.appTranslations = {
        // Basic translations
        showing: '{{ __("manufacturing.showing") }}',
        to: '{{ __("manufacturing.to") }}',
        of: '{{ __("manufacturing.of") }}',
        entries: '{{ __("manufacturing.entries") }}',
        confirm_delete: '{{ __("manufacturing.confirm_delete") }}',
        delete_warning: '{{ __("manufacturing.delete_warning") }}',
        yes_delete: '{{ __("manufacturing.yes_delete") }}',
        cancel: '{{ __("manufacturing.cancel") }}',
        deleted: '{{ __("manufacturing.deleted") }}',
        delete_success: '{{ __("manufacturing.delete_success") }}',
        error: '{{ __("manufacturing.error") }}',
        delete_error: '{{ __("manufacturing.delete_error") }}',
        
        // Additional translations
        designation: '{{ __("manufacturing.designation") }}',
        tissus: '{{ __("manufacturing.tissus") }}',
        curtain_type: '{{ __("manufacturing.curtain_type") }}',
        delivery_date: '{{ __("manufacturing.delivery_date") }}',
        created_by: '{{ __("manufacturing.created_by") }}',
        total_amount: '{{ __("manufacturing.total_amount") }}',
        edit: '{{ __("manufacturing.edit") }}',
        view: '{{ __("manufacturing.view") }}',
        delete: '{{ __("manufacturing.delete") }}',

        // Additional JavaScript translations
        failed_to_load_orders: '{{ __("manufacturing.failed_to_load_orders") }}',
        not_available: '{{ __("manufacturing.not_available") }}',
        expand_details: '{{ __("manufacturing.expand_details") }}',
        collapse_details: '{{ __("manufacturing.collapse_details") }}',

        // Status translations
        status_translations: {
            'Draft': '{{ __("manufacturing.status_draft") }}',
            'In Progress': '{{ __("manufacturing.status_in_progress") }}',
            'Completed': '{{ __("manufacturing.status_completed") }}',
            'Delivered': '{{ __("manufacturing.status_delivered") }}'
        },

        // Pagination translations
        previous: '{{ __("manufacturing.previous") }}',
        next: '{{ __("manufacturing.next") }}',
        first: '{{ __("manufacturing.first") }}',
        last: '{{ __("manufacturing.last") }}'
    };
</script>
<!-- Your custom JS -->
<script src="{{ versionedAsset('custom/js/manufacturing/manufacturing-orders-list.js') }}"></script>
@endsection 