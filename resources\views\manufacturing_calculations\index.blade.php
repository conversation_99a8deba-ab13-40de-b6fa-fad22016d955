@extends('layouts.app')
@section('title', __('manufacturing.manufacturing_calculation_rules'))

@section('content')
<!--start page wrapper -->
<div class="page-wrapper">
    <div class="page-content">
        <x-breadcrumb :langArray="[
            __('manufacturing.manufacturing'),
            __('manufacturing.calculation_rules'),
            __('manufacturing.list')
        ]"/>

        <div class="card">
            <div class="card-header px-4 py-3 d-flex justify-content-between align-items-center">
                <h5 class="mb-0">{{ __('manufacturing.manufacturing_calculation_rules') }}</h5>
                <a href="{{ route('manufacturing.manufacturing-calculations.create') }}" class="btn btn-primary">
                    <i class="bx bx-plus"></i> {{ __('manufacturing.add_new_rule') }}
                </a>
            </div>
            <div class="card-body p-4">
                @if(session('success'))
                    <div class="alert alert-success">
                        {{ session('success') }}
                    </div>
                @endif

                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>{{ __('manufacturing.height_type') }}</th>
                                <th>{{ __('manufacturing.curtain_finish') }}</th>
                                <th>{{ __('manufacturing.manipulation') }}</th>
                                <th>{{ __('manufacturing.positioning_type') }}</th>
                                <th>{{ __('manufacturing.height_deduction') }}</th>
                                <th>{{ __('manufacturing.formula') }}</th>
                                <th>{{ __('manufacturing.agrafes_spacing') }}</th>
                                <th>{{ __('manufacturing.status') }}</th>
                                <th>{{ __('manufacturing.actions') }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($rules as $rule)
                                <tr>
                                    <td>{{ ucfirst($rule->height_type) }}</td>
                                    <td>{{ $rule->curtain_finish }}</td>
                                    <td>{{ $rule->manipulation ?? 'N/A' }}</td>
                                    <td>{{ $rule->positioning_type ? __('manufacturing.' . $rule->positioning_type) : 'N/A' }}</td>
                                    <td>{{ $rule->height_deduction }} cm</td>
                                    <td>{{ $rule->formula_description }}</td>
                                    <td>{{ $rule->agrafes_spacing ? $rule->agrafes_spacing . ' cm' : 'N/A' }}</td>
                                    <td>
                                        <span class="badge bg-{{ $rule->is_active ? 'success' : 'danger' }}">
                                            {{ $rule->is_active ? __('manufacturing.active') : __('manufacturing.inactive') }}
                                        </span>
                                    </td>
                                    <td>
                                        <a href="{{ route('manufacturing.manufacturing-calculations.edit', $rule) }}"
                                           class="btn btn-sm btn-primary">
                                            <i class="bx bx-edit"></i> {{ __('manufacturing.edit') }}
                                        </a>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection 