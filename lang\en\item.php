<?php

return [

        'enter_item_name'               => 'Enter Item Name',
        'item_name'                     => 'Item Name',
        'item'                          => 'Item',
        'item_category'                 => 'Item Category',
        'details'                       => 'Item Details',
        'items'                         => 'Items',
        'item_settings'                 => 'Item Settings',
        'create'                         => 'Create Item',
        'update'                         => 'Item Update',
        'list'                          => 'Item List',
        'code'                          => 'Item Code',
        'hsn'                           => 'HSN',
        'sku'                           => 'SKU',
        'mrp'                           => 'MRP',
        'please_enter_item_quantity'   => 'Please enter valid quantity of Item ":item_name"',
        'item_qty_negative'             => 'Item ":item_name" Quantity should not be negative',
        'please_select_items'           => 'Please Select Items',
        'show_sku'                      => 'Show SKU',
        'show_mrp'                      => 'Show MRP',
        'pricing'                       => 'Pricing',
        'stock'                         => 'Stock',
        'image'                         => 'Item Image',
        'sale_price'                    => 'Sale Price',
        'purchase_price'                => 'Purchase Price',
        'discount'                      => 'Discount',
        'discount_on_sale'              => 'Discount on Sale',
        'opening_quantity'              => 'Opening Quantity',
        'at_price'                      => 'At Price',
        'min_stock'                     => 'Minimum Stock',
        'item_location'                 => 'Item Location',
        'tracking_type'                 => 'Tracking Type',
        'regular'                       => 'Regular',
        'batch'                         => 'Batch',
        'serial'                        => 'Serial',
        'batch_tracking'                => 'Batch Tracking',
        'batch_number'                  => 'Batch Number',
        'serial_no_tracking'            => 'Serial No. Tracking',
        'batch_and_tracking'            => 'Batch & Serial Number Tracking',
        'enable_serial_tracking'        => 'Enable Serial Number Tracking',
        'enable_batch_tracking'         => 'Enable Batch Tracking',
        'enable_mfg_date'               => 'Enable Manufacturing Date',
        'enable_exp_date'               => 'Enable Expiry Date',
        'enable_color'                  => 'Enable Color',
        'enable_size'                   => 'Enable Size',
        'enable_model'                  => 'Enable Model',
        'serial_number'                 => 'Serial Number',
        'serial_or_imei_number'         => 'Serial/IMEI Number',
        'mfg_date'                      => 'Mfg.Date',
        'exp_date'                      => 'Exp.Date',
        'model_no'                      => 'Model No.',
        'mrp'                           => 'MRP',
        'color'                         => 'Color',
        'size'                          => 'Size',
        'batch_no'                      => 'Batch No.',

        'category' => [
                    'category'      => 'Category',
                    'type'          => 'Category Type',
                    'details'       => 'Item Category Details',
                    'create'        => 'Create Category',
                    'list'          => 'Category List',
                    'update'        => 'Update Category',
                ],

        'opening_quantity_not_matched_with_batch_records' => 'Quantity Not matched with Batch Count!!',
        'opening_quantity_not_matched_with_serial_records' => 'Quantity Not matched with Serial Numbers!!',
        'failed_to_record_item_transactions' => 'Failed to record Item Transaction!!',
        'failed_to_update_serial_master' => 'Failed to update Item Serial Master!!',

        'failed_to_save_serials' => 'Failed to save Item Serials.',
        'failed_to_save_batch_records'  => 'Failed to save Batch Records.',
        'price_per_unit'                => 'Price/Unit',
        'quantity'                      => 'Quantity',
        'product'                       => 'Product',
        'item_type'                     => 'Item Type',
        'stock_quantity'                => 'Stock Quantity',
        'stock_qty'                     => 'Stock Qty',
        'import_items'                     => 'Import Items',
        'item_name_should_not_empty'       => 'Item Name should not be empty.',
        'item_name_already_exist'          => 'Item Name already exist.',
        'item_type_should_not_empty'       => 'Item type must be either Product or Service..',
        'invalid_item_type'                => 'Invalid Item Type.',
        'item_code_should_not_empty'       => 'Item Code should not be empty.',
        'item_code_already_exist'       => 'Item Code already exist.',
        'tax_rate_should_not_empty'        => 'Tax Rate should not be empty.',
        'invalid_tax_rate'                 => 'Invalid Tax Rate.',
        'tax_name_should_not_empty'        => 'Tax Name should not be empty.',
        'invalid_tax_type'                 => 'Invalid Tax Type.',
        'invalid_conversion_rate'                 => 'Invalid Conversion Rate.',
        'invalid_mrp'                 => 'Invalid MRP.',
        'invalid_sale_price'                 => 'Invalid Sale Price.',
        'invalid_purchase_price'                 => 'Invalid Purchase Price.',
        'invalid_minimum_quantity'                 => 'Invalid Minimum Quantity.',
        'invalid_opening_quantity'                 => 'Invalid Opening Quantity.',
        'invalid_discount_on_sale'                 => 'Invalid Discount on Sale.',
        'invalid_discount_type'                 => 'Invalid Discount Type.',
        'base_unit_should_not_empty'       => 'Base Unit should not be empty.',
        'tax_type_should_not_empty'       => 'Tax Type should not be empty.',
        'item_transaction'                  => 'Item Transaction',
        'batch_wise'                        => 'Batch Wise',
        'serial_or_imei'                    => 'Serial/IMEI',
        'batch_transaction_report'          => 'Batch Transaction Report',
        'serial_transaction_report'         => 'Serial Transaction Report',
        'general_transaction_report'        => 'General Transaction Report',
        'in_stock'                          => 'In stock',
        'barcode'                           => 'Barcode',
        'generate_barcode'                  => 'Generate Barcode',
        'barcode_type'                      => 'Barcode Type',
        'barcodes'                          => 'Barcodes',
        'no_of_labels'                      => 'Number of Labels',
        'stock_impact'                      => 'Stock Impact',
        'expired_item_report'               => 'Expired Item Report',
        'days_until_expiry'                 => 'Days Until Expiry',
        'expired_till_date'                 => 'Expired Till Date',
        'days_remainig_to_expire'           => 'Days Remaining to Expire',
        'reorder_item_report'               => 'Reorder Item Report',
        'current_stock'                     => 'Current Stock',
        'gst'                               => 'GST',
        'gstr-1-report'                     => 'GSTR-1 Report',
        'gstr-2-report'                     => 'GSTR-2 Report',
        'gstr-1'                            => 'GSTR-1',
        'gstr-2'                            => 'GSTR-2',
        'transaction_type'                  => 'Transaction Type',
        'please_enter_quantity_to_transfer' => 'Please enter quantity to transfer!!',
        'item_wise'                         => 'Item Wise',
        'trending'                          => 'Trending Items',
        'only_for_stock_maintain'           => 'Only for Stock Maintain',
        'conversion_rate'                   => 'Conversion Rate',
        /*V1.2*/
        'wholesale_price'                   => 'Wholesale Price',
        'stock_report'                      => 'Stock Report',
        'batch_stock_report'                => 'Batch Wise Item Stock Report',
        'serial_stock_report'               => 'Serial/IMEI Item Stock Report',
        'general_stock_report'              => 'General Item Stock Report',
        'available_qty'                     => 'Available Qty',
        'invalid_wholesale_price'           => 'Invalid Wholesale Price',
        //1.4
        'failed_to_save_regular_item_record'=> 'Failed to save Regular Item Record.',
        //1.4.1
        'show_hsn'                          => 'Show HSN',
        'avg_purchase_price'                => 'Average Purchase Price',
        'total'                             => 'Total Items',
        'available_stock'                   => 'Available Stock',

        //1.4.3
        'brand' => [
                    'brand'         => 'Brand',
                    'details'       => 'Brand Details',
                    'create'        => 'Create Brand',
                    'list'          => 'Brand List',
                    'update'        => 'Update Brand',
                ],

        //1.4.8
        'is_batch_compulsory'              => 'Is the Batch Number required for invoices, bills, and other transactions? (Applies only to batch items)',
        'is_item_name_unique'              => 'Mark this if item names should be unique',

        // Lens Search Modal translations
        'left_eye' => 'Left Eye',
        'right_eye' => 'Right Eye',
        'sphere' => 'Sphere',
        'cylinder' => 'Cylinder',
        'axis' => 'Axis',
        'addition' => 'Addition',
        // 'lens_material' => 'Lens Material', // Already exists or covered by existing 'material'
        // 'lens_type' => 'Lens Type',       // Already exists or can be generic
        // 'lens_coating' => 'Lens Coating',   // Already exists or can be generic
        'add_to_quotation' => 'Add to Quotation',
        'left_eye_suffix' => '(Left Eye)',
        'right_eye_suffix' => '(Right Eye)',
        'search_lenses_title' => 'Search / Filter Lenses',
        'filter_lenses_placeholder' => 'Enter lens attributes above to filter',
        'no_lenses_found_matching_criteria' => 'No lenses found matching your criteria.', // More specific than generic no_lenses_found
        'error_fetching_lenses' => 'Error fetching lenses. Please try again.',
        'enter_filters_to_search' => 'Enter filter criteria to search for lenses.',
        'specify_eye_right_left' => '- Specify Eye (Right/Left)',
        'search_lens' => 'Search Lens', // For the button itself
        'enter_more_chars_to_search' => 'Enter 2 or more characters to search.',

        // Ensuring these specific keys for lens properties are present if not covered by generic ones
        'lens_material' => 'Lens Material',
        'lens_type' => 'Lens Type',
        'lens_coating' => 'Lens Coating',

];
