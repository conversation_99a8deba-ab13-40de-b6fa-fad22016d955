<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ordered_products', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('order_id')->index('ordered_products_order_id_foreign');
            $table->unsignedBigInteger('service_id')->index('ordered_products_service_id_foreign');
            $table->text('description')->nullable();
            $table->date('start_date')->nullable()->comment('Event start date');
            $table->time('start_time')->nullable()->comment('Event start time');
            $table->date('end_date')->nullable()->comment('Event End date');
            $table->time('end_time')->nullable()->comment('Event End time');
            $table->decimal('unit_price', 10)->default(0)->comment('original price(without tax)');
            $table->decimal('quantity', 10, 0)->default(0);
            $table->decimal('total_price', 10)->default(0)->comment('(original price * quantity)');
            $table->unsignedBigInteger('tax_id')->index('ordered_products_tax_id_foreign');
            $table->string('tax_type')->default('inclusive');
            $table->decimal('tax_amount', 10)->default(0);
            $table->decimal('discount', 10)->default(0);
            $table->string('discount_type')->nullable()->comment('fixed or percentage');
            $table->decimal('discount_amount', 10)->default(0);
            $table->decimal('total_price_after_discount', 10)->default(0);
            $table->decimal('total_price_with_tax', 10)->default(0);
            $table->timestamps();
            $table->unsignedBigInteger('assigned_user_id')->nullable()->index('ordered_products_assigned_user_id_foreign');
            $table->text('assigned_user_note')->nullable();
            $table->string('job_code')->nullable();
            $table->string('staff_status')->nullable();
            $table->text('staff_status_note')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ordered_products');
    }
};
