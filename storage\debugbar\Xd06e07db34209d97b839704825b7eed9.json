{"__meta": {"id": "Xd06e07db34209d97b839704825b7eed9", "datetime": "2025-06-27 17:03:46", "utime": **********.900171, "method": "GET", "uri": "/manufacturing/orders/list?page=1", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.508175, "end": **********.900193, "duration": 0.39201807975769043, "duration_str": "392ms", "measures": [{"label": "Booting", "start": **********.508175, "relative_start": 0, "end": **********.803348, "relative_end": **********.803348, "duration": 0.29517316818237305, "duration_str": "295ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.803364, "relative_start": 0.29518914222717285, "end": **********.900195, "relative_end": 1.9073486328125e-06, "duration": 0.09683084487915039, "duration_str": "96.83ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 25160640, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET manufacturing/orders/list", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\ManufacturingOrderController@getList", "as": "manufacturing.manufacturing.order.list", "namespace": null, "prefix": "/manufacturing", "where": [], "file": "<a href=\"phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FHttp%2FControllers%2FManufacturingOrderController.php&line=161\" onclick=\"\">app/Http/Controllers/ManufacturingOrderController.php:161-209</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.027149999999999997, "accumulated_duration_str": "27.15ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 17, "namespace": "middleware", "name": "auth", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}], "start": **********.849192, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:168", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "deltapos", "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.856813, "duration": 0.026699999999999998, "duration_str": "26.7ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "deltapos", "start_percent": 0, "width_percent": 98.343}, {"sql": "select count(*) as aggregate from `manufacturing_orders`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ManufacturingOrderController.php", "file": "D:\\www\\delta\\app\\Http\\Controllers\\ManufacturingOrderController.php", "line": 196}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.8879821, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "ManufacturingOrderController.php:196", "source": "app/Http/Controllers/ManufacturingOrderController.php:196", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FHttp%2FControllers%2FManufacturingOrderController.php&line=196", "ajax": false, "filename": "ManufacturingOrderController.php", "line": "196"}, "connection": "deltapos", "start_percent": 98.343, "width_percent": 1.657}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "HNkZVWy1EvJYmVGpV26q1OhE2BZcLjdiaGB3lTAA", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/users/getimage\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1"}, "request": {"telescope": "<a href=\"http://127.0.0.1:8000/_debugbar/telescope/9f419512-3d73-419f-af39-18449a3a1f5c\" target=\"_blank\">View in Telescope</a>", "path_info": "/manufacturing/orders/list", "status_code": "<pre class=sf-dump id=sf-dump-1849336701 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1849336701\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-52823614 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>page</span>\" => \"<span class=sf-dump-str>1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-52823614\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-969111247 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-969111247\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-566069081 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">http://127.0.0.1:8000/manufacturing_orders</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">en-GB,en;q=0.9,en-US;q=0.8,fr;q=0.7,ar;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2047 characters\">ph_phc_3ESMmY9SgqEAGBB6sMGK5ayYHkeUuknH2vP6FmWH9RA_posthog=%7B%22distinct_id%22%3A%220196b41d-0793-7e06-86ce-017691734ea0%22%2C%22%24sesid%22%3A%5B1746780823698%2C%220196b41d-078e-7e17-a700-24eed1211756%22%2C1746778523534%5D%7D; aureuserp_session=eyJpdiI6IlIvOXVBdXl4UFJOWHhsSjA2YWNNeGc9PSIsInZhbHVlIjoiVXFQdlhTd3ZFMXZyV1JwNjczTEJUczB4N3YxZG5uWXNpamRHWXZyb0xIdDdiaXFoL2xOalBmNmtqMVdKZWllamlOWnNxREZvNjV5VWVxOXVGY3Q5MVNqK1ArTVBteDZIY01KTXdoc2xEaDEwY2draUl3Qi9SbHJUUnVOOWNsY1ciLCJtYWMiOiI1OWU4NmE1NjczOTQyZjZjZDM4YTYyMzZhMmZmODg4NGJlMWQ1ZjY3MjRkNzM5ZmRkMTllNDFjMTE1ODYxN2QyIiwidGFnIjoiIn0%3D; theme_mode=eyJpdiI6Ii9XM3FTcWRDTHBmaVg5M0k2cFJiMEE9PSIsInZhbHVlIjoiNlZBU0xFRmZDaEdXVE5JRVMyMFhQcG0waG1PUWdFSDdWb1pCMWx3R0JVank5WXhQMytscW4vTW4yMkZSWlhWNjVISlc3NjNtcGNMckVYK1JXb3FOU3c9PSIsIm1hYyI6ImU2MmRhOThiNGQyNDA3N2UwMGViMjRhMzRhOThkYmJkZDdjMmNjYjhjZDk2MjIxMWZlZjVlNjA4Y2ZkYTkzNGIiLCJ0YWciOiIifQ%3D%3D; language_data=eyJpdiI6InpaMG5kK3BkZ2c3NFZqKy9nVVFRd2c9PSIsInZhbHVlIjoicmw0UjBkdFZZclA3UlljTXVDSE9HWGN6S05VUHdZamlZb2hjajFlcm9uVU1LNFpsZlhQRW5wTTlPNlUrRUJ5VUMzOU5lSnJHY2JBZUIvNC95Ujl5bnFxTWpmVEt5aXY2dXJVemZlbG1LenFtdmZEKzE5RXhsc1hJUE1zVk0wNlJMTEhvTUJXLzVnWHN6d1FnVzh1WnVGUWFRN2xvUyt4aC9pRzVaWTlBMFN4NWhQN1ZSL3dGY0ljZzZOZ1UxbUN6IiwibWFjIjoiYWE2MTZlMmNhMGM3ZjMwNTRhOTZkZjc4ZTZlNzI3NjUzMjY3NDMyNmJlYmViMmY4NjFiZDExMTBjNjMzNWFjNiIsInRhZyI6IiJ9; XSRF-TOKEN=eyJpdiI6Ikh4MXNSUjlKR1R2QnJycGNuSHdPdGc9PSIsInZhbHVlIjoiZ00rcFRVZ0NRYncvcjcvaFFzMnVMRmIrMGJFRFRDR3RRY2JoUmh4VHdRR2hLT25pZkRvZVBHd2hDa1lCaG00a0RxOGJOZmRCRklkaW9SZUdaRGJCdmlvM0xaWnZOdVlsbEpmUGhWZHRGcnVkM2w2QjFwRStRamZUQ3UzWklmdGUiLCJtYWMiOiIwMTg4ZWQ1YWMxMzdkYzU4NjQ3MjM2MDI3Yzk4NmJmZWY3NGExZWIwNTkxNGRjNWFkMTkzMzgzNWFkODE2ZGVmIiwidGFnIjoiIn0%3D; delta_session=eyJpdiI6InVRYzhhbkM5ejJXNC9Xandjb3pZV0E9PSIsInZhbHVlIjoiSkxYd2xZVVcwbE90cy80c0FvTHZvZVRYODFvMC9YZ3hRamNGZGdJWXJpdm1KK1NrVVpGQWFBRnAvNWFmUDFUTklzMEd1QTVFRmFxWVYvSVNxWkU5RVJlQ3NMYkpPclFKR3RwUHkzcHJRYVJTZnR0bzlhQUN1U0R4RUEzenBKU2wiLCJtYWMiOiI2MjgzMzYzMmUxN2ViZDA5MDM1ZDFhODhhZmUxNGM0OWI1ZmFkM2RkZDE0MjA5NWM2ZGUwMGMwZTY0NmVkYTljIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-566069081\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2029480152 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ph_phc_3ESMmY9SgqEAGBB6sMGK5ayYHkeUuknH2vP6FmWH9RA_posthog</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>aureuserp_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>theme_mode</span>\" => \"<span class=sf-dump-str title=\"11 characters\">light-theme</span>\"\n  \"<span class=sf-dump-key>language_data</span>\" => \"<span class=sf-dump-str title=\"94 characters\">{&quot;language_code&quot;:&quot;fr&quot;,&quot;language_flag&quot;:&quot;flag-icon-fr&quot;,&quot;direction&quot;:&quot;ltr&quot;,&quot;emoji&quot;:&quot;flag-icon-fr&quot;}</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HNkZVWy1EvJYmVGpV26q1OhE2BZcLjdiaGB3lTAA</span>\"\n  \"<span class=sf-dump-key>delta_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">uHnuvxItO3CJLJzok54Jqf12VJbTwvqHEtEyDXtZ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2029480152\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 16:03:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjFrVTc4OEd5WTdFMjlINmdUS2JpU0E9PSIsInZhbHVlIjoiT0p2THVaT1FjZTdwSHI1a0dDdlRtbGhsV212d0FzTHJsQWhBZ0xFN2N6bGwxRHlTVkdhSS9TbGo1QlNEZm9HdnR5V09BRjRKa0dVdkVRRWZEaS9HRUUrQllFbmV4Q2F2WnJLeUJtd1dVOEN6cm9rdTE0UEtHUVJodGpvZGI2T2UiLCJtYWMiOiI2NzI4YjJmMDNlNmUyNWJkMWI4MjI5NTlmZDAxYjIxZjhiMmJhN2YwMmQyNjViM2M5ZmVlZTBlZmE5ZGQ3YzA2IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 18:03:46 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">delta_session=eyJpdiI6IjdZa1dmWnJWelBuaDRUa1krVGZ3alE9PSIsInZhbHVlIjoiTkI2OUExUmZheXE0OFR0TUpVQ2k5ODJtRUN4TWlBUmppNVRGRU14MkFKTmxSa0RhYjNKc0ZQTk41bHE4TEw1WnRQQTFBR0tGbHVXZGc1cFU0eDhOWmszL3Z3QThDSXRxTmpYeXBpMnhRQWVhbXAydG9JODJHelBwMzNpRlcrbVEiLCJtYWMiOiJhN2VkNzhmNzVhYjhjNjc4NDVjYjNmNjM0ZTg0MjE5ZTQyM2FiYzY1Zjk3ZDE4MDM5YTAxOTNhOWM1MzcwNjBmIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 18:03:46 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjFrVTc4OEd5WTdFMjlINmdUS2JpU0E9PSIsInZhbHVlIjoiT0p2THVaT1FjZTdwSHI1a0dDdlRtbGhsV212d0FzTHJsQWhBZ0xFN2N6bGwxRHlTVkdhSS9TbGo1QlNEZm9HdnR5V09BRjRKa0dVdkVRRWZEaS9HRUUrQllFbmV4Q2F2WnJLeUJtd1dVOEN6cm9rdTE0UEtHUVJodGpvZGI2T2UiLCJtYWMiOiI2NzI4YjJmMDNlNmUyNWJkMWI4MjI5NTlmZDAxYjIxZjhiMmJhN2YwMmQyNjViM2M5ZmVlZTBlZmE5ZGQ3YzA2IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 18:03:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">delta_session=eyJpdiI6IjdZa1dmWnJWelBuaDRUa1krVGZ3alE9PSIsInZhbHVlIjoiTkI2OUExUmZheXE0OFR0TUpVQ2k5ODJtRUN4TWlBUmppNVRGRU14MkFKTmxSa0RhYjNKc0ZQTk41bHE4TEw1WnRQQTFBR0tGbHVXZGc1cFU0eDhOWmszL3Z3QThDSXRxTmpYeXBpMnhRQWVhbXAydG9JODJHelBwMzNpRlcrbVEiLCJtYWMiOiJhN2VkNzhmNzVhYjhjNjc4NDVjYjNmNjM0ZTg0MjE5ZTQyM2FiYzY1Zjk3ZDE4MDM5YTAxOTNhOWM1MzcwNjBmIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 18:03:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HNkZVWy1EvJYmVGpV26q1OhE2BZcLjdiaGB3lTAA</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/users/getimage</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}