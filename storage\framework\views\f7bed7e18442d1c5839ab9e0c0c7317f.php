
<?php $__env->startSection('title', __('manufacturing.create_order')); ?>

<?php $__env->startSection('css'); ?>
<style>
    .bs-stepper {
        background-color: #fff;
        box-shadow: 0 2px 6px rgba(0,0,0,.08);
        margin-bottom: 1.5rem;
    }
    .bs-stepper .content {
        padding: 1.5rem;
        margin-top: 0 !important;
    }
    .bs-stepper .bs-stepper-header {
        padding: 0.75rem;
        border-bottom: 1px solid #dee2e6;
    }
    .bs-stepper .line {
        margin: 0 1rem;
    }
    .page-content {
        padding: 1.5rem 1.5rem 0;
    }
    .card-body {
        padding: 0;
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="page-wrapper">
    <div class="page-content">
        <?php if (isset($component)) { $__componentOriginal269900abaed345884ce342681cdc99f6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal269900abaed345884ce342681cdc99f6 = $attributes; } ?>
<?php $component = App\View\Components\Breadcrumb::resolve(['langArray' => [
            __('manufacturing.manufacturing'),
            __('manufacturing.manufacturing_orders'),
            __('manufacturing.create_order'),
        ]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('breadcrumb'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\Breadcrumb::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal269900abaed345884ce342681cdc99f6)): ?>
<?php $attributes = $__attributesOriginal269900abaed345884ce342681cdc99f6; ?>
<?php unset($__attributesOriginal269900abaed345884ce342681cdc99f6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal269900abaed345884ce342681cdc99f6)): ?>
<?php $component = $__componentOriginal269900abaed345884ce342681cdc99f6; ?>
<?php unset($__componentOriginal269900abaed345884ce342681cdc99f6); ?>
<?php endif; ?>

        <div class="row mb-3">
            <div class="col-12">
                <div class="card">
                    <div class="card-body p-4">
                        <div class="form-group">
                            <?php if (isset($component)) { $__componentOriginal109ed44299a070e6f2e0a484dff15187 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal109ed44299a070e6f2e0a484dff15187 = $attributes; } ?>
<?php $component = App\View\Components\Label::resolve(['for' => 'select_type','name' => ''.e(__('manufacturing.select_type')).''] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('label'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\Label::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal109ed44299a070e6f2e0a484dff15187)): ?>
<?php $attributes = $__attributesOriginal109ed44299a070e6f2e0a484dff15187; ?>
<?php unset($__attributesOriginal109ed44299a070e6f2e0a484dff15187); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal109ed44299a070e6f2e0a484dff15187)): ?>
<?php $component = $__componentOriginal109ed44299a070e6f2e0a484dff15187; ?>
<?php unset($__componentOriginal109ed44299a070e6f2e0a484dff15187); ?>
<?php endif; ?>
                            <select class="form-select" id="select_type" name="select_type">
                                <option value=""><?php echo e(__('manufacturing.select_type_placeholder')); ?></option>
                                <option value="Store"><?php echo e(__('manufacturing.curtain_types.store')); ?></option>
                                <option value="Rideaux"><?php echo e(__('manufacturing.curtain_types.rideaux')); ?></option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Store Form -->
        <div id="store-form" class="row d-none">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="bs-stepper">
                            <div class="bs-stepper-header" role="tablist">
                                <div class="step active" data-target="#store-basic-info">
                                    <button type="button" class="step-trigger" role="tab" aria-controls="store-basic-info" id="store-basic-info-trigger" aria-selected="true">
                                        <span class="bs-stepper-circle">1</span>
                                        <span class="bs-stepper-label"><?php echo e(__('manufacturing.basic_info')); ?></span>
                                    </button>
                                </div>
                                <div class="line"></div>
                                <div class="step" data-target="#store-dimensions">
                                    <button type="button" class="step-trigger" role="tab" aria-controls="store-dimensions" id="store-dimensions-trigger">
                                        <span class="bs-stepper-circle">2</span>
                                        <span class="bs-stepper-label"><?php echo e(__('manufacturing.dimensions')); ?></span>
                                    </button>
                                </div>
                                <div class="line"></div>
                                <div class="step" data-target="#store-pricing">
                                    <button type="button" class="step-trigger" role="tab" aria-controls="store-pricing" id="store-pricing-trigger">
                                        <span class="bs-stepper-circle">3</span>
                                        <span class="bs-stepper-label"><?php echo e(__('manufacturing.pricing_additional_info')); ?></span>
                                    </button>
                                </div>
                            </div>

                            <div class="bs-stepper-content">
                                <form id="storeOrderForm" action="<?php echo e(route('manufacturing.order.store')); ?>" method="POST" enctype="multipart/form-data">
                                    <?php echo csrf_field(); ?>
                                    <input type="hidden" name="curtain_type" value="Store">
                                    <input type="hidden" name="row_count" value="0">
                                    <input type="hidden" id="store_base_url" value="<?php echo e(url('/')); ?>">
                                    <input type="hidden" id="store_operation" name="operation" value="save">
                                    <input type="hidden" name="order_number" value="">
                                    <input type="hidden" id="prefix_code" value="<?php echo e($data['prefix_code']); ?>">
                                    <input type="hidden" id="count_id" value="<?php echo e($data['count_id']); ?>">

                                    <!-- Step 1: Basic Info -->
                                    <div id="store-basic-info" class="content active" role="tabpanel" aria-labelledby="store-basic-info-trigger">
                                        <?php echo $__env->make('manufacturing_orders._store_basic_info', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                        <div class="mt-4">
                                            <button type="button" class="btn btn-primary btn-next-store"><?php echo e(__('manufacturing.next')); ?></button>
                                        </div>
                                    </div>

                                    <!-- Step 2: Dimensions -->
                                    <div id="store-dimensions" class="content" role="tabpanel" aria-labelledby="store-dimensions-trigger">
                                        <?php echo $__env->make('manufacturing_orders._store_dimensions', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                        <div class="mt-4">
                                            <button type="button" class="btn btn-secondary btn-previous-store"><?php echo e(__('manufacturing.previous')); ?></button>
                                            <button type="button" class="btn btn-primary btn-next-store"><?php echo e(__('manufacturing.next')); ?></button>
                                        </div>
                                    </div>

                                    <!-- Step 3: Pricing -->
                                    <div id="store-pricing" class="content" role="tabpanel" aria-labelledby="store-pricing-trigger">
                                        <?php echo $__env->make('manufacturing_orders._store_pricing', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                        <div class="mt-4">
                                            <button type="button" class="btn btn-secondary btn-previous-store"><?php echo e(__('manufacturing.previous')); ?></button>
                                            <button type="button" class="btn btn-primary" id="submit_store_form"><?php echo e(__('manufacturing.submit')); ?></button>
                                            <?php if (isset($component)) { $__componentOriginal08be1f47856809f4c6eda68811b93273 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal08be1f47856809f4c6eda68811b93273 = $attributes; } ?>
<?php $component = App\View\Components\AnchorTag::resolve(['href' => ''.e(route('manufacturing.order.index')).'','text' => ''.e(__('manufacturing.close')).'','class' => 'btn btn-light'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('anchor-tag'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\AnchorTag::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal08be1f47856809f4c6eda68811b93273)): ?>
<?php $attributes = $__attributesOriginal08be1f47856809f4c6eda68811b93273; ?>
<?php unset($__attributesOriginal08be1f47856809f4c6eda68811b93273); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal08be1f47856809f4c6eda68811b93273)): ?>
<?php $component = $__componentOriginal08be1f47856809f4c6eda68811b93273; ?>
<?php unset($__componentOriginal08be1f47856809f4c6eda68811b93273); ?>
<?php endif; ?>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Rideaux Form -->
        <div id="rideaux-form" class="row d-none">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="bs-stepper">
                            <div class="bs-stepper-header" role="tablist">
                                <div class="step active" data-target="#rideaux-basic-info">
                                    <button type="button" class="step-trigger" role="tab" aria-controls="rideaux-basic-info" id="rideaux-basic-info-trigger" aria-selected="true">
                                        <span class="bs-stepper-circle">1</span>
                                        <span class="bs-stepper-label"><?php echo e(__('manufacturing.basic_info')); ?></span>
                                    </button>
                                </div>
                                <div class="line"></div>
                                <div class="step" data-target="#rideaux-dimensions">
                                    <button type="button" class="step-trigger" role="tab" aria-controls="rideaux-dimensions" id="rideaux-dimensions-trigger">
                                        <span class="bs-stepper-circle">2</span>
                                        <span class="bs-stepper-label"><?php echo e(__('manufacturing.dimensions')); ?></span>
                                    </button>
                                </div>
                                <div class="line"></div>
                                <div class="step" data-target="#rideaux-details">
                                    <button type="button" class="step-trigger" role="tab" aria-controls="rideaux-details" id="rideaux-details-trigger">
                                        <span class="bs-stepper-circle">3</span>
                                        <span class="bs-stepper-label"><?php echo e(__('manufacturing.curtain_details')); ?></span>
                                    </button>
                                </div>
                                <div class="line"></div>
                                <div class="step" data-target="#rideaux-pricing">
                                    <button type="button" class="step-trigger" role="tab" aria-controls="rideaux-pricing" id="rideaux-pricing-trigger">
                                        <span class="bs-stepper-circle">4</span>
                                        <span class="bs-stepper-label"><?php echo e(__('manufacturing.pricing_additional_info')); ?></span>
                                    </button>
                                </div>
                            </div>

                            <div class="bs-stepper-content">
                                <form id="rideauxOrderForm" action="<?php echo e(route('manufacturing.order.store')); ?>" method="POST" enctype="multipart/form-data">
                                    <?php echo csrf_field(); ?>
                                    <input type="hidden" name="curtain_type" value="Rideaux">
                                    <input type="hidden" name="row_count" value="0">
                                    <input type="hidden" id="rideaux_base_url" value="<?php echo e(url('/')); ?>">
                                    <input type="hidden" id="rideaux_operation" name="operation" value="save">
                                    <input type="hidden" name="order_number" value="">
                                    <input type="hidden" id="prefix_code" value="<?php echo e($data['prefix_code']); ?>">
                                    <input type="hidden" id="count_id" value="<?php echo e($data['count_id']); ?>">

                                    <!-- Step 1: Basic Info -->
                                    <div id="rideaux-basic-info" class="content active" role="tabpanel" aria-labelledby="rideaux-basic-info-trigger">
                                        <?php echo $__env->make('manufacturing_orders._rideaux_basic_info', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                        <div class="mt-4">
                                            <button type="button" class="btn btn-primary btn-next-rideaux"><?php echo e(__('manufacturing.next')); ?></button>
                                        </div>
                                    </div>

                                    <!-- Step 2: Dimensions -->
                                    <div id="rideaux-dimensions" class="content" role="tabpanel" aria-labelledby="rideaux-dimensions-trigger">
                                        <?php echo $__env->make('manufacturing_orders._rideaux_dimensions', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                        <div class="mt-4">
                                            <button type="button" class="btn btn-secondary btn-previous-rideaux"><?php echo e(__('manufacturing.previous')); ?></button>
                                            <button type="button" class="btn btn-primary btn-next-rideaux"><?php echo e(__('manufacturing.next')); ?></button>
                                        </div>
                                    </div>

                                    <!-- Step 3: Curtain Details -->
                                    <div id="rideaux-details" class="content" role="tabpanel" aria-labelledby="rideaux-details-trigger">
                                        <?php echo $__env->make('manufacturing_orders._rideaux_details', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                        <div class="mt-4">
                                            <button type="button" class="btn btn-secondary btn-previous-rideaux"><?php echo e(__('manufacturing.previous')); ?></button>
                                            <button type="button" class="btn btn-primary btn-next-rideaux"><?php echo e(__('manufacturing.next')); ?></button>
                                        </div>
                                    </div>

                                    <!-- Step 4: Pricing -->
                                    <div id="rideaux-pricing" class="content" role="tabpanel" aria-labelledby="rideaux-pricing-trigger">
                                        <?php echo $__env->make('manufacturing_orders._rideaux_pricing', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                        <div class="mt-4">
                                            <button type="button" class="btn btn-secondary btn-previous-rideaux"><?php echo e(__('manufacturing.previous')); ?></button>
                                            <button type="button" class="btn btn-primary" id="submit_rideaux_form"><?php echo e(__('manufacturing.submit')); ?></button>
                                            <?php if (isset($component)) { $__componentOriginal08be1f47856809f4c6eda68811b93273 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal08be1f47856809f4c6eda68811b93273 = $attributes; } ?>
<?php $component = App\View\Components\AnchorTag::resolve(['href' => ''.e(route('manufacturing.order.index')).'','text' => ''.e(__('manufacturing.close')).'','class' => 'btn btn-light'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('anchor-tag'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\AnchorTag::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal08be1f47856809f4c6eda68811b93273)): ?>
<?php $attributes = $__attributesOriginal08be1f47856809f4c6eda68811b93273; ?>
<?php unset($__attributesOriginal08be1f47856809f4c6eda68811b93273); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal08be1f47856809f4c6eda68811b93273)): ?>
<?php $component = $__componentOriginal08be1f47856809f4c6eda68811b93273; ?>
<?php unset($__componentOriginal08be1f47856809f4c6eda68811b93273); ?>
<?php endif; ?>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('js'); ?>
<script>
    // Only define baseUrl if it hasn't been defined yet
    if (typeof baseUrl === 'undefined') {
        var baseUrl = '<?php echo e(url("/")); ?>';
    }
</script>
<script src="<?php echo e(versionedAsset('custom/js/manufacturing/manufacturing-order.js')); ?>"></script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\www\delta\resources\views/manufacturing_orders/create.blade.php ENDPATH**/ ?>