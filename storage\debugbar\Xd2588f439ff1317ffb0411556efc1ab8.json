{"__meta": {"id": "Xd2588f439ff1317ffb0411556efc1ab8", "datetime": "2025-06-27 16:17:08", "utime": **********.3136, "method": "GET", "uri": "/language/switch/4", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 4, "messages": [{"message": "[16:17:07] LOG.warning: str_contains(): Passing null to parameter #1 ($haystack) of type string is deprecated in D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 268", "message_html": null, "is_string": false, "label": "warning", "time": 1751037427.995224, "xdebug_link": null, "collector": "log"}, {"message": "[16:17:07] LOG.warning: str_contains(): Passing null to parameter #1 ($haystack) of type string is deprecated in D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 268", "message_html": null, "is_string": false, "label": "warning", "time": 1751037427.995413, "xdebug_link": null, "collector": "log"}, {"message": "[16:17:07] LOG.warning: str_contains(): Passing null to parameter #1 ($haystack) of type string is deprecated in D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 268", "message_html": null, "is_string": false, "label": "warning", "time": 1751037427.995557, "xdebug_link": null, "collector": "log"}, {"message": "[16:17:07] LOG.warning: str_contains(): Passing null to parameter #1 ($haystack) of type string is deprecated in D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 268", "message_html": null, "is_string": false, "label": "warning", "time": 1751037427.995673, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1751037423.983974, "end": **********.313656, "duration": 4.329682111740112, "duration_str": "4.33s", "measures": [{"label": "Booting", "start": 1751037423.983974, "relative_start": 0, "end": 1751037427.744002, "relative_end": 1751037427.744002, "duration": 3.760028123855591, "duration_str": "3.76s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1751037427.744031, "relative_start": 3.760056972503662, "end": **********.313659, "relative_end": 2.86102294921875e-06, "duration": 0.5696280002593994, "duration_str": "570ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 24246800, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET language/switch/{id}", "middleware": "web", "controller": "App\\Http\\Controllers\\LanguageController@switchLanguage", "namespace": null, "prefix": "", "where": [], "as": "language.switch", "file": "<a href=\"phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FHttp%2FControllers%2FLanguageController.php&line=206\" onclick=\"\">app/Http/Controllers/LanguageController.php:206-222</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.005019999999999999, "accumulated_duration_str": "5.02ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/LanguageController.php", "file": "D:\\www\\delta\\app\\Http\\Controllers\\LanguageController.php", "line": 208}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.130157, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "LanguageController.php:208", "source": "app/Http/Controllers/LanguageController.php:208", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FHttp%2FControllers%2FLanguageController.php&line=208", "ajax": false, "filename": "LanguageController.php", "line": "208"}, "connection": "deltapos", "start_percent": 0, "width_percent": 0}, {"sql": "select * from `languages` where `languages`.`id` = '4' limit 1", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/LanguageController.php", "file": "D:\\www\\delta\\app\\Http\\Controllers\\LanguageController.php", "line": 208}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.279349, "duration": 0.005019999999999999, "duration_str": "5.02ms", "memory": 0, "memory_str": null, "filename": "LanguageController.php:208", "source": "app/Http/Controllers/LanguageController.php:208", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FHttp%2FControllers%2FLanguageController.php&line=208", "ajax": false, "filename": "LanguageController.php", "line": "208"}, "connection": "deltapos", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\Language": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "HNkZVWy1EvJYmVGpV26q1OhE2BZcLjdiaGB3lTAA", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/language/switch/4\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"telescope": "<a href=\"http://127.0.0.1:8000/_debugbar/telescope/9f418464-3b08-44b3-80b7-084034f6bb99\" target=\"_blank\">View in Telescope</a>", "path_info": "/language/switch/4", "status_code": "<pre class=sf-dump id=sf-dump-1376965292 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1376965292\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1305409476 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1305409476\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-378724205 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-378724205\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-565302738 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">http://127.0.0.1:8000/manufacturing/order/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">en-GB,en;q=0.9,en-US;q=0.8,fr;q=0.7,ar;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2047 characters\">ph_phc_3ESMmY9SgqEAGBB6sMGK5ayYHkeUuknH2vP6FmWH9RA_posthog=%7B%22distinct_id%22%3A%220196b41d-0793-7e06-86ce-017691734ea0%22%2C%22%24sesid%22%3A%5B1746780823698%2C%220196b41d-078e-7e17-a700-24eed1211756%22%2C1746778523534%5D%7D; aureuserp_session=eyJpdiI6IlIvOXVBdXl4UFJOWHhsSjA2YWNNeGc9PSIsInZhbHVlIjoiVXFQdlhTd3ZFMXZyV1JwNjczTEJUczB4N3YxZG5uWXNpamRHWXZyb0xIdDdiaXFoL2xOalBmNmtqMVdKZWllamlOWnNxREZvNjV5VWVxOXVGY3Q5MVNqK1ArTVBteDZIY01KTXdoc2xEaDEwY2draUl3Qi9SbHJUUnVOOWNsY1ciLCJtYWMiOiI1OWU4NmE1NjczOTQyZjZjZDM4YTYyMzZhMmZmODg4NGJlMWQ1ZjY3MjRkNzM5ZmRkMTllNDFjMTE1ODYxN2QyIiwidGFnIjoiIn0%3D; language_data=eyJpdiI6IkFLeWYyUVZhc3dlMURKNE1kWHYxUmc9PSIsInZhbHVlIjoiSUtsV1UvZ1FmVFNjRTgvMVZpM3E2YkRTR3FGNXl4akNCQVhRVkU2SlFqTnJoYUlHTml2T0hQb21RMzdhcm0rdm1OZjExWjFPRmhRQ00zTjNuZEZWeU5HNUNxZEsxQUdVbG1yVk1CM2hMY1BiYkZXNjQ1czFIUVgrTXhPd2VvZFloMWZHMlh0d3VtaDVMVDRHWU16Qm9qZkJ6V3JuOWtCK3lZbEJud3ZLNytKaEw2dGxUcnd4d0psQTdER1k5bWMzIiwibWFjIjoiOWU3MGMxNTA5MGE5YjJlZjQ4NzNjZDc1ZTliNWM1NTM1MTQ4NzcwN2QxZTNlN2E3YTZmNGFjMTI3YWZhOThkYiIsInRhZyI6IiJ9; theme_mode=eyJpdiI6Ii9XM3FTcWRDTHBmaVg5M0k2cFJiMEE9PSIsInZhbHVlIjoiNlZBU0xFRmZDaEdXVE5JRVMyMFhQcG0waG1PUWdFSDdWb1pCMWx3R0JVank5WXhQMytscW4vTW4yMkZSWlhWNjVISlc3NjNtcGNMckVYK1JXb3FOU3c9PSIsIm1hYyI6ImU2MmRhOThiNGQyNDA3N2UwMGViMjRhMzRhOThkYmJkZDdjMmNjYjhjZDk2MjIxMWZlZjVlNjA4Y2ZkYTkzNGIiLCJ0YWciOiIifQ%3D%3D; XSRF-TOKEN=eyJpdiI6IlRtNEdHWk5oRGNyQktRUXQ3Mk8vOVE9PSIsInZhbHVlIjoiTGY5Z0xVOUxsSkprL004Y21iV2JqRnFOUkZBZ2l2RTNBNmtJeFBWQ2hxcHkyZTZqTG9hdWo0WFJyQVRHUGE0Q3BYNEhiV0E2WUJRc201QXIwTHlucUdGRmRFZWo0cko0NlhHd3B6NEh6OWZWbmhZcXByRG16MkRSeFBINkdidVkiLCJtYWMiOiJhOTUzYzdkZDkzZDI5YTQxMTYyYjZiYzZiOTgwNDc1Y2FkZTFkMzcwMGE0MGU1NzZkNWQ0MTFhMmM1ODk5YTliIiwidGFnIjoiIn0%3D; delta_session=eyJpdiI6Ik1Rc1Rlb0srcWw1dmppUDJHTGRpMkE9PSIsInZhbHVlIjoiQzRGd2czcGczTWVsWHlaTEdRcWNCS1RxRDIxdEFSQ1RFMjk1SFVETVFRVVJ6R2dVdDRFS3pOcmJVSXNqZmIvcGFlQm1leDlNZnRzMlJURWYwMEpLRkoyVm5zc3o1MEpZUzlsdEsrUElzNlRGNytJNFdnTVpPbEluQUQ1VURYYnMiLCJtYWMiOiI4YmNhNTIyOWYzZWVhMTQ0NDI3OWU4NzU4YTFhMDcyNzA2OGNlNmY1MjU5M2MyMzJlMzU5MGEyYTFkZDVlZjc3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-565302738\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-999484509 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ph_phc_3ESMmY9SgqEAGBB6sMGK5ayYHkeUuknH2vP6FmWH9RA_posthog</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>aureuserp_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>language_data</span>\" => \"<span class=sf-dump-str title=\"94 characters\">{&quot;language_code&quot;:null,&quot;language_flag&quot;:&quot;flag-icon-us&quot;,&quot;direction&quot;:&quot;ltr&quot;,&quot;emoji&quot;:&quot;flag-icon-us&quot;}</span>\"\n  \"<span class=sf-dump-key>theme_mode</span>\" => \"<span class=sf-dump-str title=\"11 characters\">light-theme</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HNkZVWy1EvJYmVGpV26q1OhE2BZcLjdiaGB3lTAA</span>\"\n  \"<span class=sf-dump-key>delta_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">uHnuvxItO3CJLJzok54Jqf12VJbTwvqHEtEyDXtZ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-999484509\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2048782181 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 15:17:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">http://127.0.0.1:8000/manufacturing/order/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ik1oR1J4VnNkK29iY1ZiMEh5b3h5TkE9PSIsInZhbHVlIjoiZGJ3d0wzbGx3ZlhKa242eTd5S0JXRXg0RVZNNTZDb3BOOHJDbW1LWkJ2OGhEeW5qUjVlRC9GbVc2SE00ZFNhN2ljRzRKSlNVRDVyZVhmWjRtcXRKd2M1TE9oVWlyQksrbEZENmFQN0tocGgrTXo0amdOMkRKSE1FV0NjZTd4MHIiLCJtYWMiOiI4NjA0OGFmOWJjNDgzOGQyZmUxYWVlOTk4MTVjNWZmZGE5YjVhZTlmNDQxZGYwMzY3NDJhOGFkNDkxNGZlYzQ2IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 17:17:08 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">delta_session=eyJpdiI6InB0SEFDQnNDZjhFWUZ6dkVlakNhQ2c9PSIsInZhbHVlIjoiZERiM1pMZlpCRGFjWmhMN2dsWWlGMThaUm1YR2Vyd2dsZ0EzeTR1UDFwNVJBY2hWUFJxYUQ4WkFZejhtcTErTmxKNi9vNGRlMWFsalFzaS9ZOUZMWXVsUFRRcTJrd3BSM09lNStIdi9DN3p1Mlp2ZXc2UVMvUHVPcG9ZcnBTS1oiLCJtYWMiOiI3NTNkM2YzMTVmNDgyOTEzNzgwZmQ1ZTc0MjYxYzljOGE2YmM1YWMwNWU4Y2UwMjZjNzc3MzQ4NzkzZjk1NDNjIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 17:17:08 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"528 characters\">language_data=eyJpdiI6InpaMG5kK3BkZ2c3NFZqKy9nVVFRd2c9PSIsInZhbHVlIjoicmw0UjBkdFZZclA3UlljTXVDSE9HWGN6S05VUHdZamlZb2hjajFlcm9uVU1LNFpsZlhQRW5wTTlPNlUrRUJ5VUMzOU5lSnJHY2JBZUIvNC95Ujl5bnFxTWpmVEt5aXY2dXJVemZlbG1LenFtdmZEKzE5RXhsc1hJUE1zVk0wNlJMTEhvTUJXLzVnWHN6d1FnVzh1WnVGUWFRN2xvUyt4aC9pRzVaWTlBMFN4NWhQN1ZSL3dGY0ljZzZOZ1UxbUN6IiwibWFjIjoiYWE2MTZlMmNhMGM3ZjMwNTRhOTZkZjc4ZTZlNzI3NjUzMjY3NDMyNmJlYmViMmY4NjFiZDExMTBjNjMzNWFjNiIsInRhZyI6IiJ9; expires=Sat, 01 Jun 2030 15:17:08 GMT; Max-Age=155520000; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ik1oR1J4VnNkK29iY1ZiMEh5b3h5TkE9PSIsInZhbHVlIjoiZGJ3d0wzbGx3ZlhKa242eTd5S0JXRXg0RVZNNTZDb3BOOHJDbW1LWkJ2OGhEeW5qUjVlRC9GbVc2SE00ZFNhN2ljRzRKSlNVRDVyZVhmWjRtcXRKd2M1TE9oVWlyQksrbEZENmFQN0tocGgrTXo0amdOMkRKSE1FV0NjZTd4MHIiLCJtYWMiOiI4NjA0OGFmOWJjNDgzOGQyZmUxYWVlOTk4MTVjNWZmZGE5YjVhZTlmNDQxZGYwMzY3NDJhOGFkNDkxNGZlYzQ2IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 17:17:08 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">delta_session=eyJpdiI6InB0SEFDQnNDZjhFWUZ6dkVlakNhQ2c9PSIsInZhbHVlIjoiZERiM1pMZlpCRGFjWmhMN2dsWWlGMThaUm1YR2Vyd2dsZ0EzeTR1UDFwNVJBY2hWUFJxYUQ4WkFZejhtcTErTmxKNi9vNGRlMWFsalFzaS9ZOUZMWXVsUFRRcTJrd3BSM09lNStIdi9DN3p1Mlp2ZXc2UVMvUHVPcG9ZcnBTS1oiLCJtYWMiOiI3NTNkM2YzMTVmNDgyOTEzNzgwZmQ1ZTc0MjYxYzljOGE2YmM1YWMwNWU4Y2UwMjZjNzc3MzQ4NzkzZjk1NDNjIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 17:17:08 GMT; path=/; httponly</span>\"\n    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"495 characters\">language_data=eyJpdiI6InpaMG5kK3BkZ2c3NFZqKy9nVVFRd2c9PSIsInZhbHVlIjoicmw0UjBkdFZZclA3UlljTXVDSE9HWGN6S05VUHdZamlZb2hjajFlcm9uVU1LNFpsZlhQRW5wTTlPNlUrRUJ5VUMzOU5lSnJHY2JBZUIvNC95Ujl5bnFxTWpmVEt5aXY2dXJVemZlbG1LenFtdmZEKzE5RXhsc1hJUE1zVk0wNlJMTEhvTUJXLzVnWHN6d1FnVzh1WnVGUWFRN2xvUyt4aC9pRzVaWTlBMFN4NWhQN1ZSL3dGY0ljZzZOZ1UxbUN6IiwibWFjIjoiYWE2MTZlMmNhMGM3ZjMwNTRhOTZkZjc4ZTZlNzI3NjUzMjY3NDMyNmJlYmViMmY4NjFiZDExMTBjNjMzNWFjNiIsInRhZyI6IiJ9; expires=Sat, 01-Jun-2030 15:17:08 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2048782181\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-621615808 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HNkZVWy1EvJYmVGpV26q1OhE2BZcLjdiaGB3lTAA</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/language/switch/4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-621615808\", {\"maxDepth\":0})</script>\n"}}