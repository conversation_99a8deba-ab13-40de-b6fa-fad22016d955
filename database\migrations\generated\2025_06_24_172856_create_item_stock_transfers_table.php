<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('item_stock_transfers', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('stock_transfer_id')->index('item_stock_transfers_stock_transfer_id_foreign');
            $table->unsignedBigInteger('item_id')->index('item_stock_transfers_item_id_foreign');
            $table->unsignedBigInteger('from_warehouse_id')->index('item_stock_transfers_from_warehouse_id_foreign');
            $table->unsignedBigInteger('to_warehouse_id')->index('item_stock_transfers_to_warehouse_id_foreign');
            $table->unsignedBigInteger('from_item_transaction_id')->index('item_stock_transfers_from_item_transaction_id_foreign');
            $table->unsignedBigInteger('to_item_transaction_id')->index('item_stock_transfers_to_item_transaction_id_foreign');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('item_stock_transfers');
    }
};
