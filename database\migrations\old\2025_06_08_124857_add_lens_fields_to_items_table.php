<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('items', function (Blueprint $table) {
            //
            $table->string('color')->nullable();
    $table->string('material')->nullable();
    $table->string('size')->nullable();
    $table->string('frame_type')->nullable();
    $table->string('frame_shape')->nullable();
    $table->string('frame_style')->nullable();
    $table->string('lens_type')->nullable();
    $table->string('lens_material')->nullable();
    $table->string('lens_coating')->nullable();
    $table->boolean('is_photochromic')->default(false);
    $table->boolean('is_polarized')->default(false);
    $table->string('contact_lens_type')->nullable();
    $table->string('contact_lens_material')->nullable();
    $table->string('water_content')->nullable(); // Or decimal/float if appropriate
    $table->integer('pack_quantity')->nullable();
    $table->string('lens_code')->nullable();
    $table->string('lens_label')->nullable();
    $table->string('lens_diameter')->nullable(); // Or decimal/float
    $table->string('lens_geo')->nullable();
    $table->string('lens_index')->nullable(); // Or decimal/float
    $table->decimal('purchase_price_ht', 15, 2)->nullable();
    $table->decimal('selling_price_ht', 15, 2)->nullable();
    $table->string('sphere_min')->nullable(); // Or decimal/float
    $table->string('sphere_max')->nullable(); // Or decimal/float
    $table->string('cylinder_min')->nullable(); // Or decimal/float
    $table->string('cylinder_max')->nullable(); // Or decimal/float
    $table->string('addition_min')->nullable(); // Or decimal/float
    $table->string('addition_max')->nullable(); // Or decimal/float
    $table->string('lens_material_type')->nullable();
    $table->string('lens_photo')->nullable(); // For image path or identifier
    $table->string('lens_supplier')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('items', function (Blueprint $table) {
            //
        });
    }
};
