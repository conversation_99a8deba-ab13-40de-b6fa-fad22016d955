{"__meta": {"id": "X34e89cb13218fccd2dbee19f03c5228d", "datetime": "2025-06-27 17:54:26", "utime": **********.651958, "method": "GET", "uri": "/manufacturing/calculations", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.104073, "end": **********.651973, "duration": 0.5478999614715576, "duration_str": "548ms", "measures": [{"label": "Booting", "start": **********.104073, "relative_start": 0, "end": **********.405445, "relative_end": **********.405445, "duration": 0.30137205123901367, "duration_str": "301ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.405459, "relative_start": 0.30138587951660156, "end": **********.651975, "relative_end": 1.9073486328125e-06, "duration": 0.24651598930358887, "duration_str": "247ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 28417208, "peak_usage_str": "27MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 11, "templates": [{"name": "manufacturing_calculations.index", "param_count": null, "params": [], "start": **********.533089, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/manufacturing_calculations/index.blade.phpmanufacturing_calculations.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fmanufacturing_calculations%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "components.breadcrumb", "param_count": null, "params": [], "start": **********.535725, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/components/breadcrumb.blade.phpcomponents.breadcrumb", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fcomponents%2Fbreadcrumb.blade.php&line=1", "ajax": false, "filename": "breadcrumb.blade.php", "line": "?"}}, {"name": "layouts.app", "param_count": null, "params": [], "start": **********.542103, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/layouts/app.blade.phplayouts.app", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}}, {"name": "layouts.head", "param_count": null, "params": [], "start": **********.543573, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/layouts/head.blade.phplayouts.head", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Flayouts%2Fhead.blade.php&line=1", "ajax": false, "filename": "head.blade.php", "line": "?"}}, {"name": "layouts.page-loader", "param_count": null, "params": [], "start": **********.544868, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/layouts/page-loader.blade.phplayouts.page-loader", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Flayouts%2Fpage-loader.blade.php&line=1", "ajax": false, "filename": "page-loader.blade.php", "line": "?"}}, {"name": "layouts.navigation", "param_count": null, "params": [], "start": **********.545645, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/layouts/navigation.blade.phplayouts.navigation", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Flayouts%2Fnavigation.blade.php&line=1", "ajax": false, "filename": "navigation.blade.php", "line": "?"}}, {"name": "layouts.header", "param_count": null, "params": [], "start": **********.621069, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/layouts/header.blade.phplayouts.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Flayouts%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "components.header-shortcut-menu", "param_count": null, "params": [], "start": **********.624709, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/components/header-shortcut-menu.blade.phpcomponents.header-shortcut-menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fcomponents%2Fheader-shortcut-menu.blade.php&line=1", "ajax": false, "filename": "header-shortcut-menu.blade.php", "line": "?"}}, {"name": "components.flag-toggle", "param_count": null, "params": [], "start": **********.638356, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/components/flag-toggle.blade.phpcomponents.flag-toggle", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fcomponents%2Fflag-toggle.blade.php&line=1", "ajax": false, "filename": "flag-toggle.blade.php", "line": "?"}}, {"name": "layouts.footer", "param_count": null, "params": [], "start": **********.642123, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/layouts/footer.blade.phplayouts.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Flayouts%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}, {"name": "layouts.script", "param_count": null, "params": [], "start": **********.642763, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/layouts/script.blade.phplayouts.script", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Flayouts%2Fscript.blade.php&line=1", "ajax": false, "filename": "script.blade.php", "line": "?"}}]}, "route": {"uri": "GET manufacturing/calculations", "middleware": "web, auth, can:manufacturing.calculation.view", "controller": "App\\Http\\Controllers\\ManufacturingCalculationController@index", "as": "manufacturing.manufacturing-calculations.index", "namespace": null, "prefix": "manufacturing/calculations", "where": [], "file": "<a href=\"phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FHttp%2FControllers%2FManufacturingCalculationController.php&line=76\" onclick=\"\">app/Http/Controllers/ManufacturingCalculationController.php:76-80</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.028, "accumulated_duration_str": "28ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 17, "namespace": "middleware", "name": "auth", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}], "start": **********.44747, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:168", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "deltapos", "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.454628, "duration": 0.022850000000000002, "duration_str": "22.85ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "deltapos", "start_percent": 0, "width_percent": 81.607}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 1 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["1", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\www\\delta\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\www\\delta\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 193}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\www\\delta\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 244}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\www\\delta\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 136}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.505938, "duration": 0.00227, "duration_str": "2.27ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:305", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:305", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=305", "ajax": false, "filename": "HasPermissions.php", "line": "305"}, "connection": "deltapos", "start_percent": 81.607, "width_percent": 8.107}, {"sql": "select * from `manufacturing_calculations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ManufacturingCalculationController.php", "file": "D:\\www\\delta\\app\\Http\\Controllers\\ManufacturingCalculationController.php", "line": 78}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.520727, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "ManufacturingCalculationController.php:78", "source": "app/Http/Controllers/ManufacturingCalculationController.php:78", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FHttp%2FControllers%2FManufacturingCalculationController.php&line=78", "ajax": false, "filename": "ManufacturingCalculationController.php", "line": "78"}, "connection": "deltapos", "start_percent": 89.714, "width_percent": 2.821}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\www\\delta\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, {"index": 23, "namespace": null, "name": "app/Providers/AuthServiceProvider.php", "file": "D:\\www\\delta\\app\\Providers\\AuthServiceProvider.php", "line": 42}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 438}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 406}], "start": **********.583982, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:188", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:188", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=188", "ajax": false, "filename": "HasRoles.php", "line": "188"}, "connection": "deltapos", "start_percent": 92.536, "width_percent": 2.679}, {"sql": "select `id`, `name`, `code`, `emoji` from `languages` where `status` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/View/Components/FlagToggle.php", "file": "D:\\www\\delta\\app\\View\\Components\\FlagToggle.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 961}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.636298, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "FlagToggle.php:43", "source": "app/View/Components/FlagToggle.php:43", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FView%2FComponents%2FFlagToggle.php&line=43", "ajax": false, "filename": "FlagToggle.php", "line": "43"}, "connection": "deltapos", "start_percent": 95.214, "width_percent": 2.571}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "view", "name": "layouts.header", "file": "D:\\www\\delta\\resources\\views/layouts/header.blade.php", "line": 81}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.639971, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "layouts.header:81", "source": "view::layouts.header:81", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Flayouts%2Fheader.blade.php&line=81", "ajax": false, "filename": "header.blade.php", "line": "81"}, "connection": "deltapos", "start_percent": 97.786, "width_percent": 2.214}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 167, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\ManufacturingCalculation": {"value": 18, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FModels%2FManufacturingCalculation.php&line=1", "ajax": false, "filename": "ManufacturingCalculation.php", "line": "?"}}, "App\\Models\\Language": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 190, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 114, "messages": [{"message": "[\n  ability => manufacturing.calculation.view,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1309109415 data-indent-pad=\"  \"><span class=sf-dump-note>manufacturing.calculation.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"30 characters\">manufacturing.calculation.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1309109415\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.519777, "xdebug_link": null}, {"message": "[ability => customer.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-768599928 data-indent-pad=\"  \"><span class=sf-dump-note>customer.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">customer.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-768599928\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.547933, "xdebug_link": null}, {"message": "[ability => customer.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1313010086 data-indent-pad=\"  \"><span class=sf-dump-note>customer.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">customer.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1313010086\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.54839, "xdebug_link": null}, {"message": "[ability => supplier.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-914926405 data-indent-pad=\"  \"><span class=sf-dump-note>supplier.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">supplier.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-914926405\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.549653, "xdebug_link": null}, {"message": "[ability => sale.invoice.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1462038423 data-indent-pad=\"  \"><span class=sf-dump-note>sale.invoice.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">sale.invoice.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1462038423\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.551424, "xdebug_link": null}, {"message": "[ability => sale.invoice.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1554660537 data-indent-pad=\"  \"><span class=sf-dump-note>sale.invoice.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">sale.invoice.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1554660537\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.553506, "xdebug_link": null}, {"message": "[ability => sale.invoice.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1113614350 data-indent-pad=\"  \"><span class=sf-dump-note>sale.invoice.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">sale.invoice.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1113614350\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.555311, "xdebug_link": null}, {"message": "[ability => sale.quotation.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1754568062 data-indent-pad=\"  \"><span class=sf-dump-note>sale.quotation.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">sale.quotation.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1754568062\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.557092, "xdebug_link": null}, {"message": "[ability => sale.invoice.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1842090487 data-indent-pad=\"  \"><span class=sf-dump-note>sale.invoice.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">sale.invoice.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1842090487\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.558275, "xdebug_link": null}, {"message": "[ability => sale.order.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1605160749 data-indent-pad=\"  \"><span class=sf-dump-note>sale.order.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">sale.order.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1605160749\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.559778, "xdebug_link": null}, {"message": "[ability => sale.return.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-327561317 data-indent-pad=\"  \"><span class=sf-dump-note>sale.return.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">sale.return.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-327561317\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.561101, "xdebug_link": null}, {"message": "[ability => purchase.bill.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-176410367 data-indent-pad=\"  \"><span class=sf-dump-note>purchase.bill.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">purchase.bill.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-176410367\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.562088, "xdebug_link": null}, {"message": "[ability => purchase.bill.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-922663648 data-indent-pad=\"  \"><span class=sf-dump-note>purchase.bill.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">purchase.bill.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-922663648\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.563282, "xdebug_link": null}, {"message": "[ability => purchase.bill.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1243965323 data-indent-pad=\"  \"><span class=sf-dump-note>purchase.bill.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">purchase.bill.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1243965323\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.564156, "xdebug_link": null}, {"message": "[ability => purchase.order.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1619647186 data-indent-pad=\"  \"><span class=sf-dump-note>purchase.order.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">purchase.order.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1619647186\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.564956, "xdebug_link": null}, {"message": "[ability => purchase.return.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1149040106 data-indent-pad=\"  \"><span class=sf-dump-note>purchase.return.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">purchase.return.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1149040106\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.565832, "xdebug_link": null}, {"message": "[ability => item.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-536860715 data-indent-pad=\"  \"><span class=sf-dump-note>item.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">item.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-536860715\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.566487, "xdebug_link": null}, {"message": "[ability => item.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2020369773 data-indent-pad=\"  \"><span class=sf-dump-note>item.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">item.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2020369773\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.567598, "xdebug_link": null}, {"message": "[ability => item.category.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1858906299 data-indent-pad=\"  \"><span class=sf-dump-note>item.category.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">item.category.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1858906299\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.568335, "xdebug_link": null}, {"message": "[ability => item.brand.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1948259163 data-indent-pad=\"  \"><span class=sf-dump-note>item.brand.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">item.brand.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1948259163\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.569705, "xdebug_link": null}, {"message": "[ability => expense.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-931107424 data-indent-pad=\"  \"><span class=sf-dump-note>expense.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">expense.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-931107424\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.570298, "xdebug_link": null}, {"message": "[ability => expense.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1097973440 data-indent-pad=\"  \"><span class=sf-dump-note>expense.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">expense.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1097973440\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.571116, "xdebug_link": null}, {"message": "[ability => expense.category.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1382948786 data-indent-pad=\"  \"><span class=sf-dump-note>expense.category.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">expense.category.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1382948786\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.571702, "xdebug_link": null}, {"message": "[\n  ability => expense.subcategory.view,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1004018007 data-indent-pad=\"  \"><span class=sf-dump-note>expense.subcategory.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">expense.subcategory.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1004018007\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.572985, "xdebug_link": null}, {"message": "[ability => transaction.cash.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1308705983 data-indent-pad=\"  \"><span class=sf-dump-note>transaction.cash.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">transaction.cash.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1308705983\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.573972, "xdebug_link": null}, {"message": "[ability => transaction.cash.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-353570851 data-indent-pad=\"  \"><span class=sf-dump-note>transaction.cash.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">transaction.cash.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-353570851\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.574912, "xdebug_link": null}, {"message": "[\n  ability => transaction.cheque.view,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>transaction.cheque.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">transaction.cheque.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.576023, "xdebug_link": null}, {"message": "[ability => transaction.bank.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>transaction.bank.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">transaction.bank.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.577197, "xdebug_link": null}, {"message": "[ability => warehouse.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>warehouse.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">warehouse.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.577938, "xdebug_link": null}, {"message": "[ability => warehouse.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1083304959 data-indent-pad=\"  \"><span class=sf-dump-note>warehouse.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">warehouse.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1083304959\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.579035, "xdebug_link": null}, {"message": "[ability => stock_transfer.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-747011784 data-indent-pad=\"  \"><span class=sf-dump-note>stock_transfer.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">stock_transfer.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-747011784\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.579774, "xdebug_link": null}, {"message": "[ability => import.item, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1526486058 data-indent-pad=\"  \"><span class=sf-dump-note>import.item</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">import.item</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1526486058\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.580543, "xdebug_link": null}, {"message": "[ability => import.item, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-91232361 data-indent-pad=\"  \"><span class=sf-dump-note>import.item</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">import.item</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-91232361\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.581272, "xdebug_link": null}, {"message": "[ability => import.party, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-233107256 data-indent-pad=\"  \"><span class=sf-dump-note>import.party</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">import.party</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-233107256\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.582033, "xdebug_link": null}, {"message": "[ability => generate.barcode, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>generate.barcode</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">generate.barcode</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.582788, "xdebug_link": null}, {"message": "[ability => account.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>account.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">account.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.585659, "xdebug_link": null}, {"message": "[ability => account.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>account.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">account.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.586395, "xdebug_link": null}, {"message": "[ability => account.group.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>account.group.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">account.group.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.586857, "xdebug_link": null}, {"message": "[ability => profile.edit, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>profile.edit</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">profile.edit</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.587161, "xdebug_link": null}, {"message": "[ability => profile.edit, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-226174472 data-indent-pad=\"  \"><span class=sf-dump-note>profile.edit</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">profile.edit</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-226174472\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.587639, "xdebug_link": null}, {"message": "[ability => user.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-77672499 data-indent-pad=\"  \"><span class=sf-dump-note>user.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">user.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-77672499\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.587884, "xdebug_link": null}, {"message": "[ability => role.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1480440300 data-indent-pad=\"  \"><span class=sf-dump-note>role.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">role.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1480440300\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.588136, "xdebug_link": null}, {"message": "[ability => permission.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1915023160 data-indent-pad=\"  \"><span class=sf-dump-note>permission.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">permission.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1915023160\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.588549, "xdebug_link": null}, {"message": "[ability => sms.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-578167980 data-indent-pad=\"  \"><span class=sf-dump-note>sms.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">sms.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-578167980\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.588854, "xdebug_link": null}, {"message": "[ability => sms.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-844437720 data-indent-pad=\"  \"><span class=sf-dump-note>sms.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">sms.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-844437720\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.589346, "xdebug_link": null}, {"message": "[ability => sms.template.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-236399357 data-indent-pad=\"  \"><span class=sf-dump-note>sms.template.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">sms.template.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-236399357\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.589685, "xdebug_link": null}, {"message": "[ability => email.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1247302135 data-indent-pad=\"  \"><span class=sf-dump-note>email.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">email.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1247302135\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.590043, "xdebug_link": null}, {"message": "[ability => email.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1872815503 data-indent-pad=\"  \"><span class=sf-dump-note>email.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">email.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1872815503\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.590362, "xdebug_link": null}, {"message": "[ability => email.template.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-247878896 data-indent-pad=\"  \"><span class=sf-dump-note>email.template.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">email.template.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-247878896\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.590762, "xdebug_link": null}, {"message": "[ability => report.*, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1287775706 data-indent-pad=\"  \"><span class=sf-dump-note>report.*</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">report.*</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1287775706\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.59117, "xdebug_link": null}, {"message": "[ability => report.profit_and_loss, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-994187515 data-indent-pad=\"  \"><span class=sf-dump-note>report.profit_and_loss</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">report.profit_and_loss</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-994187515\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.591571, "xdebug_link": null}, {"message": "[\n  ability => report.item.transaction.batch,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-492966058 data-indent-pad=\"  \"><span class=sf-dump-note>report.item.transaction.batch</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"29 characters\">report.item.transaction.batch</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-492966058\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.592001, "xdebug_link": null}, {"message": "[\n  ability => report.item.transaction.batch,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1032695559 data-indent-pad=\"  \"><span class=sf-dump-note>report.item.transaction.batch</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"29 characters\">report.item.transaction.batch</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1032695559\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.592497, "xdebug_link": null}, {"message": "[\n  ability => report.item.transaction.serial,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-161512552 data-indent-pad=\"  \"><span class=sf-dump-note>report.item.transaction.serial</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"30 characters\">report.item.transaction.serial</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-161512552\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.593015, "xdebug_link": null}, {"message": "[\n  ability => report.item.transaction.general,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1509539750 data-indent-pad=\"  \"><span class=sf-dump-note>report.item.transaction.general</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"31 characters\">report.item.transaction.general</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1509539750\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.593485, "xdebug_link": null}, {"message": "[ability => report.purchase, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-325031213 data-indent-pad=\"  \"><span class=sf-dump-note>report.purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">report.purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-325031213\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.593959, "xdebug_link": null}, {"message": "[ability => report.purchase, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-666949276 data-indent-pad=\"  \"><span class=sf-dump-note>report.purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">report.purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-666949276\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.594437, "xdebug_link": null}, {"message": "[ability => report.purchase.item, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1016470617 data-indent-pad=\"  \"><span class=sf-dump-note>report.purchase.item</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">report.purchase.item</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1016470617\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.594969, "xdebug_link": null}, {"message": "[\n  ability => report.purchase.payment,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1460172084 data-indent-pad=\"  \"><span class=sf-dump-note>report.purchase.payment</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">report.purchase.payment</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1460172084\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.595481, "xdebug_link": null}, {"message": "[ability => report.sale, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1285947945 data-indent-pad=\"  \"><span class=sf-dump-note>report.sale</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">report.sale</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1285947945\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.596062, "xdebug_link": null}, {"message": "[ability => report.sale, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-690813125 data-indent-pad=\"  \"><span class=sf-dump-note>report.sale</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">report.sale</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-690813125\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.596542, "xdebug_link": null}, {"message": "[ability => report.sale.item, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1049595599 data-indent-pad=\"  \"><span class=sf-dump-note>report.sale.item</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">report.sale.item</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1049595599\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.59701, "xdebug_link": null}, {"message": "[ability => report.sale.payment, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2031703316 data-indent-pad=\"  \"><span class=sf-dump-note>report.sale.payment</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">report.sale.payment</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2031703316\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.597475, "xdebug_link": null}, {"message": "[\n  ability => report.customer.due.payment,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-41255657 data-indent-pad=\"  \"><span class=sf-dump-note>report.customer.due.payment</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"27 characters\">report.customer.due.payment</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-41255657\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.598474, "xdebug_link": null}, {"message": "[\n  ability => report.customer.due.payment,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-413910155 data-indent-pad=\"  \"><span class=sf-dump-note>report.customer.due.payment</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"27 characters\">report.customer.due.payment</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-413910155\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.599677, "xdebug_link": null}, {"message": "[\n  ability => report.supplier.due.payment,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1509859074 data-indent-pad=\"  \"><span class=sf-dump-note>report.supplier.due.payment</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"27 characters\">report.supplier.due.payment</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1509859074\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.600742, "xdebug_link": null}, {"message": "[ability => report.expense, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-630635480 data-indent-pad=\"  \"><span class=sf-dump-note>report.expense</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">report.expense</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-630635480\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.601243, "xdebug_link": null}, {"message": "[ability => report.expense, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1822880221 data-indent-pad=\"  \"><span class=sf-dump-note>report.expense</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">report.expense</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1822880221\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.601709, "xdebug_link": null}, {"message": "[ability => report.expense.item, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-696220232 data-indent-pad=\"  \"><span class=sf-dump-note>report.expense.item</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">report.expense.item</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-696220232\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.602189, "xdebug_link": null}, {"message": "[ability => report.expense.payment, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-110774328 data-indent-pad=\"  \"><span class=sf-dump-note>report.expense.payment</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">report.expense.payment</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-110774328\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.602676, "xdebug_link": null}, {"message": "[\n  ability => report.transaction.cashflow,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-983398172 data-indent-pad=\"  \"><span class=sf-dump-note>report.transaction.cashflow</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"27 characters\">report.transaction.cashflow</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-983398172\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.603105, "xdebug_link": null}, {"message": "[\n  ability => report.transaction.cashflow,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>report.transaction.cashflow</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"27 characters\">report.transaction.cashflow</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.603497, "xdebug_link": null}, {"message": "[\n  ability => report.transaction.bank-statement,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>report.transaction.bank-statement</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"33 characters\">report.transaction.bank-statement</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.603914, "xdebug_link": null}, {"message": "[ability => report.gst*, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>report.gst*</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">report.gst*</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.604312, "xdebug_link": null}, {"message": "[ability => report.gstr1, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-401028218 data-indent-pad=\"  \"><span class=sf-dump-note>report.gstr1</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">report.gstr1</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-401028218\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.60469, "xdebug_link": null}, {"message": "[ability => report.gstr2, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-403421061 data-indent-pad=\"  \"><span class=sf-dump-note>report.gstr2</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">report.gstr2</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-403421061\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.605087, "xdebug_link": null}, {"message": "[ability => report.stock_transfer, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-76525362 data-indent-pad=\"  \"><span class=sf-dump-note>report.stock_transfer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">report.stock_transfer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-76525362\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.605617, "xdebug_link": null}, {"message": "[ability => report.stock_transfer, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-838150142 data-indent-pad=\"  \"><span class=sf-dump-note>report.stock_transfer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">report.stock_transfer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-838150142\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.606117, "xdebug_link": null}, {"message": "[\n  ability => report.stock_transfer.item,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-227063252 data-indent-pad=\"  \"><span class=sf-dump-note>report.stock_transfer.item</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"26 characters\">report.stock_transfer.item</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-227063252\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.606645, "xdebug_link": null}, {"message": "[ability => report.stock_report.*, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1989839806 data-indent-pad=\"  \"><span class=sf-dump-note>report.stock_report.*</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">report.stock_report.*</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1989839806\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.607038, "xdebug_link": null}, {"message": "[\n  ability => report.stock_report.item.batch,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-605394426 data-indent-pad=\"  \"><span class=sf-dump-note>report.stock_report.item.batch</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"30 characters\">report.stock_report.item.batch</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-605394426\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.608012, "xdebug_link": null}, {"message": "[\n  ability => report.stock_report.item.serial,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-85037303 data-indent-pad=\"  \"><span class=sf-dump-note>report.stock_report.item.serial</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"31 characters\">report.stock_report.item.serial</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-85037303\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.609131, "xdebug_link": null}, {"message": "[\n  ability => report.stock_report.item.general,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-88341101 data-indent-pad=\"  \"><span class=sf-dump-note>report.stock_report.item.general</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"32 characters\">report.stock_report.item.general</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-88341101\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.610237, "xdebug_link": null}, {"message": "[ability => report.expired.item, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1979023181 data-indent-pad=\"  \"><span class=sf-dump-note>report.expired.item</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">report.expired.item</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1979023181\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.610811, "xdebug_link": null}, {"message": "[ability => report.reorder.item, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1694654363 data-indent-pad=\"  \"><span class=sf-dump-note>report.reorder.item</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">report.reorder.item</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1694654363\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.611456, "xdebug_link": null}, {"message": "[ability => tax.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1453471016 data-indent-pad=\"  \"><span class=sf-dump-note>tax.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">tax.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1453471016\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.611713, "xdebug_link": null}, {"message": "[ability => app.settings.edit, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1377394027 data-indent-pad=\"  \"><span class=sf-dump-note>app.settings.edit</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">app.settings.edit</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1377394027\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.611971, "xdebug_link": null}, {"message": "[ability => company.edit, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1011006989 data-indent-pad=\"  \"><span class=sf-dump-note>company.edit</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">company.edit</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1011006989\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.612284, "xdebug_link": null}, {"message": "[ability => tax.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1183492011 data-indent-pad=\"  \"><span class=sf-dump-note>tax.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">tax.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1183492011\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.61248, "xdebug_link": null}, {"message": "[ability => payment.type.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1331633864 data-indent-pad=\"  \"><span class=sf-dump-note>payment.type.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">payment.type.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1331633864\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.613098, "xdebug_link": null}, {"message": "[ability => currency.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1489942580 data-indent-pad=\"  \"><span class=sf-dump-note>currency.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">currency.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1489942580\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.614344, "xdebug_link": null}, {"message": "[ability => unit.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1885897402 data-indent-pad=\"  \"><span class=sf-dump-note>unit.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">unit.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1885897402\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.615609, "xdebug_link": null}, {"message": "[ability => language.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1571407189 data-indent-pad=\"  \"><span class=sf-dump-note>language.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">language.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1571407189\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.616355, "xdebug_link": null}, {"message": "[\n  ability => manufacturing.order.view,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-447023161 data-indent-pad=\"  \"><span class=sf-dump-note>manufacturing.order.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manufacturing.order.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-447023161\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.617939, "xdebug_link": null}, {"message": "[\n  ability => manufacturing.order.view,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1458604543 data-indent-pad=\"  \"><span class=sf-dump-note>manufacturing.order.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manufacturing.order.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1458604543\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.619187, "xdebug_link": null}, {"message": "[\n  ability => manufacturing.calculation.view,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2049512018 data-indent-pad=\"  \"><span class=sf-dump-note>manufacturing.calculation.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"30 characters\">manufacturing.calculation.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2049512018\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.620467, "xdebug_link": null}, {"message": "[ability => purchase.bill.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1499571891 data-indent-pad=\"  \"><span class=sf-dump-note>purchase.bill.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">purchase.bill.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1499571891\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.622252, "xdebug_link": null}, {"message": "[ability => sale.invoice.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-744242498 data-indent-pad=\"  \"><span class=sf-dump-note>sale.invoice.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">sale.invoice.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-744242498\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.623152, "xdebug_link": null}, {"message": "[ability => sale.invoice.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1203446806 data-indent-pad=\"  \"><span class=sf-dump-note>sale.invoice.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">sale.invoice.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1203446806\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.624029, "xdebug_link": null}, {"message": "[ability => sale.invoice.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1348046711 data-indent-pad=\"  \"><span class=sf-dump-note>sale.invoice.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">sale.invoice.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1348046711\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.626193, "xdebug_link": null}, {"message": "[ability => purchase.bill.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1592235127 data-indent-pad=\"  \"><span class=sf-dump-note>purchase.bill.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">purchase.bill.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1592235127\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.627224, "xdebug_link": null}, {"message": "[ability => expense.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1874502393 data-indent-pad=\"  \"><span class=sf-dump-note>expense.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">expense.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1874502393\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.627765, "xdebug_link": null}, {"message": "[ability => sale.invoice.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-470397237 data-indent-pad=\"  \"><span class=sf-dump-note>sale.invoice.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">sale.invoice.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-470397237\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.628638, "xdebug_link": null}, {"message": "[ability => purchase.bill.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-646303828 data-indent-pad=\"  \"><span class=sf-dump-note>purchase.bill.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">purchase.bill.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-646303828\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.629436, "xdebug_link": null}, {"message": "[ability => purchase.order.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1904016947 data-indent-pad=\"  \"><span class=sf-dump-note>purchase.order.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">purchase.order.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1904016947\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.63041, "xdebug_link": null}, {"message": "[ability => sale.return.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1587238965 data-indent-pad=\"  \"><span class=sf-dump-note>sale.return.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">sale.return.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1587238965\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.631331, "xdebug_link": null}, {"message": "[ability => purchase.return.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-186166316 data-indent-pad=\"  \"><span class=sf-dump-note>purchase.return.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">purchase.return.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-186166316\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.632141, "xdebug_link": null}, {"message": "[ability => stock_transfer.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-431391886 data-indent-pad=\"  \"><span class=sf-dump-note>stock_transfer.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">stock_transfer.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-431391886\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.632797, "xdebug_link": null}, {"message": "[ability => item.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-49326234 data-indent-pad=\"  \"><span class=sf-dump-note>item.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">item.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-49326234\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.633431, "xdebug_link": null}, {"message": "[ability => customer.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2069892330 data-indent-pad=\"  \"><span class=sf-dump-note>customer.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">customer.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2069892330\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.633579, "xdebug_link": null}, {"message": "[ability => supplier.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-803119689 data-indent-pad=\"  \"><span class=sf-dump-note>supplier.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">supplier.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-803119689\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.634292, "xdebug_link": null}, {"message": "[ability => sale.quotation.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1236731735 data-indent-pad=\"  \"><span class=sf-dump-note>sale.quotation.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">sale.quotation.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1236731735\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.635423, "xdebug_link": null}, {"message": "[\n  ability => general.permission.to.apply.discount.to.purchase,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1363592198 data-indent-pad=\"  \"><span class=sf-dump-note>general.permission.to.apply.discount.to.purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"48 characters\">general.permission.to.apply.discount.to.purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1363592198\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.645521, "xdebug_link": null}, {"message": "[\n  ability => general.permission.to.apply.discount.to.sale,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2054430934 data-indent-pad=\"  \"><span class=sf-dump-note>general.permission.to.apply.discount.to.sale</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"44 characters\">general.permission.to.apply.discount.to.sale</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2054430934\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.646828, "xdebug_link": null}]}, "session": {"_token": "DbgoOTCuumHy04EHLVHFGwOg3Jxe3DqOD4DE9hb1", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/manufacturing/calculations\"\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"telescope": "<a href=\"http://127.0.0.1:8000/_debugbar/telescope/9f41a730-8769-4011-810a-34bd6be43327\" target=\"_blank\">View in Telescope</a>", "path_info": "/manufacturing/calculations", "status_code": "<pre class=sf-dump id=sf-dump-950976728 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-950976728\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1544056487 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1544056487\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1861813697 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1861813697\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1988006881 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/manufacturing/order</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">en-GB,en;q=0.9,en-US;q=0.8,fr;q=0.7,ar;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1685 characters\">ph_phc_3ESMmY9SgqEAGBB6sMGK5ayYHkeUuknH2vP6FmWH9RA_posthog=%7B%22distinct_id%22%3A%220196b41d-0793-7e06-86ce-017691734ea0%22%2C%22%24sesid%22%3A%5B1746780823698%2C%220196b41d-078e-7e17-a700-24eed1211756%22%2C1746778523534%5D%7D; theme_mode=eyJpdiI6Ii9XM3FTcWRDTHBmaVg5M0k2cFJiMEE9PSIsInZhbHVlIjoiNlZBU0xFRmZDaEdXVE5JRVMyMFhQcG0waG1PUWdFSDdWb1pCMWx3R0JVank5WXhQMytscW4vTW4yMkZSWlhWNjVISlc3NjNtcGNMckVYK1JXb3FOU3c9PSIsIm1hYyI6ImU2MmRhOThiNGQyNDA3N2UwMGViMjRhMzRhOThkYmJkZDdjMmNjYjhjZDk2MjIxMWZlZjVlNjA4Y2ZkYTkzNGIiLCJ0YWciOiIifQ%3D%3D; language_data=eyJpdiI6InpaMG5kK3BkZ2c3NFZqKy9nVVFRd2c9PSIsInZhbHVlIjoicmw0UjBkdFZZclA3UlljTXVDSE9HWGN6S05VUHdZamlZb2hjajFlcm9uVU1LNFpsZlhQRW5wTTlPNlUrRUJ5VUMzOU5lSnJHY2JBZUIvNC95Ujl5bnFxTWpmVEt5aXY2dXJVemZlbG1LenFtdmZEKzE5RXhsc1hJUE1zVk0wNlJMTEhvTUJXLzVnWHN6d1FnVzh1WnVGUWFRN2xvUyt4aC9pRzVaWTlBMFN4NWhQN1ZSL3dGY0ljZzZOZ1UxbUN6IiwibWFjIjoiYWE2MTZlMmNhMGM3ZjMwNTRhOTZkZjc4ZTZlNzI3NjUzMjY3NDMyNmJlYmViMmY4NjFiZDExMTBjNjMzNWFjNiIsInRhZyI6IiJ9; XSRF-TOKEN=eyJpdiI6InEyWGpudGFzdGFZQ3g0bmNPczk2amc9PSIsInZhbHVlIjoiVTNaZ2JOM0FkL2tLWUNlaVM4RzIxMlpMMDNpa1pEMzVycUpsendyN29xd3Q3M3pjUnFLUzJ6Mkk3TkRyaFdoRm1heGpvMGF4UmZ6cndJcTlveGhYR0ZweFpOcWFkTlVrWlRObG5XKzRuQmU4dXVudk9aU3NzdWQ3alhqc3BzeUUiLCJtYWMiOiIwMTMyYzBhOGI1NTgxM2NlOWRlZmNiMWFiYTFkODZmNTVmYzkwZDJiMTdkNzMzYWFiNzkwMDRlZDcyNzcyYWQ0IiwidGFnIjoiIn0%3D; delta_session=eyJpdiI6IkpMU3lRcEtMbWtEbDB3d2F3S3pHV1E9PSIsInZhbHVlIjoiREJNL0EwbzBMU0VlZmxxczM3Ym01NXJEckFlcmR0WGViaTBNQ1JJa0RlTFFyTDhMOXJDNklxT1pqU0Ewc2k2RXlEQy9LWmJaYjVqODVTKzRxaWxvekdydkdGVlR0WmF4Y1hEMkFoMDhkMlZBb01uVFgvaHdwYjhuZHBhMTR6RFciLCJtYWMiOiJkZDdmYTIyMzkxY2FkYTZhOGUxMmRhYTU0MDM5NzM2NDNiNGUyYmYzMjZlYzRkYmRlMjVmOWZhMmM2ZmM1ODZmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1988006881\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1223743772 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ph_phc_3ESMmY9SgqEAGBB6sMGK5ayYHkeUuknH2vP6FmWH9RA_posthog</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>theme_mode</span>\" => \"<span class=sf-dump-str title=\"11 characters\">light-theme</span>\"\n  \"<span class=sf-dump-key>language_data</span>\" => \"<span class=sf-dump-str title=\"94 characters\">{&quot;language_code&quot;:&quot;fr&quot;,&quot;language_flag&quot;:&quot;flag-icon-fr&quot;,&quot;direction&quot;:&quot;ltr&quot;,&quot;emoji&quot;:&quot;flag-icon-fr&quot;}</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DbgoOTCuumHy04EHLVHFGwOg3Jxe3DqOD4DE9hb1</span>\"\n  \"<span class=sf-dump-key>delta_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BzceRqun0Ae5OezZzHQypR8L1x1bHb6dsUfkY5Ah</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1223743772\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1250689810 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 16:54:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImlkY3B3T3MrWkNnVlMreGpyWFhHYmc9PSIsInZhbHVlIjoibmZYRy85RHd6TjEvOXJZZzhsbFNwUmh6aEFKRXRpSmdDNWZ4c2QzSStnM094VWw3ZTFLK2JLZVlKeXVEMDBXNzhBVmY0ZWVBV1VhWm1mNlJFSStZWVc2Yi9WMW1qQmRWWHN1NG9UYTZabVZtUDE5V0ovL09Rc3I0M0wxenJ4bzIiLCJtYWMiOiI2YzJkZTkyZjdlMTk1NjFkM2ZmYjI2ZWNmMDBlNzRhMzE4M2M4MTg4NDAzMWM3NjIyYzg0ZDRmNTI2YTI1N2Y0IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 18:54:26 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">delta_session=eyJpdiI6IldYQnEwYzY0Y2xwNm90L0R5VkFnOHc9PSIsInZhbHVlIjoiTi8vMFZhOFJDbG95SkVaSWRKdjVmbm40a0Jab3djbWh4V2JocFBXaDN6b2phNHRNeDEyT2xHU1dsV1dnSUZ1ek43R3RqV0lDUWQxREtBekkxUVJyeDc3QkVYUmlYVk5lQTVLaytHS0V3Tk9KY0Z1Vmo0WVFiamU5YW9RSUd2d1AiLCJtYWMiOiIxOTQ4OWUzMWEzZDUyNmU1ZDkxMjQ1NThkZTk5OGU3YTAzZTJlZDI0OTZkZDdiZGQyYTMyZWFkOWQ4OWNlYzkxIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 18:54:26 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImlkY3B3T3MrWkNnVlMreGpyWFhHYmc9PSIsInZhbHVlIjoibmZYRy85RHd6TjEvOXJZZzhsbFNwUmh6aEFKRXRpSmdDNWZ4c2QzSStnM094VWw3ZTFLK2JLZVlKeXVEMDBXNzhBVmY0ZWVBV1VhWm1mNlJFSStZWVc2Yi9WMW1qQmRWWHN1NG9UYTZabVZtUDE5V0ovL09Rc3I0M0wxenJ4bzIiLCJtYWMiOiI2YzJkZTkyZjdlMTk1NjFkM2ZmYjI2ZWNmMDBlNzRhMzE4M2M4MTg4NDAzMWM3NjIyYzg0ZDRmNTI2YTI1N2Y0IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 18:54:26 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">delta_session=eyJpdiI6IldYQnEwYzY0Y2xwNm90L0R5VkFnOHc9PSIsInZhbHVlIjoiTi8vMFZhOFJDbG95SkVaSWRKdjVmbm40a0Jab3djbWh4V2JocFBXaDN6b2phNHRNeDEyT2xHU1dsV1dnSUZ1ek43R3RqV0lDUWQxREtBekkxUVJyeDc3QkVYUmlYVk5lQTVLaytHS0V3Tk9KY0Z1Vmo0WVFiamU5YW9RSUd2d1AiLCJtYWMiOiIxOTQ4OWUzMWEzZDUyNmU1ZDkxMjQ1NThkZTk5OGU3YTAzZTJlZDI0OTZkZDdiZGQyYTMyZWFkOWQ4OWNlYzkxIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 18:54:26 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1250689810\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1736453390 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DbgoOTCuumHy04EHLVHFGwOg3Jxe3DqOD4DE9hb1</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"48 characters\">http://127.0.0.1:8000/manufacturing/calculations</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1736453390\", {\"maxDepth\":0})</script>\n"}}