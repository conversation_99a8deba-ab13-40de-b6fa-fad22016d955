<?php

namespace Database\Seeders\Updates;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use App\Models\PermissionGroup;
use Spatie\Permission\Models\Permission;

class ManufacturingPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        echo "ManufacturingPermissionsSeeder Running...";
        $this->addNewPermissions();
        echo "\nManufacturingPermissionsSeeder Completed!!\n";
    }

    public function addNewPermissions()
    {
        $permissionGroupId = PermissionGroup::firstOrCreate(['name' => 'Manufacturing'])->id;

        $manufacturingPermissionsArray = [
            // Manufacturing Orders permissions
            [
                'name'          => 'manufacturing.order.create',
                'display_name'  => 'Manufacturing Order Create',
                'permission_group_id'  => $permissionGroupId,
            ],
            [
                'name'          => 'manufacturing.order.edit',
                'display_name'  => 'Manufacturing Order Edit',
                'permission_group_id'  => $permissionGroupId,
            ],
            [
                'name'          => 'manufacturing.order.view',
                'display_name'  => 'Manufacturing Order View',
                'permission_group_id'  => $permissionGroupId,
            ],
            [
                'name'          => 'manufacturing.order.delete',
                'display_name'  => 'Manufacturing Order Delete',
                'permission_group_id'  => $permissionGroupId,
            ],
            // Manufacturing Calculations permissions
            [
                'name'          => 'manufacturing.calculation.create',
                'display_name'  => 'Manufacturing Calculation Create',
                'permission_group_id'  => $permissionGroupId,
            ],
            [
                'name'          => 'manufacturing.calculation.edit',
                'display_name'  => 'Manufacturing Calculation Edit',
                'permission_group_id'  => $permissionGroupId,
            ],
            [
                'name'          => 'manufacturing.calculation.view',
                'display_name'  => 'Manufacturing Calculation View',
                'permission_group_id'  => $permissionGroupId,
            ],
            [
                'name'          => 'manufacturing.calculation.delete',
                'display_name'  => 'Manufacturing Calculation Delete',
                'permission_group_id'  => $permissionGroupId,
            ],
        ];

        foreach ($manufacturingPermissionsArray as $permission) {
            // Validate if the permission exists
            $isPermissionExist = Permission::where('name', $permission['name'])->count();
            $isPermissionExist = $isPermissionExist > 0 ? true : false;

            if (!$isPermissionExist) {
                Permission::firstOrCreate([
                    'name' => $permission['name'],
                    'display_name' => $permission['display_name'],
                    'permission_group_id' => $permission['permission_group_id'],
                    'status' => 1,
                ]);
            }
        }
    }
}
