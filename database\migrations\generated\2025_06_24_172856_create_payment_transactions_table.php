<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_transactions', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->date('transaction_date');
            $table->unsignedBigInteger('payment_type_id')->index('payment_transactions_payment_type_id_foreign');
            $table->unsignedBigInteger('transfer_to_payment_type_id')->nullable()->index('payment_transactions_transfer_to_payment_type_id_foreign');
            $table->string('transaction_type');
            $table->unsignedBigInteger('transaction_id');
            $table->decimal('amount', 20, 4)->default(0);
            $table->string('reference_no')->nullable();
            $table->text('note')->nullable();
            $table->string('payment_from_unique_code')->nullable()->comment('Identify from which form payment done');
            $table->unsignedBigInteger('created_by')->nullable()->index('payment_transactions_created_by_foreign');
            $table->unsignedBigInteger('updated_by')->nullable()->index('payment_transactions_updated_by_foreign');
            $table->timestamps();

            $table->index(['transaction_type', 'transaction_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_transactions');
    }
};
