<?php

return [
    'purchase'                      => 'Purchase',
    'details'                       => 'Purchase Details',
    'list'                          => 'Purchase List',
    'create'                        => 'Create Purchase',
    'code'                          => 'Purchase Code',
    'number'                        => 'Purchase Number',
    'items'                         => 'Purchase Items',
    'update'                        => 'Update Purchase',

    'bill'                          => 'Purchase Bill',
    'bill_no'                       => 'Bill No.',
    'bills'                         => 'Purchase Bills',
    'print'                         => 'Purchase Print',
    'convert_to_purchase'           => 'Convert to Purchase',
    'already_converted'             => 'Purchase Order Already Converted to Purchase Bill',
    'return_to'                     => 'Return To',
    'purchase_invoice_number'       => 'Purchase Invoice Number',
    'debit_note'                    => 'Debit Note',
    'convert_to_return'             => 'Convert to Return',
    'purchase_bill_number'          => 'Purchase Bill Number',
    'convert_to_bill'               => 'Convert to Bill',
    'item_purchase'                 => 'Item Purchase',
    'purchase_report'               => 'Purchase Report',
    'item_purchase_report'          => 'Item Purchase Report',
    'purchase_payment_report'       => 'Purchase Payment Report',
    'purchase_payment'              => 'Purchase Payment',

    'purchase_without_tax'          => 'Purchase Without Tax',
    'purchase_return_without_tax'   => 'Purchase Return Without Tax',
    'purchase_bills'                => 'Purchase Bills',

    'return' => [
                    'return'         => 'Purchase Return/Dr.Note',
                    'create'        => 'Purchase Return Create',
                    'details'       => 'Purchase Return Details',
                    'code'          => 'Return ID',
                    'date'          => 'Return Date',
                    'print'         => 'Purchase Return Print',
                    // 'status'        => 'Purchase Order Status',
                    // 'type'          => 'Purchase Order Type',
                    // 'create'        => 'Create Purchase Order',
                    // 'list'          => 'Purchase Order List',
                    'update'        => 'Update Purchase Return',
                ],

    'order' => [
                    'order'         => 'Purchase Order',
                    'number'        => 'Order Number',
                    'code'          => 'Order ID',
                    'status'        => 'Purchase Order Status',
                    'type'          => 'Purchase Order Type',
                    'details'       => 'Purchase Order Details',
                    'create'        => 'Create Purchase Order',
                    'list'          => 'Purchase Order List',
                    'update'        => 'Update Purchase Order',
                    'print'         => 'Purchase Order Print',
                    'pending'         => 'Pending Purchase Orders',
                    'completed'         => 'Completed Purchase Orders',
                ],

    //1.4
    'add'                        => 'Add Purchase',
    'purchased_items_history'                        => 'Purchased Items History',
    'purchased_items'                        => 'Purchased Items',

];
