<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->date('order_date');
            $table->string('prefix_code')->nullable();
            $table->string('count_id')->nullable();
            $table->string('order_code')->nullable();
            $table->unsignedBigInteger('party_id')->index('orders_party_id_foreign');
            $table->string('order_status')->nullable();
            $table->text('note')->nullable();
            $table->decimal('total_amount', 10)->default(0);
            $table->decimal('paid_amount', 10)->default(0);
            $table->string('payment_status')->nullable();
            $table->unsignedBigInteger('created_by')->nullable()->index('orders_created_by_foreign');
            $table->unsignedBigInteger('updated_by')->nullable()->index('orders_updated_by_foreign');
            $table->timestamps();
            $table->text('schedule_note')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orders');
    }
};
