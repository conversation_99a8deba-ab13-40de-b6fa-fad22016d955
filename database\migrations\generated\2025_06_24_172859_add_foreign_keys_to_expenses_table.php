<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('expenses', function (Blueprint $table) {
            $table->foreign(['created_by'])->references(['id'])->on('users')->onUpdate('no action')->onDelete('no action');
            $table->foreign(['expense_category_id'])->references(['id'])->on('expense_categories')->onUpdate('no action')->onDelete('no action');
            $table->foreign(['expense_subcategory_id'])->references(['id'])->on('expense_subcategories')->onUpdate('no action')->onDelete('no action');
            $table->foreign(['updated_by'])->references(['id'])->on('users')->onUpdate('no action')->onDelete('no action');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('expenses', function (Blueprint $table) {
            $table->dropForeign('expenses_created_by_foreign');
            $table->dropForeign('expenses_expense_category_id_foreign');
            $table->dropForeign('expenses_expense_subcategory_id_foreign');
            $table->dropForeign('expenses_updated_by_foreign');
        });
    }
};
