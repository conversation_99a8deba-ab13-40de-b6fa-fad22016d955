<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('ordered_products', function (Blueprint $table) {
            $table->foreign(['assigned_user_id'])->references(['id'])->on('users')->onUpdate('no action')->onDelete('no action');
            $table->foreign(['order_id'])->references(['id'])->on('orders')->onUpdate('no action')->onDelete('cascade');
            $table->foreign(['service_id'])->references(['id'])->on('services')->onUpdate('no action')->onDelete('no action');
            $table->foreign(['tax_id'])->references(['id'])->on('taxes')->onUpdate('no action')->onDelete('no action');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('ordered_products', function (Blueprint $table) {
            $table->dropForeign('ordered_products_assigned_user_id_foreign');
            $table->dropForeign('ordered_products_order_id_foreign');
            $table->dropForeign('ordered_products_service_id_foreign');
            $table->dropForeign('ordered_products_tax_id_foreign');
        });
    }
};
