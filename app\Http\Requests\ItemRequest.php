<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Validator;
use Illuminate\Validation\Rule;

class ItemRequest extends FormRequest
{
    /**
     * Indicates if the validator should stop on the first rule failure.
     *
     * @var bool
     */
    protected $stopOnFirstFailure = true;

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {

        $rulesArray = [
            'hsn'                             => ['nullable', 'string', 'max:100'],
            'item_category_id'                => ['required'],

            'brand_id'                        => ['nullable'],

            'base_unit_id'                    => ['required'],
            'secondary_unit_id'               => ['required'],
            'conversion_rate'                 => ['required'],
            'description'                     => ['nullable','string', 'max:250'],
            'status'                          => ['required'],
            //Pricing Tab
            'sale_price'                      => ['required', 'numeric'],
            'is_sale_price_with_tax'          => ['required', 'numeric'],

            'sale_price_discount'             => ['nullable', 'numeric'],
            'sale_price_discount_type'        => ['required', 'string', 'max:100'],

            'purchase_price'                  => ['nullable', 'numeric'],
            'is_purchase_price_with_tax'      => ['required', 'numeric'],

            'tax_id'                          => ['numeric'],

            'wholesale_price'                 => ['nullable', 'numeric'],
            'is_wholesale_price_with_tax'     => ['required', 'numeric'],

            //Stock Tab
            'tracking_type'                   => ['required', 'string', 'max:100'],
            'warehouse_id'                    => ['required'],
            'transaction_date'                => ['required'],
            'opening_quantity'                => ['nullable', 'numeric'],
            'serial_number_json'              => ['nullable'],
            'batch_details_json'              => ['nullable'],
            'stock_entry_price'               => ['nullable', 'numeric'],
            'min_stock'                       => ['nullable', 'numeric'],
            'item_location'                   => ['nullable', ],

            // Lens specific fields
            'color'                           => ['nullable', 'string', 'max:255'],
            'material'                        => ['nullable', 'string', 'max:255'],
            'size'                            => ['nullable', 'string', 'max:255'],
            'frame_type'                      => ['nullable', 'string', 'max:255'],
            'frame_shape'                     => ['nullable', 'string', 'max:255'],
            'frame_style'                     => ['nullable', 'string', 'max:255'],
            'lens_type'                       => ['nullable', 'string', 'max:255'],
            'lens_material'                   => ['nullable', 'string', 'max:255'],
            'lens_coating'                    => ['nullable', 'string', 'max:255'],
            'is_photochromic'                 => ['nullable', 'boolean'],
            'is_polarized'                    => ['nullable', 'boolean'],
            'contact_lens_type'               => ['nullable', 'string', 'max:255'],
            'contact_lens_material'           => ['nullable', 'string', 'max:255'],
            'water_content'                   => ['nullable', 'string', 'max:255'],
            'pack_quantity'                   => ['nullable', 'numeric'],
            'lens_code'                       => ['nullable', 'string', 'max:255'],
            'lens_label'                      => ['nullable', 'string', 'max:255'],
            'lens_diameter'                   => ['nullable', 'numeric'],
            'lens_geo'                        => ['nullable', 'string', 'max:255'],
            'lens_index'                      => ['nullable', 'numeric'],
            'purchase_price_ht'               => ['nullable', 'numeric'],
            'selling_price_ht'                => ['nullable', 'numeric'],
            'sphere_min'                      => ['nullable', 'numeric'],
            'sphere_max'                      => ['nullable', 'numeric'],
            'cylinder_min'                    => ['nullable', 'numeric'],
            'cylinder_max'                    => ['nullable', 'numeric'],
            'addition_min'                    => ['nullable', 'numeric'],
            'addition_max'                    => ['nullable', 'numeric'],
            'lens_material_type'              => ['nullable', 'string', 'max:255'],
            'lens_photo'                      => ['nullable', 'string', 'max:255'],
            'lens_supplier'                   => ['nullable', 'string', 'max:255'],
        ];

        if ($this->isMethod('PUT')) {
            $itemId                     = $this->input('item_id');
            //$rulesArray['mrp']           = ['required'];
            $rulesArray['name']          = ['required', 'string', 'max:100', (app('company')['is_item_name_unique']) ? Rule::unique('items')->where('name', $_POST['name'])->ignore($itemId) : null];
            $rulesArray['item_code']     = ['required', 'string', 'max:100', Rule::unique('items')->where('item_code', $_POST['item_code'])->ignore($itemId)];
        }else{
            $rulesArray['name']          = ['required', 'string', 'max:100', (app('company')['is_item_name_unique']) ? Rule::unique('items')->where('name', $_POST['name']) : null];
            $rulesArray['item_code']     = ['required', 'string', 'max:100', Rule::unique('items')->where('item_code', $_POST['item_code'])];
        }

        if ($this->has('sku')) {
            $rulesArray['sku'] = ['nullable', 'string', 'max:100'];
        }

        if ($this->has('mrp')) {
            $rulesArray['mrp'] = ['nullable', 'numeric'];
        }
        return $rulesArray;

    }
    public function messages(): array
    {
        $responseMessages = [];

        if ($this->isMethod('PUT')) {
            $responseMessages['item_id.required']    = 'ID Not found to update record';
        }

        return $responseMessages;
    }
    /**
     * Get the "after" validation callables for the request.
     */
    public function withValidator(Validator $validator)
    {
        $validator->after(function ($validator) {
            $data = $validator->getData();
            $data['sale_price']             = $data['sale_price']??0;
            $data['sale_price_discount']    = $data['sale_price_discount']??0;
            $data['purchase_price']         = $data['purchase_price']??0;
            $data['wholesale_price']        = $data['wholesale_price']??0;
            $data['min_stock']              = $data['min_stock']??0;
            $data['opening_quantity']       = $data['opening_quantity']??0;
            $data['at_price']               = $data['at_price']??0;
            $data['conversion_rate']        = ($data['is_service']) ? 1 : $data['conversion_rate'];
            $data['tracking_type']          = ($data['is_service']) ? 'regular' : $data['tracking_type'];
            $data['min_stock']              = ($data['is_service']) ? 0 : $data['min_stock'];
            $data['item_location']          = ($data['is_service']) ? null : $data['item_location'];
            $data['sku']                    = $data['sku'] ?? null;
            $data['mrp']                    = $data['mrp'] ?? 0;

            // Default values for new lens-related fields
            $data['color']                    = $data['color'] ?? null;
            $data['material']                 = $data['material'] ?? null;
            $data['size']                     = $data['size'] ?? null;
            $data['frame_type']               = $data['frame_type'] ?? null;
            $data['frame_shape']              = $data['frame_shape'] ?? null;
            $data['frame_style']              = $data['frame_style'] ?? null;
            $data['lens_type']                = $data['lens_type'] ?? null;
            $data['lens_material']            = $data['lens_material'] ?? null;
            $data['lens_coating']             = $data['lens_coating'] ?? null;
            $data['contact_lens_type']        = $data['contact_lens_type'] ?? null;
            $data['contact_lens_material']    = $data['contact_lens_material'] ?? null;
            $data['water_content']            = $data['water_content'] ?? null;
            $data['lens_code']                = $data['lens_code'] ?? null;
            $data['lens_label']               = $data['lens_label'] ?? null;
            $data['lens_geo']                 = $data['lens_geo'] ?? null;
            $data['lens_material_type']       = $data['lens_material_type'] ?? null;
            $data['lens_photo']               = $data['lens_photo'] ?? null;
            $data['lens_supplier']            = $data['lens_supplier'] ?? null;

            $data['pack_quantity']            = $data['pack_quantity'] ?? 0;
            $data['lens_diameter']            = $data['lens_diameter'] ?? null;
            $data['lens_index']               = $data['lens_index'] ?? null;
            $data['purchase_price_ht']        = $data['purchase_price_ht'] ?? null;
            $data['selling_price_ht']         = $data['selling_price_ht'] ?? null;
            $data['sphere_min']               = $data['sphere_min'] ?? null;
            $data['sphere_max']               = $data['sphere_max'] ?? null;
            $data['cylinder_min']             = $data['cylinder_min'] ?? null;
            $data['cylinder_max']             = $data['cylinder_max'] ?? null;
            $data['addition_min']             = $data['addition_min'] ?? null;
            $data['addition_max']             = $data['addition_max'] ?? null;
            // Boolean fields 'is_photochromic' and 'is_polarized' will default to false if not present.

            $this->replace($data);
        });
    }
}
