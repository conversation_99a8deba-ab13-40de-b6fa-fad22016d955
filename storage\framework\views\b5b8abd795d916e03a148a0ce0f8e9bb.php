<!--sidebar wrapper -->
		<div class="sidebar-wrapper" data-simplebar="true">
			<div class="sidebar-header">
				<div>
					<img src=<?php echo e(url("/app/getimage/" . app('site')['colored_logo'])); ?> class="logo-icon" alt="logo icon">
				</div>
				<div>
					<h4 class="logo-text"><?php echo e(app('site')['name']); ?></h4>
				</div>
				<div class="toggle-icon ms-auto"><i class='bx bx-arrow-back'></i>
				</div>
			 </div>
			<!--navigation-->
			<ul class="metismenu" id="menu">
				<li>
					<a href="<?php echo e(route('dashboard')); ?>">
						<div class="parent-icon"><i class='bx bx-home-alt'></i>
						</div>
						<div class="menu-title"><?php echo e(__('app.dashboard')); ?></div>
					</a>
				</li>

				<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['customer.create', 'customer.view', 'supplier.create', 'supplier.view'])): ?>
				<li>
					<a href="javascript:;" class="has-arrow">
						<div class="parent-icon"><i class="bx bx-group"></i>
						</div>
						<div class="menu-title"><?php echo e(__('party.contacts')); ?></div>
					</a>
					<ul>

						<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('customer.view')): ?>
						<li class="<?php echo e(request()->is(['party/customer/*', 'party/payment/customer/*'])? 'mm-active' : ''); ?>">
						    <a href="<?php echo e(route('party.list', ['partyType' => 'customer'])); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('customer.customers')); ?></a>
						</li>
						<?php endif; ?>

						<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('supplier.view')): ?>
						<li class="<?php echo e(request()->is(['party/supplier/*', 'party/payment/supplier/*'])? 'mm-active' : ''); ?>">
						    <a href="<?php echo e(route('party.list', ['partyType' => 'supplier'])); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('supplier.suppliers')); ?></a>
						</li>
						<?php endif; ?>
					</ul>
				</li>
				<?php endif; ?>

				<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['sale.invoice.view', 'sale.order.view', 'sale.return.view', 'sale.quotation.view'])): ?>
				<li>
					<a href="javascript:;" class="has-arrow">
						<div class="parent-icon"><i class="bx bx-cart"></i>
						</div>
						<div class="menu-title"><?php echo e(__('sale.sale')); ?></div>
					</a>
					<ul>
						<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('sale.invoice.create')): ?>
						<li class="<?php echo e(request()->is('pos*') ? 'mm-active' : ''); ?>">
											<a href="<?php echo e(route('pos.create')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('sale.pos')); ?></a>
										</li>
						<?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('sale.invoice.view')): ?>
						<li class="<?php echo e(request()->is('sale/invoice/*') ? 'mm-active' : ''); ?>">
											<a href="<?php echo e(route('sale.invoice.list')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('sale.invoices')); ?></a>
										</li>
						<?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('sale.quotation.view')): ?>
						<li class="<?php echo e(request()->is('quotation/*') ? 'mm-active' : ''); ?>">
											<a href="<?php echo e(route('sale.quotation.list')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('sale.quotation.quotations')); ?></a>
										</li>
						<?php endif; ?>

						<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('sale.invoice.view')): ?>
						<li class="<?php echo e(request()->is('payment/in') ? 'mm-active' : ''); ?>">
											<a href="<?php echo e(route('sale.payment.in')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('payment.payment_in')); ?></a>
										</li>
						<?php endif; ?>

						<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('sale.order.view')): ?>
						<li class="<?php echo e(request()->is('sale/order/*') ? 'mm-active' : ''); ?>">
											<a href="<?php echo e(route('sale.order.list')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('sale.order.order')); ?></a>
										</li>
						<?php endif; ?>

						<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('sale.return.view')): ?>
						<li class="<?php echo e(request()->is('sale/return/*') ? 'mm-active' : ''); ?>">
											<a href="<?php echo e(route('sale.return.list')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('sale.return.return')); ?></a>
										</li>
						<?php endif; ?>
					</ul>
				</li>
				<?php endif; ?>

				<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['purchase.bill.view', 'purchase.order.view', 'purchase.return.view'])): ?>
				<li>
					<a href="javascript:;" class="has-arrow">
						<div class="parent-icon"><i class="bx bx-purchase-tag-alt"></i>
						</div>
						<div class="menu-title"><?php echo e(__('purchase.purchase')); ?></div>
					</a>
					<ul>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('purchase.bill.view')): ?>
						<li class="<?php echo e(request()->is('purchase/bill/*') ? 'mm-active' : ''); ?>">
											<a href="<?php echo e(route('purchase.bill.list')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('purchase.bills')); ?></a>
										</li>
						<?php endif; ?>

						<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('purchase.bill.view')): ?>
						<li class="<?php echo e(request()->is('payment/out') ? 'mm-active' : ''); ?>">
											<a href="<?php echo e(route('purchase.payment.out')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('payment.payment_out')); ?></a>
										</li>
						<?php endif; ?>

						<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('purchase.order.view')): ?>
						<li class="<?php echo e(request()->is('purchase/order/*') ? 'mm-active' : ''); ?>">
											<a href="<?php echo e(route('purchase.order.list')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('purchase.order.order')); ?></a>
										</li>
						<?php endif; ?>

						<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('purchase.return.view')): ?>
						<li class="<?php echo e(request()->is('purchase/return/*') ? 'mm-active' : ''); ?>">
											<a href="<?php echo e(route('purchase.return.list')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('purchase.return.return')); ?></a>
										</li>
						<?php endif; ?>
					</ul>
				</li>
				<?php endif; ?>

				





				<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['item.create', 'item.view', 'item.category.create', 'item.category.view', 'item.brand.create', 'item.brand.view'])): ?>
				<li>
					<a href="javascript:;" class="has-arrow">
						<div class="parent-icon"><i class="bx bx-package"></i>
						</div>
						<div class="menu-title"><?php echo e(__('item.items')); ?></div>
					</a>
					<ul>
						<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('item.view')): ?>
						<li class="<?php echo e(request()->is('item/list', 'item/create', 'item/edit*', 'item/transaction*') ? 'mm-active' : ''); ?>">
											<a href="<?php echo e(route('item.list')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('item.list')); ?></a>
										</li>

						<?php endif; ?>
						<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('item.category.view')): ?>
						<li class="<?php echo e(request()->is('item/category/*', 'item/category/*') ? 'mm-active' : ''); ?>">
											<a href="<?php echo e(route('item.category.list')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('item.category.list')); ?></a>
										</li>
						<?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('item.brand.view')): ?>
						<li class="<?php echo e(request()->is('item/brand/*', 'item/brand/*') ? 'mm-active' : ''); ?>">
											<a href="<?php echo e(route('item.brand.list')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('item.brand.list')); ?></a>
										</li>
						<?php endif; ?>
					</ul>
				</li>
				<?php endif; ?>

				

				<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['expense.create', 'expense.view', 'expense.category.view', 'expense.subcategory.view'])): ?>
				<li>
					<a href="javascript:;" class="has-arrow">
						<div class="parent-icon"><i class="bx bx-minus-circle"></i>
						</div>
						<div class="menu-title"><?php echo e(__('expense.expense')); ?></div>
					</a>
					<ul>
						<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('expense.view')): ?>
						<li class="<?php echo e(request()->is('expense/list', 'expense/create','expense/edit*', 'expense/print/*') ? 'mm-active' : ''); ?>">
											<a href="<?php echo e(route('expense.list')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('expense.list')); ?></a>
										</li>
						<?php endif; ?>
						<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('expense.category.view')): ?>
						<li class="<?php echo e(request()->is('expense/category/list', 'expense/category/edit*') ? 'mm-active' : ''); ?>">
											<a href="<?php echo e(route('expense.category.list')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('expense.category.list')); ?></a>
										</li>
						<?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('expense.subcategory.view')): ?>
						<li class="<?php echo e(request()->is('expense/subcategory/list', 'expense/subcategory/edit*') ? 'mm-active' : ''); ?>">
											<a href="<?php echo e(route('expense.subcategory.list')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('expense.subcategory.list')); ?></a>
										</li>
						<?php endif; ?>
					</ul>
				</li>
				<?php endif; ?>

				<?php if(app('company')['is_enable_crm']): ?>
					<li class="menu-label">CRM</li>
					<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['order.create', 'order.view'])): ?>
					<li>
						<a href="javascript:;" class="has-arrow">
							<div class="parent-icon"><i class="bx bx-door-open"></i>
							</div>
							<div class="menu-title"><?php echo e(__('order.orders')); ?></div>
						</a>
						<ul>
							<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('order.create')): ?>
							<li class="<?php echo e(request()->is('order/create') ? 'mm-active' : ''); ?>">
												<a href="<?php echo e(route('order.create')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('order.create')); ?></a>
											</li>
							<?php endif; ?>
							<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('order.view')): ?>
							<li class="<?php echo e(request()->is('order/list', 'order/edit*', 'order/receipt*') ? 'mm-active' : ''); ?>">
												<a href="<?php echo e(route('order.list')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('order.list')); ?></a>
											</li>
							<?php endif; ?>
							<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('service.view')): ?>
							<li class="<?php echo e(request()->is('service/list', 'service/edit*') ? 'mm-active' : ''); ?>">
												<a href="<?php echo e(route('service.list')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('service.list')); ?></a>
											</li>
							<?php endif; ?>
						</ul>
					</li>
					<?php endif; ?>

					<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['schedule.create', 'schedule.view', 'assigned_jobs.create', 'assigned_jobs.view'])): ?>
					<li>
						<a href="javascript:;" class="has-arrow">
							<div class="parent-icon"><i class="bx bx-alarm-add"></i>
							</div>
							<div class="menu-title"><?php echo e(__('schedule.scheduling')); ?></div>
						</a>
						<ul>
							<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('schedule.view')): ?>
							<li class="<?php echo e(request()->is('schedule/list', 'schedule/edit*', 'schedule/receipt*', 'order/timeline/*') ? 'mm-active' : ''); ?>">
								<a href="<?php echo e(route('schedule.list')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('order.list')); ?></a>
							</li>
							<?php endif; ?>

							<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('assigned_jobs.view')): ?>
							<li class="<?php echo e(request()->is('assigned-jobs/list', 'assigned-jobs/update*') ? 'mm-active' : ''); ?>">
								<a href="<?php echo e(route('assigned_jobs.list')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('schedule.jobs')); ?></a>
							</li>
							<?php endif; ?>

						</ul>
					</li>
					<?php endif; ?>
				<?php endif; ?>
				<li class="menu-label">CORE</li>
				<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['transaction.cash.view', 'transaction.cheque.view', 'transaction.bank.view'])): ?>
				<li>
					<a href="javascript:;" class="has-arrow">
						<div class="parent-icon"><i class="bx bx-wallet-alt"></i>
						</div>
						<div class="menu-title"><?php echo e(__('payment.cash_and_bank')); ?></div>
					</a>
					<ul>
						<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('transaction.cash.view')): ?>
						<li class="<?php echo e(request()->is('transaction/cash/list') ? 'mm-active' : ''); ?>">
											<a href="<?php echo e(route('transaction.cash.list')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('payment.cash_in_hand')); ?></a>
										</li>
						<?php endif; ?>

						<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('transaction.cheque.view')): ?>
						<li class="<?php echo e(request()->is('transaction/cheque/list') ? 'mm-active' : ''); ?>">
											<a href="<?php echo e(route('transaction.cheque.list')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('payment.cheques')); ?></a>
										</li>
						<?php endif; ?>

						<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('transaction.bank.view')): ?>
						<li class="<?php echo e(request()->is('transaction/bank/list') ? 'mm-active' : ''); ?>">
											<a href="<?php echo e(route('transaction.bank.list')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('payment.bank')); ?></a>
										</li>
						<?php endif; ?>
					</ul>
				</li>
				<?php endif; ?>

				<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['warehouse.view', 'stock_transfer.view'])): ?>
				<li>
					<a href="javascript:;" class="has-arrow">
						<div class="parent-icon"><i class="bx bx-building"></i>
						</div>
						<div class="menu-title"><?php echo e(__('warehouse.warehouse')); ?></div>
					</a>
					<ul>
						<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('warehouse.view')): ?>
						<li class="<?php echo e(request()->is('warehouse*') ? 'mm-active' : ''); ?>">
							<a href="<?php echo e(route('warehouse.list')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('warehouse.warehouses')); ?></a>
						</li>
						<?php endif; ?>

						<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('stock_transfer.view')): ?>
						<li class="<?php echo e(request()->is('stock-transfer/list') ? 'mm-active' : ''); ?>">
											<a href="<?php echo e(route('stock_transfer.list')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('warehouse.stock_transfer')); ?></a>
										</li>
						<?php endif; ?>


					</ul>
				</li>
				<?php endif; ?>

				<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['import.item', 'import.party', 'generate.barcode'])): ?>
				<li>
					<a href="javascript:;" class="has-arrow">
						<div class="parent-icon"><i class="bx bx-wrench"></i>
						</div>
						<div class="menu-title"><?php echo e(__('app.utilities')); ?></div>
					</a>
					<ul>
						<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('import.item')): ?>
						<li class="<?php echo e(request()->is('import/item') ? 'mm-active' : ''); ?>">
											<a href="<?php echo e(route('import.items')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('item.import_items')); ?></a>
										</li>
						<?php endif; ?>
						<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('import.party')): ?>
						<li class="<?php echo e(request()->is('import/party') ? 'mm-active' : ''); ?>">
											<a href="<?php echo e(route('import.party')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('party.import_contacts')); ?></a>
										</li>
						<?php endif; ?>
						<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('generate.barcode')): ?>
						<li class="<?php echo e(request()->is('item/generate/barcode') ? 'mm-active' : ''); ?>">
											<a href="<?php echo e(route('generate.barcode')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('item.generate_barcode')); ?></a>
										</li>
						<?php endif; ?>
					</ul>
				</li>
				<?php endif; ?>

				<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['account.create', 'account.view', 'account.group.create', 'account.group.view'])): ?>
				<li class="d-none">
					<a href="javascript:;" class="has-arrow">
						<div class="parent-icon"><i class="bx bx-money"></i>
						</div>
						<div class="menu-title"><?php echo e(__('account.accounts')); ?></div>
					</a>
					<ul>
						<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('account.view')): ?>
						<li class="<?php echo e(request()->is('account/list', 'account/create', 'account/edit*') ? 'mm-active' : ''); ?>">
											<a href="<?php echo e(route('account.list')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('account.list')); ?></a>
										</li>

						<?php endif; ?>
						<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('account.group.view')): ?>
						<li class="<?php echo e(request()->is('account/group/list', 'account/group/create', 'account/group/edit*') ? 'mm-active' : ''); ?>">
											<a href="<?php echo e(route('account.group.list')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('account.group.list')); ?></a>
										</li>
						<?php endif; ?>
					</ul>
				</li>
				<?php endif; ?>

				<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['profile.edit', 'user.view', 'role.view', 'permission.view', 'permission.group.view'])): ?>
                <li>
					<a href="javascript:;" class="has-arrow">
						<div class="parent-icon"><i class="bx bx-group"></i>
						</div>
						<div class="menu-title"><?php echo e(__('user.users')); ?></div>
					</a>
					<ul>
						<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('profile.edit')): ?>
						<li class="<?php echo e(request()->is('profile') ? 'mm-active' : ''); ?>">
											<a href="<?php echo e(route('user.profile')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('user.profile')); ?></a>
										</li>
						<?php endif; ?>
						<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('user.view')): ?>
						<li class="<?php echo e(request()->is('users*') ? 'mm-active' : ''); ?>">
											<a href="<?php echo e(route('users.list')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('user.users')); ?></a>
										</li>
						<?php endif; ?>
						<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('role.view')): ?>
                        <li class="<?php echo e(request()->is('role-and-permission/role*') ? 'mm-active' : ''); ?>">
							<a href="<?php echo e(route('roles.list')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('app.roles')); ?></a>
						</li>
						<?php endif; ?>
					</ul>
					<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['permission.view', 'permission.group.view'])): ?>
					
					<?php endif; ?>
				</li>
				<?php endif; ?>

				<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['sms.create', 'sms.template.view'])): ?>
				<li>
					<a href="javascript:;" class="has-arrow">
						<div class="parent-icon"><i class="bx bx-message"></i>
						</div>
						<div class="menu-title"><?php echo e(__('message.sms')); ?></div>
					</a>
					<ul>
						<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('sms.create')): ?>
						<li class="<?php echo e(request()->is('sms/create') ? 'mm-active' : ''); ?>">
											<a href="<?php echo e(route('sms.create')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('message.create_sms')); ?></a>
										</li>
						<?php endif; ?>
						<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('sms.template.view')): ?>
						<li class="<?php echo e(request()->is('sms/template/list','sms/template/create', 'sms/template/edit*') ? 'mm-active' : ''); ?>">
											<a href="<?php echo e(route('sms.template.list')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('message.templates')); ?></a>
										</li>
						<?php endif; ?>
					</ul>
				</li>
				<?php endif; ?>

				<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['email.create', 'email.template.view'])): ?>
                <li>
                    <a href="javascript:;" class="has-arrow">
                        <div class="parent-icon"><i class="bx bx-envelope"></i>
                        </div>
                        <div class="menu-title"><?php echo e(__('message.email')); ?></div>
                    </a>
                    <ul>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('email.create')): ?>
                        <li class="<?php echo e(request()->is('email/create') ? 'mm-active' : ''); ?>">
                                            <a href="<?php echo e(route('email.create')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('message.create_email')); ?></a>
                                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('email.template.view')): ?>
                        <li class="<?php echo e(request()->is('email/template/list','email/template/create', 'email/template/edit*') ? 'mm-active' : ''); ?>">
                                            <a href="<?php echo e(route('email.template.list')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('message.templates')); ?></a>
                                        </li>
                        <?php endif; ?>
                    </ul>
                </li>
                <?php endif; ?>

                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['report.*'])): ?>
                <li>
                    <a href="javascript:;" class="has-arrow">
                        <div class="parent-icon"><i class="bx bx-bar-chart-square"></i>
                        </div>
                        <div class="menu-title"><?php echo e(__('app.reports')); ?></div>
                    </a>
                    <ul>
                    	<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('report.profit_and_loss')): ?>
                        <li class="<?php echo e(request()->is('report/profit-and-loss') ? 'mm-active' : ''); ?>">
                                            <a href="<?php echo e(route('report.profit_and_loss')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('account.profit_and_loss')); ?></a>
                                        </li>
                        <?php endif; ?>
                    	
						<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['report.item.transaction.batch', 'report.item.transaction.serial', 'report.item.transaction.general'])): ?>

							<li> <a class="has-arrow" href="javascript:;"><i class='bx bx-radio-circle'></i><?php echo e(__('item.item_transaction')); ?></a>
										<ul>
											<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('report.item.transaction.batch')): ?>
											<li class="<?php echo e(request()->is('report/item-transaction/batch') ? 'mm-active' : ''); ?>">
												<a href="<?php echo e(route('report.item.transaction.batch')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('item.batch_wise')); ?></a>
											</li>
											<?php endif; ?>
											<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('report.item.transaction.serial')): ?>
											<li class="<?php echo e(request()->is('report/item-transaction/serial') ? 'mm-active' : ''); ?>">
												<a href="<?php echo e(route('report.item.transaction.serial')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('item.serial_or_imei')); ?></a>
											</li>
											<?php endif; ?>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('report.item.transaction.general')): ?>
                                            <li class="<?php echo e(request()->is('report/item-transaction/general') ? 'mm-active' : ''); ?>">
                                                <a href="<?php echo e(route('report.item.transaction.general')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('app.general')); ?></a>
                                            </li>
                                            <?php endif; ?>
										</ul>
							</li>

						<?php endif; ?>
						<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['report.purchase', 'report.purchase.item'])): ?>
							<li> <a class="has-arrow" href="javascript:;"><i class='bx bx-radio-circle'></i><?php echo e(__('purchase.purchase')); ?></a>
										<ul>
											<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('report.purchase')): ?>
											<li class="<?php echo e(request()->is('report/purchase') ? 'mm-active' : ''); ?>">
												<a href="<?php echo e(route('report.purchase')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('purchase.purchase')); ?></a>
											</li>
											<?php endif; ?>
											<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('report.purchase.item')): ?>
											<li class="<?php echo e(request()->is('report/purchase/item') ? 'mm-active' : ''); ?>">
												<a href="<?php echo e(route('report.purchase.item')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('purchase.item_purchase')); ?></a>
											</li>
											<?php endif; ?>
											<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('report.purchase.payment')): ?>
											<li class="<?php echo e(request()->is('report/purchase/payment') ? 'mm-active' : ''); ?>">
												<a href="<?php echo e(route('report.purchase.payment')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('app.payment')); ?></a>
											</li>
											<?php endif; ?>
										</ul>
							</li>
						<?php endif; ?>
						<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['report.sale', 'report.sale.item'])): ?>
							<li> <a class="has-arrow" href="javascript:;"><i class='bx bx-radio-circle'></i><?php echo e(__('sale.sale')); ?></a>
										<ul>
											<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('report.sale')): ?>
											<li class="<?php echo e(request()->is('report/sale') ? 'mm-active' : ''); ?>">
												<a href="<?php echo e(route('report.sale')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('sale.sale')); ?></a>
											</li>
											<?php endif; ?>
											<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('report.sale.item')): ?>
											<li class="<?php echo e(request()->is('report/sale/item') ? 'mm-active' : ''); ?>">
												<a href="<?php echo e(route('report.sale.item')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('sale.item_sale')); ?></a>
											</li>
											<?php endif; ?>
											<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('report.sale.payment')): ?>
											<li class="<?php echo e(request()->is('report/sale/payment') ? 'mm-active' : ''); ?>">
												<a href="<?php echo e(route('report.sale.payment')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('app.payment')); ?></a>
											</li>
											<?php endif; ?>
										</ul>
							</li>
						<?php endif; ?>

						<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['report.customer.due.payment', 'report.supplier.due.payment'])): ?>
							<li> <a class="has-arrow" href="javascript:;"><i class='bx bx-radio-circle'></i><?php echo e(__('payment.due_payments')); ?>&nbsp;<span class="badge bg-primary">New</span></a>
										<ul>
											<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('report.customer.due.payment')): ?>
											<li class="<?php echo e(request()->is('report/customer/due') ? 'mm-active' : ''); ?>">
												<a href="<?php echo e(route('report.customer.due.payment')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('customer.customer')); ?></a>
											</li>
											<?php endif; ?>
											<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('report.supplier.due.payment')): ?>
											<li class="<?php echo e(request()->is('report/supplier/due') ? 'mm-active' : ''); ?>">
												<a href="<?php echo e(route('report.supplier.due.payment')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('supplier.supplier')); ?></a>
											</li>
											<?php endif; ?>
										</ul>
							</li>
						<?php endif; ?>

						<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['report.expense', 'report.expense.item'])): ?>
							<li> <a class="has-arrow" href="javascript:;"><i class='bx bx-radio-circle'></i><?php echo e(__('expense.expense')); ?></a>
										<ul>
											<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('report.expense')): ?>
											<li class="<?php echo e(request()->is('report/expense') ? 'mm-active' : ''); ?>">
												<a href="<?php echo e(route('report.expense')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('expense.expense')); ?></a>
											</li>
											<?php endif; ?>
											<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('report.expense.item')): ?>
											<li class="<?php echo e(request()->is('report/expense/item') ? 'mm-active' : ''); ?>">
												<a href="<?php echo e(route('report.expense.item')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('expense.item')); ?></a>
											</li>
											<?php endif; ?>
											<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('report.expense.payment')): ?>
											<li class="<?php echo e(request()->is('report/expense/payment') ? 'mm-active' : ''); ?>">
												<a href="<?php echo e(route('report.expense.payment')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('app.payment')); ?></a>
											</li>
											<?php endif; ?>
										</ul>
							</li>
						<?php endif; ?>

						<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['report.transaction.cashflow', 'report.transaction.bank-statement'])): ?>
							<li> <a class="has-arrow" href="javascript:;"><i class='bx bx-radio-circle'></i><?php echo e(__('app.transactions')); ?></a>
										<ul>
											<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('report.transaction.cashflow')): ?>
											<li class="<?php echo e(request()->is('report/transaction/cashflow') ? 'mm-active' : ''); ?>">
												<a href="<?php echo e(route('report.transaction.cashflow')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('payment.cash_flow')); ?></a>
											</li>
											<?php endif; ?>
											<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('report.transaction.bank-statement')): ?>
											<li class="<?php echo e(request()->is('report/transaction/bank-statement') ? 'mm-active' : ''); ?>">
												<a href="<?php echo e(route('report.transaction.bank-statement')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('payment.bank_statement')); ?></a>
											</li>
											<?php endif; ?>
										</ul>
							</li>
						<?php endif; ?>

						<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['report.gst*'])): ?>
							<li> <a class="has-arrow" href="javascript:;"><i class='bx bx-radio-circle'></i><?php echo e(__('item.gst')); ?></a>
										<ul>
											<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('report.gstr1')): ?>
											<li class="<?php echo e(request()->is('report/gstr-1') ? 'mm-active' : ''); ?>">
												<a href="<?php echo e(route('report.gstr-1')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('item.gstr-1')); ?></a>
											</li>
											<?php endif; ?>
											<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('report.gstr2')): ?>
											<li class="<?php echo e(request()->is('report/gstr-2') ? 'mm-active' : ''); ?>">
												<a href="<?php echo e(route('report.gstr-2')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('item.gstr-2')); ?></a>
											</li>
											<?php endif; ?>
										</ul>
							</li>
						<?php endif; ?>
						<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['report.stock_transfer', 'report.stock_transfer.item'])): ?>
							<li> <a class="has-arrow" href="javascript:;"><i class='bx bx-radio-circle'></i><?php echo e(__('warehouse.stock_transfer')); ?></a>
										<ul>
											<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('report.stock_transfer')): ?>
											<li class="<?php echo e(request()->is('report/stock-transfer') ? 'mm-active' : ''); ?>">
												<a href="<?php echo e(route('report.stock_transfer')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('warehouse.stock_transfer')); ?></a>
											</li>
											<?php endif; ?>
											<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('report.stock_transfer.item')): ?>
											<li class="<?php echo e(request()->is('report/stock-transfer/item') ? 'mm-active' : ''); ?>">
												<a href="<?php echo e(route('report.stock_transfer.item')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('item.item_wise')); ?></a>
											</li>
											<?php endif; ?>
										</ul>
							</li>
						<?php endif; ?>

						<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['report.stock_report.*'])): ?>
							<li> <a class="has-arrow" href="javascript:;"><i class='bx bx-radio-circle'></i><?php echo e(__('item.stock_report')); ?>&nbsp;<span class="badge bg-primary">New</span></a>
										<ul>
											<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('report.stock_report.item.batch')): ?>
											<li class="<?php echo e(request()->is('report/stock-report/batch') ? 'mm-active' : ''); ?>">
												<a href="<?php echo e(route('report.stock_report.item.batch')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('item.batch_wise')); ?></a>
											</li>
											<?php endif; ?>
											<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('report.stock_report.item.serial')): ?>
											<li class="<?php echo e(request()->is('report/stock-report/serial') ? 'mm-active' : ''); ?>">
												<a href="<?php echo e(route('report.stock_report.item.serial')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('item.serial_or_imei')); ?></a>
											</li>
											<?php endif; ?>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('report.stock_report.item.general')): ?>
                                            <li class="<?php echo e(request()->is('report/stock-report/general') ? 'mm-active' : ''); ?>">
                                                <a href="<?php echo e(route('report.stock_report.item.general')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('app.general')); ?></a>
                                            </li>
                                            <?php endif; ?>
										</ul>
							</li>
						<?php endif; ?>

						<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('report.expired.item')): ?>
                        <li class="<?php echo e(request()->is('report/expired/item') ? 'mm-active' : ''); ?>">
                                            <a href="<?php echo e(route('report.expired.item')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('item.expired_item_report')); ?></a>
                                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('report.reorder.item')): ?>
                        <li class="<?php echo e(request()->is('report/reorder/item') ? 'mm-active' : ''); ?>">
                                            <a href="<?php echo e(route('report.reorder.item')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('item.reorder_item_report')); ?></a>
                                        </li>
                        <?php endif; ?>



                        <?php if(app('company')['is_enable_crm']): ?>
	                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('report.order')): ?>
	                        <li class="<?php echo e(request()->is('report/order') ? 'mm-active' : ''); ?>">
	                                            <a href="<?php echo e(route('report.order')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('order.report')); ?></a>
	                                        </li>
	                        <?php endif; ?>
	                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('report.order.payment')): ?>
	                        <li class="<?php echo e(request()->is('report/order/payment') ? 'mm-active' : ''); ?>">
	                                            <a href="<?php echo e(route('report.order.payment')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('order.payments')); ?></a>
	                                        </li>
	                        <?php endif; ?>
	                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('report.job.status')): ?>
	                        <li class="<?php echo e(request()->is('report/job-status') ? 'mm-active' : ''); ?>">
	                                            <a href="<?php echo e(route('report.job.status')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('order.job-status')); ?></a>
	                                        </li>
	                        <?php endif; ?>
	                    <?php endif; ?>

                    </ul>
                </li>
                <?php endif; ?>

				<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['tax.view', 'app.settings.edit', 'company.edit', 'payment.type.view', 'unit.view', 'language.view'])): ?>
				<li>
					<a href="javascript:;" class="has-arrow">
						<div class="parent-icon"><i class="bx bx-cog"></i>
						</div>
						<div class="menu-title"><?php echo e(__('app.settings')); ?></div>
					</a>
					<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['app.settings.edit'])): ?>
					<ul>
						<li class="<?php echo e(request()->is('settings/app') ? 'mm-active' : ''); ?>">
							<a href="<?php echo e(route('settings.app')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('app.app_settings')); ?></a>
						</li>
					</ul>
					<?php endif; ?>
					<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['company.edit'])): ?>
					<ul>
						<li class="<?php echo e(request()->is('company') ? 'mm-active' : ''); ?>">
							<a href="<?php echo e(route('company')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('app.company')); ?></a>
						</li>
					</ul>
					<?php endif; ?>
					<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['tax.view'])): ?>
					<ul>
						<li class="<?php echo e(request()->is('tax*') ? 'mm-active' : ''); ?>">
							<a href="<?php echo e(route('tax.list')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('tax.tax_rates')); ?></a>
						</li>
					</ul>
					<?php endif; ?>

					<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['payment.type.view'])): ?>
					<ul>
						<li class="<?php echo e(request()->is('payment*') ? 'mm-active' : ''); ?>">
							<a href="<?php echo e(route('payment.types.list')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('payment.bank_accounts')); ?></a>
						</li>
					</ul>
					<?php endif; ?>
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['currency.view'])): ?>
					<ul>
						<li class="<?php echo e(request()->is('currency*') ? 'mm-active' : ''); ?>">
							<a href="<?php echo e(route('currency.list')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('currency.list')); ?></a>
						</li>
					</ul>
					<?php endif; ?>
					<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['unit.view'])): ?>
					<ul>
						<li class="<?php echo e(request()->is('unit*') ? 'mm-active' : ''); ?>">
							<a href="<?php echo e(route('unit.list')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('unit.list')); ?></a>
						</li>
					</ul>
					<?php endif; ?>
					<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['language.view'])): ?>
					<ul>
						<li class="<?php echo e(request()->is('language*') ? 'mm-active' : ''); ?>">
							<a href="<?php echo e(route('language.list')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('language.languages')); ?></a>
						</li>
					</ul>
					<?php endif; ?>
				</li>
				<?php endif; ?>

                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['manufacturing.order.view', 'manufacturing.calculation.view'])): ?>
                <li class="menu-label">MANUFACTURING</li>
                <li>
                    <a href="javascript:;" class="has-arrow">
                        <div class="parent-icon"><i class="bx bx-cog"></i>
                        </div>
                        <div class="menu-title"><?php echo e(__('manufacturing.manufacturing')); ?></div>
                    </a>
                    <ul>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manufacturing.order.view')): ?>
                        <li class="<?php echo e(request()->is('manufacturing/order*') ? 'mm-active' : ''); ?>">
                            <a href="<?php echo e(route('manufacturing.order.index')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('manufacturing.manufacturing_orders')); ?></a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manufacturing.calculation.view')): ?>
                        <li class="<?php echo e(request()->is('manufacturing/calculations*') ? 'mm-active' : ''); ?>">
                            <a href="<?php echo e(route('manufacturing-calculations.index')); ?>"><i class='bx bx-radio-circle'></i><?php echo e(__('manufacturing.calculation_rules')); ?></a>
                        </li>
                        <?php endif; ?>
                    </ul>
                </li>
                <?php endif; ?>

                <li class="menu-label">OTHER</li>

                <li class="bg-light">
					<a href="javascript:void(0);" id="clearCache">
						<div class="parent-icon text-primary"><i class='bx bx-refresh '></i>
						</div>
						<div class="menu-title"><?php echo e(__('app.clear_cache')); ?></div>
					</a>
				</li>

				<?php if(config('demo.enabled')): ?>
				<li class="bg-light">
					<a href="https://delta.creatantech.com/documentation" target="_blank">
						<div class="parent-icon text-primary"><i class='bx bx-folder '></i>
						</div>
						<div class="menu-title"><?php echo e(__('app.documentation')); ?></div>
					</a>
				</li>
				<?php endif; ?>

			</ul>
			<!--end navigation-->
		</div>
		<!--end sidebar wrapper -->
<?php /**PATH D:\www\delta\resources\views/layouts/navigation.blade.php ENDPATH**/ ?>