<?php

namespace App\Http\Controllers;

use App\Models\ManufacturingCalculation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ManufacturingCalculationController extends Controller
{
    public function __construct()
    {
        $this->middleware('can:manufacturing.calculation.view')->only(['index', 'show']);
        $this->middleware('can:manufacturing.calculation.create')->only(['create', 'store']);
        $this->middleware('can:manufacturing.calculation.edit')->only(['edit', 'update']);
        $this->middleware('can:manufacturing.calculation.delete')->only(['destroy']);
    }

    /**
     * Get calculation rule based on parameters
     */
    public function getRule(Request $request)
    {
        try {
            $request->validate([
                'height_type' => 'required|in:simple,double',
                'curtain_finish' => 'required|in:Wave,PP,PF',
                'manipulation' => 'nullable|in:Moteur,<PERSON><PERSON>',
                'positioning_type' => 'nullable|in:lateral,central',
            ]);

            // Debug logging
            Log::info('getRule request:', [
                'height_type' => $request->height_type,
                'curtain_finish' => $request->curtain_finish,
                'manipulation' => $request->manipulation,
                'positioning_type' => $request->positioning_type
            ]);

            // Check all available rules
            $allRules = ManufacturingCalculation::where('is_active', true)->get();
            Log::info('Available rules count:', ['count' => $allRules->count()]);

            foreach ($allRules as $availableRule) {
                Log::info('Available rule:', [
                    'id' => $availableRule->id,
                    'height_type' => $availableRule->height_type,
                    'curtain_finish' => $availableRule->curtain_finish,
                    'manipulation' => $availableRule->manipulation,
                    'positioning_type' => $availableRule->positioning_type
                ]);
            }

            $rule = ManufacturingCalculation::getRule(
                $request->height_type,
                $request->curtain_finish,
                $request->manipulation,
                $request->positioning_type
            );

            \Log::info('Found rule:', ['rule' => $rule ? $rule->toArray() : null]);

            return response()->json(['rule' => $rule]);
        } catch (\Exception $e) {
            Log::error('Error in getRule:', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);
            return response()->json(['error' => 'Failed to get calculation rule'], 500);
        }
    }

    /**
     * Display a listing of calculation rules
     */
    public function index()
    {
        $rules = ManufacturingCalculation::all();
        return view('manufacturing_calculations.index', compact('rules'));
    }

    /**
     * Show the form for creating a new calculation rule
     */
    public function create()
    {
        return view('manufacturing_calculations.create');
    }

    /**
     * Store a newly created calculation rule
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'height_type' => 'required|in:simple,double',
            'curtain_finish' => 'required|in:Wave,PP,PF',
            'manipulation' => 'nullable|in:Moteur,Manuelle',
            'positioning_type' => 'nullable|in:lateral,central',
            'height_deduction' => 'required|numeric|min:0|max:100',
            'formula_description' => 'required|string|max:255',
            'agrafes_spacing' => 'nullable|integer|min:1|max:100',
        ]);

        ManufacturingCalculation::create($validated);

        return redirect()->route('manufacturing-calculations.index')
            ->with('success', 'Calculation rule created successfully.');
    }

    /**
     * Show the form for editing the calculation rule
     */
    public function edit(ManufacturingCalculation $calculation)
    {
        return view('manufacturing_calculations.edit', compact('calculation'));
    }

    /**
     * Update the calculation rule
     */
    public function update(Request $request, ManufacturingCalculation $calculation)
    {
        $validated = $request->validate([
            'height_type' => 'required|in:simple,double',
            'curtain_finish' => 'required|in:Wave,PP,PF',
            'manipulation' => 'nullable|in:Moteur,Manuelle',
            'positioning_type' => 'nullable|in:lateral,central',
            'height_deduction' => 'required|numeric|min:0|max:100',
            'formula_description' => 'required|string|max:255',
            'agrafes_spacing' => 'nullable|integer|min:1|max:100',
            'is_active' => 'boolean',
        ]);

        $calculation->update($validated);

        return redirect()->route('manufacturing-calculations.index')
            ->with('success', 'Calculation rule updated successfully.');
    }
} 