<?php

namespace App\Http\Controllers;

use App\Models\ManufacturingCalculation;
use Illuminate\Http\Request;

class ManufacturingCalculationController extends Controller
{
    /**
     * Get calculation rule based on parameters
     */
    public function getRule(Request $request)
    {
        try {
            $request->validate([
                'height_type' => 'required|in:simple,double',
                'curtain_finish' => 'required|in:Wave,PP,PF',
                'manipulation' => 'nullable|in:Moteur,Manuelle',
            ]);

            $rule = ManufacturingCalculation::getRule(
                $request->height_type,
                $request->curtain_finish,
                $request->manipulation
            );

            return response()->json(['rule' => $rule]);
        } catch (\Exception $e) {
            \Log::error('Error in getRule:', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);
            return response()->json(['error' => 'Failed to get calculation rule'], 500);
        }
    }

    /**
     * Display a listing of calculation rules
     */
    public function index()
    {
        $rules = ManufacturingCalculation::all();
        return view('manufacturing_calculations.index', compact('rules'));
    }

    /**
     * Show the form for creating a new calculation rule
     */
    public function create()
    {
        return view('manufacturing_calculations.create');
    }

    /**
     * Store a newly created calculation rule
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'height_type' => 'required|in:simple,double',
            'curtain_finish' => 'required|in:Wave,PP,PF',
            'manipulation' => 'nullable|in:Moteur,Manuelle',
            'height_deduction' => 'required|numeric|min:0|max:100',
            'formula_description' => 'required|string|max:255',
            'agrafes_spacing' => 'nullable|integer|min:1|max:100',
        ]);

        ManufacturingCalculation::create($validated);

        return redirect()->route('manufacturing-calculations.index')
            ->with('success', 'Calculation rule created successfully.');
    }

    /**
     * Show the form for editing the calculation rule
     */
    public function edit(ManufacturingCalculation $calculation)
    {
        return view('manufacturing_calculations.edit', compact('calculation'));
    }

    /**
     * Update the calculation rule
     */
    public function update(Request $request, ManufacturingCalculation $calculation)
    {
        $validated = $request->validate([
            'height_type' => 'required|in:simple,double',
            'curtain_finish' => 'required|in:Wave,PP,PF',
            'manipulation' => 'nullable|in:Moteur,Manuelle',
            'height_deduction' => 'required|numeric|min:0|max:100',
            'formula_description' => 'required|string|max:255',
            'agrafes_spacing' => 'nullable|integer|min:1|max:100',
            'is_active' => 'boolean',
        ]);

        $calculation->update($validated);

        return redirect()->route('manufacturing-calculations.index')
            ->with('success', 'Calculation rule updated successfully.');
    }
} 