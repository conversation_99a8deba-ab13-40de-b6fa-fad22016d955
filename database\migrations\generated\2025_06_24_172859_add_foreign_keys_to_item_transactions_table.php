<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('item_transactions', function (Blueprint $table) {
            $table->foreign(['created_by'])->references(['id'])->on('users')->onUpdate('no action')->onDelete('no action');
            $table->foreign(['item_id'])->references(['id'])->on('items')->onUpdate('no action')->onDelete('no action');
            $table->foreign(['tax_id'])->references(['id'])->on('taxes')->onUpdate('no action')->onDelete('no action');
            $table->foreign(['unit_id'])->references(['id'])->on('units')->onUpdate('no action')->onDelete('no action');
            $table->foreign(['updated_by'])->references(['id'])->on('users')->onUpdate('no action')->onDelete('no action');
            $table->foreign(['warehouse_id'])->references(['id'])->on('warehouses')->onUpdate('no action')->onDelete('no action');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('item_transactions', function (Blueprint $table) {
            $table->dropForeign('item_transactions_created_by_foreign');
            $table->dropForeign('item_transactions_item_id_foreign');
            $table->dropForeign('item_transactions_tax_id_foreign');
            $table->dropForeign('item_transactions_unit_id_foreign');
            $table->dropForeign('item_transactions_updated_by_foreign');
            $table->dropForeign('item_transactions_warehouse_id_foreign');
        });
    }
};
