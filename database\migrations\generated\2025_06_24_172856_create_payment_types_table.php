<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_types', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('unique_code')->nullable();
            $table->string('name');
            $table->string('account_number')->nullable();
            $table->string('bank_code')->nullable();
            $table->text('description')->nullable();
            $table->unsignedBigInteger('user_id')->nullable()->index('payment_types_user_id_foreign');
            $table->boolean('print_bit')->default(false);
            $table->boolean('status')->default(true);
            $table->boolean('is_deletable')->default(true);
            $table->unsignedBigInteger('created_by')->nullable()->index('payment_types_created_by_foreign');
            $table->unsignedBigInteger('updated_by')->nullable()->index('payment_types_updated_by_foreign');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_types');
    }
};
