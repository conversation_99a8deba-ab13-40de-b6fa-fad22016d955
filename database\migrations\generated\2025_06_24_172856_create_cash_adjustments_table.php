<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cash_adjustments', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->date('adjustment_date');
            $table->string('adjustment_type');
            $table->unsignedBigInteger('payment_type_id')->index('cash_adjustments_payment_type_id_foreign');
            $table->decimal('amount', 20, 4)->default(0);
            $table->text('note')->nullable();
            $table->unsignedBigInteger('created_by')->nullable()->index('cash_adjustments_created_by_foreign');
            $table->unsignedBigInteger('updated_by')->nullable()->index('cash_adjustments_updated_by_foreign');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cash_adjustments');
    }
};
