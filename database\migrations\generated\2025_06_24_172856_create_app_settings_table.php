<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('app_settings', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('application_name');
            $table->string('footer_text');
            $table->string('colored_logo')->nullable();
            $table->string('light_logo')->nullable();
            $table->string('active_sms_api')->nullable();
            $table->timestamps();
            $table->string('fevicon')->nullable();
            $table->unsignedBigInteger('language_id')->nullable()->index('app_settings_language_id_foreign');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('app_settings');
    }
};
