<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('status_histories', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->date('status_date');
            $table->string('status');
            $table->text('note')->nullable();
            $table->string('statusable_type');
            $table->unsignedBigInteger('statusable_id');
            $table->unsignedBigInteger('created_by')->index('status_histories_created_by_foreign');
            $table->unsignedBigInteger('updated_by')->index('status_histories_updated_by_foreign');
            $table->timestamps();

            $table->index(['statusable_type', 'statusable_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('status_histories');
    }
};
