<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ManufacturingCalculation extends Model
{
    use HasFactory;

    protected $fillable = [
        'height_type',
        'curtain_finish',
        'manipulation',
        'height_deduction',
        'formula_description',
        'agrafes_spacing',
        'is_active'
    ];

    protected $casts = [
        'height_deduction' => 'decimal:2',
        'agrafes_spacing' => 'integer',
        'is_active' => 'boolean'
    ];

    /**
     * Get calculation rule for given parameters
     */
    public static function getRule($heightType, $curtainFinish, $manipulation)
    {
        return self::where('height_type', $heightType)
            ->where('curtain_finish', $curtainFinish)
            ->where('manipulation', $manipulation)
            ->where('is_active', true)
            ->first();
    }
} 