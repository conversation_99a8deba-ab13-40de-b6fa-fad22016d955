{"name": "jquery-ui", "title": "jQuery <PERSON>", "description": "A curated set of user interface interactions, effects, widgets, and themes built on top of the jQuery JavaScript Library.", "version": "1.13.3", "homepage": "https://jqueryui.com", "author": {"name": "OpenJS Foundation and other contributors", "url": "https://github.com/jquery/jquery-ui/blob/1.13.3/AUTHORS.txt"}, "main": "ui/widget.js", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "joern.z<PERSON><PERSON><PERSON>@gmail.com", "url": "https://bassistance.de"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mike.sherov.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://www.tjvantoll.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.felixnagel.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/arschmitz"}], "repository": {"type": "git", "url": "git://github.com/jquery/jquery-ui.git"}, "bugs": {"url": "https://github.com/jquery/jquery-ui/issues"}, "license": "MIT", "scripts": {"build": "grunt build", "lint": "grunt lint", "test:server": "node tests/runner/server.js", "test:unit": "node tests/runner/command.js", "test": "grunt && npm run test:unit -- -h"}, "dependencies": {"jquery": ">=1.8.0 <4.0.0"}, "devDependencies": {"body-parser": "1.20.2", "browserstack-local": "1.5.5", "commitplease": "3.2.0", "diff": "5.2.0", "eslint-config-jquery": "3.0.2", "exit-hook": "4.0.0", "express": "4.19.2", "express-body-parser-error-handler": "1.0.7", "grunt": "1.6.1", "grunt-bowercopy": "1.2.5", "grunt-cli": "1.4.3", "grunt-compare-size": "0.4.2", "grunt-contrib-concat": "2.1.0", "grunt-contrib-csslint": "2.0.0", "grunt-contrib-requirejs": "1.0.0", "grunt-contrib-uglify": "5.2.2", "grunt-eslint": "24.0.1", "grunt-git-authors": "3.2.0", "grunt-html": "16.0.0", "load-grunt-tasks": "5.1.0", "rimraf": "4.4.1", "selenium-webdriver": "4.18.1", "testswarm": "1.1.2", "yargs": "17.7.2"}, "keywords": []}