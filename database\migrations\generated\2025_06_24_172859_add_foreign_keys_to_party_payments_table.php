<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('party_payments', function (Blueprint $table) {
            $table->foreign(['created_by'])->references(['id'])->on('users')->onUpdate('no action')->onDelete('no action');
            $table->foreign(['party_id'])->references(['id'])->on('parties')->onUpdate('no action')->onDelete('no action');
            $table->foreign(['payment_type_id'])->references(['id'])->on('payment_types')->onUpdate('no action')->onDelete('no action');
            $table->foreign(['updated_by'])->references(['id'])->on('users')->onUpdate('no action')->onDelete('no action');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('party_payments', function (Blueprint $table) {
            $table->dropForeign('party_payments_created_by_foreign');
            $table->dropForeign('party_payments_party_id_foreign');
            $table->dropForeign('party_payments_payment_type_id_foreign');
            $table->dropForeign('party_payments_updated_by_foreign');
        });
    }
};
