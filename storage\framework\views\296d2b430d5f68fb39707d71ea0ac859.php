<?php $__env->startSection('title', __('message.sms_templates')); ?>

<?php $__env->startSection('css'); ?>
<link href="<?php echo e(asset('assets/plugins/datatable/css/dataTables.bootstrap5.min.css')); ?>" rel="stylesheet">
<?php $__env->stopSection(); ?>
        <?php $__env->startSection('content'); ?>
        <!--start page wrapper -->
        <div class="page-wrapper">
            <div class="page-content">
                    <?php if (isset($component)) { $__componentOriginal269900abaed345884ce342681cdc99f6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal269900abaed345884ce342681cdc99f6 = $attributes; } ?>
<?php $component = App\View\Components\Breadcrumb::resolve(['langArray' => [
                                            'message.sms',
                                            'message.sms_templates',
                                        ]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('breadcrumb'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\Breadcrumb::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal269900abaed345884ce342681cdc99f6)): ?>
<?php $attributes = $__attributesOriginal269900abaed345884ce342681cdc99f6; ?>
<?php unset($__attributesOriginal269900abaed345884ce342681cdc99f6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal269900abaed345884ce342681cdc99f6)): ?>
<?php $component = $__componentOriginal269900abaed345884ce342681cdc99f6; ?>
<?php unset($__componentOriginal269900abaed345884ce342681cdc99f6); ?>
<?php endif; ?>

                    <div class="card">

                    <div class="card-header px-4 py-3 d-flex justify-content-between">
                        <!-- Other content on the left side -->
                        <div>
                            <h5 class="mb-0 text-uppercase"><?php echo e(__('message.sms_templates')); ?></h5>
                        </div>
                        
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('sms.template.create')): ?>
                        <!-- Button pushed to the right side -->
                        <?php if (isset($component)) { $__componentOriginal08be1f47856809f4c6eda68811b93273 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal08be1f47856809f4c6eda68811b93273 = $attributes; } ?>
<?php $component = App\View\Components\AnchorTag::resolve(['href' => ''.e(route('sms.template.create')).'','text' => ''.e(__('message.create_sms_template')).'','class' => 'btn btn-primary px-5'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('anchor-tag'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\AnchorTag::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal08be1f47856809f4c6eda68811b93273)): ?>
<?php $attributes = $__attributesOriginal08be1f47856809f4c6eda68811b93273; ?>
<?php unset($__attributesOriginal08be1f47856809f4c6eda68811b93273); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal08be1f47856809f4c6eda68811b93273)): ?>
<?php $component = $__componentOriginal08be1f47856809f4c6eda68811b93273; ?>
<?php unset($__componentOriginal08be1f47856809f4c6eda68811b93273); ?>
<?php endif; ?>
                        <?php endif; ?>
                    </div>
                    <div class="card-body">
                        <form class="row g-3 needs-validation" id="datatableForm" action="<?php echo e(route('sms.template.delete')); ?>" enctype="multipart/form-data">
                            
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('POST'); ?>
                            <div class="table-responsive">
                                <table class="table table-striped table-bordered border w-100" id="datatable">
                                    <thead>
                                        <tr>
                                            <th><?php echo e(__('app.name')); ?></th>
                                            <th><?php echo e(__('message.content')); ?></th>
                                            <th><?php echo e(__('app.created_at')); ?></th>
                                            <th><?php echo e(__('app.action')); ?></th>
                                        </tr>
                                    </thead>
                                </table>
                            </div>
                        </form>
                    </div>
                </div>
                    </div>
                </div>
                <!--end row-->
            </div>
        </div>
        <?php $__env->stopSection(); ?>
<?php $__env->startSection('js'); ?>
<script src="<?php echo e(versionedAsset('assets/plugins/datatable/js/jquery.dataTables.min.js')); ?>"></script>
<script src="<?php echo e(versionedAsset('assets/plugins/datatable/js/dataTables.bootstrap5.min.js')); ?>"></script>
<script src="<?php echo e(versionedAsset('custom/js/common/common.js')); ?>"></script>
<script src="<?php echo e(versionedAsset('custom/js/sms-template/sms-template-list.js')); ?>"></script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\www\delta\resources\views/sms-template/list.blade.php ENDPATH**/ ?>