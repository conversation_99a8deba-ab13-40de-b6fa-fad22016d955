{"__meta": {"id": "Xb165c233b2f8fdda54f1fc4de0e4db85", "datetime": "2025-06-27 15:53:41", "utime": **********.048773, "method": "GET", "uri": "/app/getimage/684aec0a5b455.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 4, "messages": [{"message": "[15:53:40] LOG.warning: str_contains(): Passing null to parameter #1 ($haystack) of type string is deprecated in D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 268", "message_html": null, "is_string": false, "label": "warning", "time": 1751036020.946933, "xdebug_link": null, "collector": "log"}, {"message": "[15:53:40] LOG.warning: str_contains(): Passing null to parameter #1 ($haystack) of type string is deprecated in D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 268", "message_html": null, "is_string": false, "label": "warning", "time": 1751036020.947218, "xdebug_link": null, "collector": "log"}, {"message": "[15:53:40] LOG.warning: str_contains(): Passing null to parameter #1 ($haystack) of type string is deprecated in D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 268", "message_html": null, "is_string": false, "label": "warning", "time": 1751036020.947502, "xdebug_link": null, "collector": "log"}, {"message": "[15:53:40] LOG.warning: str_contains(): Passing null to parameter #1 ($haystack) of type string is deprecated in D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 268", "message_html": null, "is_string": false, "label": "warning", "time": 1751036020.947636, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1751036020.558024, "end": **********.048813, "duration": 0.49078917503356934, "duration_str": "491ms", "measures": [{"label": "Booting", "start": 1751036020.558024, "relative_start": 0, "end": 1751036020.904392, "relative_end": 1751036020.904392, "duration": 0.34636807441711426, "duration_str": "346ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1751036020.904418, "relative_start": 0.*****************, "end": **********.048816, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "144ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "25MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET app/getimage/{image_name?}", "middleware": "web", "uses": "Closure($image_name = null) {#348\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#341 …}\n  file: \"D:\\www\\delta\\routes\\web.php\"\n  line: \"111 to 117\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Froutes%2Fweb.php&line=111\" onclick=\"\">routes/web.php:111-117</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "iSnE31HdLWjb9pxwIm1DCJlCI99aPSw6mNotXKpu", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/app/getimage/684aec0a5b455.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"telescope": "<a href=\"http://127.0.0.1:8000/_debugbar/telescope/9f417c00-a58a-4028-8fb3-************\" target=\"_blank\">View in Telescope</a>", "path_info": "/app/getimage/684aec0a5b455.png", "status_code": "<pre class=sf-dump id=sf-dump-1125668415 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1125668415\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "image/png", "request_query": "<pre class=sf-dump id=sf-dump-616256621 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-616256621\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-168760432 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-168760432\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1594832556 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">en-GB,en;q=0.9,en-US;q=0.8,fr;q=0.7,ar;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2047 characters\">ph_phc_3ESMmY9SgqEAGBB6sMGK5ayYHkeUuknH2vP6FmWH9RA_posthog=%7B%22distinct_id%22%3A%220196b41d-0793-7e06-86ce-017691734ea0%22%2C%22%24sesid%22%3A%5B1746780823698%2C%220196b41d-078e-7e17-a700-24eed1211756%22%2C1746778523534%5D%7D; aureuserp_session=eyJpdiI6IlIvOXVBdXl4UFJOWHhsSjA2YWNNeGc9PSIsInZhbHVlIjoiVXFQdlhTd3ZFMXZyV1JwNjczTEJUczB4N3YxZG5uWXNpamRHWXZyb0xIdDdiaXFoL2xOalBmNmtqMVdKZWllamlOWnNxREZvNjV5VWVxOXVGY3Q5MVNqK1ArTVBteDZIY01KTXdoc2xEaDEwY2draUl3Qi9SbHJUUnVOOWNsY1ciLCJtYWMiOiI1OWU4NmE1NjczOTQyZjZjZDM4YTYyMzZhMmZmODg4NGJlMWQ1ZjY3MjRkNzM5ZmRkMTllNDFjMTE1ODYxN2QyIiwidGFnIjoiIn0%3D; language_data=eyJpdiI6IkFLeWYyUVZhc3dlMURKNE1kWHYxUmc9PSIsInZhbHVlIjoiSUtsV1UvZ1FmVFNjRTgvMVZpM3E2YkRTR3FGNXl4akNCQVhRVkU2SlFqTnJoYUlHTml2T0hQb21RMzdhcm0rdm1OZjExWjFPRmhRQ00zTjNuZEZWeU5HNUNxZEsxQUdVbG1yVk1CM2hMY1BiYkZXNjQ1czFIUVgrTXhPd2VvZFloMWZHMlh0d3VtaDVMVDRHWU16Qm9qZkJ6V3JuOWtCK3lZbEJud3ZLNytKaEw2dGxUcnd4d0psQTdER1k5bWMzIiwibWFjIjoiOWU3MGMxNTA5MGE5YjJlZjQ4NzNjZDc1ZTliNWM1NTM1MTQ4NzcwN2QxZTNlN2E3YTZmNGFjMTI3YWZhOThkYiIsInRhZyI6IiJ9; theme_mode=eyJpdiI6Ii9XM3FTcWRDTHBmaVg5M0k2cFJiMEE9PSIsInZhbHVlIjoiNlZBU0xFRmZDaEdXVE5JRVMyMFhQcG0waG1PUWdFSDdWb1pCMWx3R0JVank5WXhQMytscW4vTW4yMkZSWlhWNjVISlc3NjNtcGNMckVYK1JXb3FOU3c9PSIsIm1hYyI6ImU2MmRhOThiNGQyNDA3N2UwMGViMjRhMzRhOThkYmJkZDdjMmNjYjhjZDk2MjIxMWZlZjVlNjA4Y2ZkYTkzNGIiLCJ0YWciOiIifQ%3D%3D; XSRF-TOKEN=eyJpdiI6IklwLzMzWmU1N2lGdmZNRlR4bVQzQ1E9PSIsInZhbHVlIjoiMjlpK1dWMjh6SFhKTmZLbzJZMkFXQ0dNZzE3c0hlVlhFOWRHeDVlZ2VjRG9EVGpBTUJTNlhtRHJBZTVUZXptYnVkZ3Z6bm40MlB2WG5FeWxpZ0Q4OU4zZFJWK3p1L2lSeVNzOURaM2wyZnhyV3N0dUo3eWhxbGtoQWd5VzkzVEwiLCJtYWMiOiJmZGU0YWVkYzU3ZWE0NTg3OGUyM2EzYTExZmQ2M2RkZmE5NWE5OGQ1NGJmOTAxYjcwOTVlYjdlM2E3N2Q4ZjgzIiwidGFnIjoiIn0%3D; delta_session=eyJpdiI6ImRxeUdDK3Q5Sm5EUTZJbFZFUS84b0E9PSIsInZhbHVlIjoiODBoWEdLVnpDWUVRRSsrRC9DMUhKbHJncFRUTXY3WW90WW9qeERBWEpEMDZPVWZMb2FMdUxyYWZxV1pHTHIrNFJzbnZGem42RzNDM3MrQjJ1VXFmSjdCa3ZNaEFDclJ4RDVsTkpzdyt0RGZ3cDNFL09WdGdKY1hQeGR4MkpMaWwiLCJtYWMiOiIwMGYyMDA2ZTllOTYyYmU0Njc5OGFjNWU2YzBmMjM4Mzk0MGQxZTY1MmYzN2MyZmY3OGJmNTUyMmYyMzEwNTNjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1594832556\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1701907187 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ph_phc_3ESMmY9SgqEAGBB6sMGK5ayYHkeUuknH2vP6FmWH9RA_posthog</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>aureuserp_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>language_data</span>\" => \"<span class=sf-dump-str title=\"94 characters\">{&quot;language_code&quot;:null,&quot;language_flag&quot;:&quot;flag-icon-us&quot;,&quot;direction&quot;:&quot;ltr&quot;,&quot;emoji&quot;:&quot;flag-icon-us&quot;}</span>\"\n  \"<span class=sf-dump-key>theme_mode</span>\" => \"<span class=sf-dump-str title=\"11 characters\">light-theme</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iSnE31HdLWjb9pxwIm1DCJlCI99aPSw6mNotXKpu</span>\"\n  \"<span class=sf-dump-key>delta_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TnVV7kfA4A6TOw8sFVFdOPCSbxEqNnJE3VHiSuhC</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1701907187\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1714528925 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 14:53:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>last-modified</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 12 Jun 2025 15:02:33 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">image/png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">5442</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-ranges</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">bytes</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Im83bmdEVW94Q2NGVmhhdnBaVE1QSVE9PSIsInZhbHVlIjoiUjFOUDJrMU9LQlhtZG1XZklDamQ3dHJtOEMraXFOSVh5V0ZKdGtWSGk3YUxJMGs5M1M0N3RPT043NE9hSjQwQmMyYytxaWhWWlo5QmJlNDJWQVQzanJwN2VsVG9aOWVRdGZnWmJ6R2grejFCYVNGb3hpOWV6TUZ3emhMaE9Wdy8iLCJtYWMiOiI4MDE0NWRiODY3ZWYyZDE3MTIzODAzYTM5N2ZhYWIwNWM5ZjcwODFmNWM0Mzk1ZjdlMTI2M2ViYzZhNTkzNGFlIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 16:53:41 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">delta_session=eyJpdiI6IlZKYk5LM3lVaUtkQ3FBRXptQm5DU2c9PSIsInZhbHVlIjoiV3lXQzlJRGgxc3BUc0dLUi90UGhoTkd6ZnE0NVZicFpRV1UzT2ZUdmZyYkNNaDRLTUZtN3JPUnJ3RS9HcCtmelV4UlF6LzhmekVKYzZHUDJadUJrcUNQTWFaL0FJQ3A4UHFKYVF3eDZmUUxWeUlrWnY0VFdISW9BNWZxS2VjdEIiLCJtYWMiOiI4YmJkMTY3ZjY5NjJiNTRjZDBiNzQwMDhkOGRlNzBhZDgwZDNjNTU5ZDQ4YmIwMDQzZGJlYzQ3NTZlMGM3NTZjIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 16:53:41 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Im83bmdEVW94Q2NGVmhhdnBaVE1QSVE9PSIsInZhbHVlIjoiUjFOUDJrMU9LQlhtZG1XZklDamQ3dHJtOEMraXFOSVh5V0ZKdGtWSGk3YUxJMGs5M1M0N3RPT043NE9hSjQwQmMyYytxaWhWWlo5QmJlNDJWQVQzanJwN2VsVG9aOWVRdGZnWmJ6R2grejFCYVNGb3hpOWV6TUZ3emhMaE9Wdy8iLCJtYWMiOiI4MDE0NWRiODY3ZWYyZDE3MTIzODAzYTM5N2ZhYWIwNWM5ZjcwODFmNWM0Mzk1ZjdlMTI2M2ViYzZhNTkzNGFlIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 16:53:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">delta_session=eyJpdiI6IlZKYk5LM3lVaUtkQ3FBRXptQm5DU2c9PSIsInZhbHVlIjoiV3lXQzlJRGgxc3BUc0dLUi90UGhoTkd6ZnE0NVZicFpRV1UzT2ZUdmZyYkNNaDRLTUZtN3JPUnJ3RS9HcCtmelV4UlF6LzhmekVKYzZHUDJadUJrcUNQTWFaL0FJQ3A4UHFKYVF3eDZmUUxWeUlrWnY0VFdISW9BNWZxS2VjdEIiLCJtYWMiOiI4YmJkMTY3ZjY5NjJiNTRjZDBiNzQwMDhkOGRlNzBhZDgwZDNjNTU5ZDQ4YmIwMDQzZGJlYzQ3NTZlMGM3NTZjIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 16:53:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1714528925\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1930484812 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iSnE31HdLWjb9pxwIm1DCJlCI99aPSw6mNotXKpu</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"52 characters\">http://127.0.0.1:8000/app/getimage/684aec0a5b455.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1930484812\", {\"maxDepth\":0})</script>\n"}}