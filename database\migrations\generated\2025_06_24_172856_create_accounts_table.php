<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('accounts', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('group_id')->index('accounts_group_id_foreign');
            $table->string('number')->nullable();
            $table->string('name');
            $table->string('description')->nullable();
            $table->decimal('debit_amt', 20, 4)->default(0);
            $table->decimal('credit_amt', 20, 4)->default(0);
            $table->string('unique_code')->nullable();
            $table->unsignedBigInteger('created_by')->nullable()->index('accounts_created_by_foreign');
            $table->unsignedBigInteger('updated_by')->nullable()->index('accounts_updated_by_foreign');
            $table->timestamps();
            $table->boolean('is_deletable')->default(true);
            $table->unsignedBigInteger('payment_type_bank_id')->nullable()->index('accounts_payment_type_bank_id_foreign');
            $table->unsignedBigInteger('expense_category_id')->nullable()->index('accounts_expense_category_id_foreign');
            $table->unsignedBigInteger('party_id')->nullable()->index('accounts_party_id_foreign');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('accounts');
    }
};
