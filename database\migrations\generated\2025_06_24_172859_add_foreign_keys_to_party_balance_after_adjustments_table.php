<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('party_balance_after_adjustments', function (Blueprint $table) {
            $table->foreign(['party_payment_id'], 'pba_party_payment_id_foreign')
                  ->references(['id'])
                  ->on('party_payments')
                  ->onUpdate('no action')
                  ->onDelete('cascade');
                  
            $table->foreign(['payment_transaction_id'], 'pba_payment_transaction_id_foreign')
                  ->references(['id'])
                  ->on('payment_transactions')
                  ->onUpdate('no action')
                  ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('party_balance_after_adjustments', function (Blueprint $table) {
            $table->dropForeign('pba_party_payment_id_foreign');
            $table->dropForeign('pba_payment_transaction_id_foreign');
        });
    }
};
