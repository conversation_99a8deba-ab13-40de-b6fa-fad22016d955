<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('party_transactions', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->date('transaction_date');
            $table->unsignedBigInteger('party_id')->index('party_transactions_party_id_foreign');
            $table->decimal('to_pay', 20, 4)->default(0);
            $table->decimal('to_receive', 20, 4)->default(0);
            $table->string('transaction_type');
            $table->unsignedBigInteger('transaction_id');
            $table->unsignedBigInteger('created_by')->nullable()->index('party_transactions_created_by_foreign');
            $table->unsignedBigInteger('updated_by')->nullable()->index('party_transactions_updated_by_foreign');
            $table->timestamps();

            $table->index(['transaction_type', 'transaction_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('party_transactions');
    }
};
