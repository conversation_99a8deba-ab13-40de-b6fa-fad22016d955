<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('account_groups', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('parent_id')->default(0);
            $table->string('number')->nullable();
            $table->string('name');
            $table->string('description')->nullable();
            $table->decimal('balance', 20, 4)->default(0);
            $table->string('unique_code')->nullable();
            $table->unsignedBigInteger('created_by')->nullable()->index('account_groups_created_by_foreign');
            $table->unsignedBigInteger('updated_by')->nullable()->index('account_groups_updated_by_foreign');
            $table->timestamps();
            $table->boolean('is_deletable')->default(true);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('account_groups');
    }
};
