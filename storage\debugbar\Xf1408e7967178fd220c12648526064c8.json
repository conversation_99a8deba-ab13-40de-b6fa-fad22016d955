{"__meta": {"id": "Xf1408e7967178fd220c12648526064c8", "datetime": "2025-06-27 17:54:33", "utime": **********.979477, "method": "GET", "uri": "/manufacturing/order/create", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.380113, "end": **********.979491, "duration": 0.5993781089782715, "duration_str": "599ms", "measures": [{"label": "Booting", "start": **********.380113, "relative_start": 0, "end": **********.623728, "relative_end": **********.623728, "duration": 0.24361515045166016, "duration_str": "244ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.623749, "relative_start": 0.2436361312866211, "end": **********.979493, "relative_end": 1.9073486328125e-06, "duration": 0.3557438850402832, "duration_str": "356ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 28443568, "peak_usage_str": "27MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 99, "templates": [{"name": "1x manufacturing_orders.create", "param_count": null, "params": [], "start": **********.774502, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/manufacturing_orders/create.blade.phpmanufacturing_orders.create", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fmanufacturing_orders%2Fcreate.blade.php&line=1", "ajax": false, "filename": "create.blade.php", "line": "?"}, "render_count": 1, "name_original": "manufacturing_orders.create"}, {"name": "1x components.breadcrumb", "param_count": null, "params": [], "start": **********.777279, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/components/breadcrumb.blade.phpcomponents.breadcrumb", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fcomponents%2Fbreadcrumb.blade.php&line=1", "ajax": false, "filename": "breadcrumb.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.breadcrumb"}, {"name": "44x components.label", "param_count": null, "params": [], "start": **********.779695, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/components/label.blade.phpcomponents.label", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fcomponents%2Flabel.blade.php&line=1", "ajax": false, "filename": "label.blade.php", "line": "?"}, "render_count": 44, "name_original": "components.label"}, {"name": "1x manufacturing_orders._store_basic_info", "param_count": null, "params": [], "start": **********.780607, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/manufacturing_orders/_store_basic_info.blade.phpmanufacturing_orders._store_basic_info", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fmanufacturing_orders%2F_store_basic_info.blade.php&line=1", "ajax": false, "filename": "_store_basic_info.blade.php", "line": "?"}, "render_count": 1, "name_original": "manufacturing_orders._store_basic_info"}, {"name": "33x components.input", "param_count": null, "params": [], "start": **********.782559, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/components/input.blade.phpcomponents.input", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fcomponents%2Finput.blade.php&line=1", "ajax": false, "filename": "input.blade.php", "line": "?"}, "render_count": 33, "name_original": "components.input"}, {"name": "1x manufacturing_orders._store_dimensions", "param_count": null, "params": [], "start": **********.790515, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/manufacturing_orders/_store_dimensions.blade.phpmanufacturing_orders._store_dimensions", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fmanufacturing_orders%2F_store_dimensions.blade.php&line=1", "ajax": false, "filename": "_store_dimensions.blade.php", "line": "?"}, "render_count": 1, "name_original": "manufacturing_orders._store_dimensions"}, {"name": "1x manufacturing_orders._store_pricing", "param_count": null, "params": [], "start": **********.796815, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/manufacturing_orders/_store_pricing.blade.phpmanufacturing_orders._store_pricing", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fmanufacturing_orders%2F_store_pricing.blade.php&line=1", "ajax": false, "filename": "_store_pricing.blade.php", "line": "?"}, "render_count": 1, "name_original": "manufacturing_orders._store_pricing"}, {"name": "2x components.textarea", "param_count": null, "params": [], "start": **********.816742, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/components/textarea.blade.phpcomponents.textarea", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fcomponents%2Ftextarea.blade.php&line=1", "ajax": false, "filename": "textarea.blade.php", "line": "?"}, "render_count": 2, "name_original": "components.textarea"}, {"name": "2x components.anchor-tag", "param_count": null, "params": [], "start": **********.817769, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/components/anchor-tag.blade.phpcomponents.anchor-tag", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fcomponents%2Fanchor-tag.blade.php&line=1", "ajax": false, "filename": "anchor-tag.blade.php", "line": "?"}, "render_count": 2, "name_original": "components.anchor-tag"}, {"name": "1x manufacturing_orders._rideaux_basic_info", "param_count": null, "params": [], "start": **********.818543, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/manufacturing_orders/_rideaux_basic_info.blade.phpmanufacturing_orders._rideaux_basic_info", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fmanufacturing_orders%2F_rideaux_basic_info.blade.php&line=1", "ajax": false, "filename": "_rideaux_basic_info.blade.php", "line": "?"}, "render_count": 1, "name_original": "manufacturing_orders._rideaux_basic_info"}, {"name": "1x manufacturing_orders._rideaux_dimensions", "param_count": null, "params": [], "start": **********.829605, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/manufacturing_orders/_rideaux_dimensions.blade.phpmanufacturing_orders._rideaux_dimensions", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fmanufacturing_orders%2F_rideaux_dimensions.blade.php&line=1", "ajax": false, "filename": "_rideaux_dimensions.blade.php", "line": "?"}, "render_count": 1, "name_original": "manufacturing_orders._rideaux_dimensions"}, {"name": "1x manufacturing_orders._rideaux_details", "param_count": null, "params": [], "start": **********.83966, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/manufacturing_orders/_rideaux_details.blade.phpmanufacturing_orders._rideaux_details", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fmanufacturing_orders%2F_rideaux_details.blade.php&line=1", "ajax": false, "filename": "_rideaux_details.blade.php", "line": "?"}, "render_count": 1, "name_original": "manufacturing_orders._rideaux_details"}, {"name": "1x manufacturing_orders._rideaux_pricing", "param_count": null, "params": [], "start": **********.84876, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/manufacturing_orders/_rideaux_pricing.blade.phpmanufacturing_orders._rideaux_pricing", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fmanufacturing_orders%2F_rideaux_pricing.blade.php&line=1", "ajax": false, "filename": "_rideaux_pricing.blade.php", "line": "?"}, "render_count": 1, "name_original": "manufacturing_orders._rideaux_pricing"}, {"name": "1x layouts.app", "param_count": null, "params": [], "start": **********.864699, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/layouts/app.blade.phplayouts.app", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.app"}, {"name": "1x layouts.head", "param_count": null, "params": [], "start": **********.865213, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/layouts/head.blade.phplayouts.head", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Flayouts%2Fhead.blade.php&line=1", "ajax": false, "filename": "head.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.head"}, {"name": "1x layouts.page-loader", "param_count": null, "params": [], "start": **********.866005, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/layouts/page-loader.blade.phplayouts.page-loader", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Flayouts%2Fpage-loader.blade.php&line=1", "ajax": false, "filename": "page-loader.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.page-loader"}, {"name": "1x layouts.navigation", "param_count": null, "params": [], "start": **********.866477, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/layouts/navigation.blade.phplayouts.navigation", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Flayouts%2Fnavigation.blade.php&line=1", "ajax": false, "filename": "navigation.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.navigation"}, {"name": "1x layouts.header", "param_count": null, "params": [], "start": **********.947496, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/layouts/header.blade.phplayouts.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Flayouts%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.header"}, {"name": "1x components.header-shortcut-menu", "param_count": null, "params": [], "start": **********.950963, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/components/header-shortcut-menu.blade.phpcomponents.header-shortcut-menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fcomponents%2Fheader-shortcut-menu.blade.php&line=1", "ajax": false, "filename": "header-shortcut-menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.header-shortcut-menu"}, {"name": "1x components.flag-toggle", "param_count": null, "params": [], "start": **********.964436, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/components/flag-toggle.blade.phpcomponents.flag-toggle", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fcomponents%2Fflag-toggle.blade.php&line=1", "ajax": false, "filename": "flag-toggle.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.flag-toggle"}, {"name": "1x layouts.footer", "param_count": null, "params": [], "start": **********.96821, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/layouts/footer.blade.phplayouts.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Flayouts%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.footer"}, {"name": "1x layouts.script", "param_count": null, "params": [], "start": **********.968728, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/layouts/script.blade.phplayouts.script", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Flayouts%2Fscript.blade.php&line=1", "ajax": false, "filename": "script.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.script"}]}, "route": {"uri": "GET manufacturing/order/create", "middleware": "web, auth, can:manufacturing.order.create", "as": "manufacturing.order.create", "controller": "App\\Http\\Controllers\\ManufacturingOrderController@create", "namespace": null, "prefix": "/manufacturing", "where": [], "file": "<a href=\"phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FHttp%2FControllers%2FManufacturingOrderController.php&line=30\" onclick=\"\">app/Http/Controllers/ManufacturingOrderController.php:30-42</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.03178, "accumulated_duration_str": "31.78ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 17, "namespace": "middleware", "name": "auth", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}], "start": **********.676466, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:168", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "deltapos", "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.683534, "duration": 0.02552, "duration_str": "25.52ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "deltapos", "start_percent": 0, "width_percent": 80.302}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 1 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["1", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\www\\delta\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\www\\delta\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 193}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\www\\delta\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 244}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\www\\delta\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 136}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.7400022, "duration": 0.00204, "duration_str": "2.04ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:305", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:305", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=305", "ajax": false, "filename": "HasPermissions.php", "line": "305"}, "connection": "deltapos", "start_percent": 80.302, "width_percent": 6.419}, {"sql": "select * from `manufacturing_orders` order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ManufacturingOrderController.php", "file": "D:\\www\\delta\\app\\Http\\Controllers\\ManufacturingOrderController.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.761911, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "ManufacturingOrderController.php:33", "source": "app/Http/Controllers/ManufacturingOrderController.php:33", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FHttp%2FControllers%2FManufacturingOrderController.php&line=33", "ajax": false, "filename": "ManufacturingOrderController.php", "line": "33"}, "connection": "deltapos", "start_percent": 86.721, "width_percent": 2.926}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\www\\delta\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, {"index": 23, "namespace": null, "name": "app/Providers/AuthServiceProvider.php", "file": "D:\\www\\delta\\app\\Providers\\AuthServiceProvider.php", "line": 42}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 438}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 406}], "start": **********.910972, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:188", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:188", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=188", "ajax": false, "filename": "HasRoles.php", "line": "188"}, "connection": "deltapos", "start_percent": 89.648, "width_percent": 3.839}, {"sql": "select `id`, `name`, `code`, `emoji` from `languages` where `status` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/View/Components/FlagToggle.php", "file": "D:\\www\\delta\\app\\View\\Components\\FlagToggle.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 961}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.9622219, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "FlagToggle.php:43", "source": "app/View/Components/FlagToggle.php:43", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FView%2FComponents%2FFlagToggle.php&line=43", "ajax": false, "filename": "FlagToggle.php", "line": "43"}, "connection": "deltapos", "start_percent": 93.486, "width_percent": 3.367}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "view", "name": "layouts.header", "file": "D:\\www\\delta\\resources\\views/layouts/header.blade.php", "line": 81}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.965949, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "layouts.header:81", "source": "view::layouts.header:81", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Flayouts%2Fheader.blade.php&line=81", "ajax": false, "filename": "header.blade.php", "line": "81"}, "connection": "deltapos", "start_percent": 96.853, "width_percent": 3.147}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 167, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\Language": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 172, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 114, "messages": [{"message": "[\n  ability => manufacturing.order.create,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1428536006 data-indent-pad=\"  \"><span class=sf-dump-note>manufacturing.order.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"26 characters\">manufacturing.order.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1428536006\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.76058, "xdebug_link": null}, {"message": "[ability => customer.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-964888826 data-indent-pad=\"  \"><span class=sf-dump-note>customer.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">customer.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-964888826\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.868728, "xdebug_link": null}, {"message": "[ability => customer.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-852252903 data-indent-pad=\"  \"><span class=sf-dump-note>customer.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">customer.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-852252903\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.869185, "xdebug_link": null}, {"message": "[ability => supplier.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2067916870 data-indent-pad=\"  \"><span class=sf-dump-note>supplier.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">supplier.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2067916870\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.870636, "xdebug_link": null}, {"message": "[ability => sale.invoice.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2124019708 data-indent-pad=\"  \"><span class=sf-dump-note>sale.invoice.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">sale.invoice.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2124019708\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.873362, "xdebug_link": null}, {"message": "[ability => sale.invoice.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-311822470 data-indent-pad=\"  \"><span class=sf-dump-note>sale.invoice.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">sale.invoice.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-311822470\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.874925, "xdebug_link": null}, {"message": "[ability => sale.invoice.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-969360097 data-indent-pad=\"  \"><span class=sf-dump-note>sale.invoice.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">sale.invoice.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-969360097\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.876255, "xdebug_link": null}, {"message": "[ability => sale.quotation.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1775606874 data-indent-pad=\"  \"><span class=sf-dump-note>sale.quotation.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">sale.quotation.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1775606874\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.877841, "xdebug_link": null}, {"message": "[ability => sale.invoice.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-100860009 data-indent-pad=\"  \"><span class=sf-dump-note>sale.invoice.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">sale.invoice.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-100860009\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.879146, "xdebug_link": null}, {"message": "[ability => sale.order.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-89816678 data-indent-pad=\"  \"><span class=sf-dump-note>sale.order.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">sale.order.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-89816678\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.880639, "xdebug_link": null}, {"message": "[ability => sale.return.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1058761836 data-indent-pad=\"  \"><span class=sf-dump-note>sale.return.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">sale.return.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1058761836\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.881922, "xdebug_link": null}, {"message": "[ability => purchase.bill.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-752400890 data-indent-pad=\"  \"><span class=sf-dump-note>purchase.bill.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">purchase.bill.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-752400890\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.883002, "xdebug_link": null}, {"message": "[ability => purchase.bill.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-160986964 data-indent-pad=\"  \"><span class=sf-dump-note>purchase.bill.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">purchase.bill.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-160986964\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.884261, "xdebug_link": null}, {"message": "[ability => purchase.bill.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-341813393 data-indent-pad=\"  \"><span class=sf-dump-note>purchase.bill.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">purchase.bill.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-341813393\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.885328, "xdebug_link": null}, {"message": "[ability => purchase.order.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1206984022 data-indent-pad=\"  \"><span class=sf-dump-note>purchase.order.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">purchase.order.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1206984022\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.886405, "xdebug_link": null}, {"message": "[ability => purchase.return.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-791970487 data-indent-pad=\"  \"><span class=sf-dump-note>purchase.return.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">purchase.return.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-791970487\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.887662, "xdebug_link": null}, {"message": "[ability => item.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1078771947 data-indent-pad=\"  \"><span class=sf-dump-note>item.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">item.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1078771947\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.888697, "xdebug_link": null}, {"message": "[ability => item.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1042433252 data-indent-pad=\"  \"><span class=sf-dump-note>item.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">item.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1042433252\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.890763, "xdebug_link": null}, {"message": "[ability => item.category.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-768468149 data-indent-pad=\"  \"><span class=sf-dump-note>item.category.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">item.category.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-768468149\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.891748, "xdebug_link": null}, {"message": "[ability => item.brand.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1171352862 data-indent-pad=\"  \"><span class=sf-dump-note>item.brand.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">item.brand.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1171352862\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.893434, "xdebug_link": null}, {"message": "[ability => expense.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-674152358 data-indent-pad=\"  \"><span class=sf-dump-note>expense.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">expense.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-674152358\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.894151, "xdebug_link": null}, {"message": "[ability => expense.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-595738783 data-indent-pad=\"  \"><span class=sf-dump-note>expense.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">expense.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-595738783\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.895217, "xdebug_link": null}, {"message": "[ability => expense.category.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-837558885 data-indent-pad=\"  \"><span class=sf-dump-note>expense.category.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">expense.category.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-837558885\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.89636, "xdebug_link": null}, {"message": "[\n  ability => expense.subcategory.view,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-791497310 data-indent-pad=\"  \"><span class=sf-dump-note>expense.subcategory.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">expense.subcategory.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-791497310\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.898047, "xdebug_link": null}, {"message": "[ability => transaction.cash.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-665937501 data-indent-pad=\"  \"><span class=sf-dump-note>transaction.cash.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">transaction.cash.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-665937501\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.899325, "xdebug_link": null}, {"message": "[ability => transaction.cash.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1929740090 data-indent-pad=\"  \"><span class=sf-dump-note>transaction.cash.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">transaction.cash.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1929740090\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.900583, "xdebug_link": null}, {"message": "[\n  ability => transaction.cheque.view,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>transaction.cheque.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">transaction.cheque.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.901866, "xdebug_link": null}, {"message": "[ability => transaction.bank.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>transaction.bank.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">transaction.bank.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.903225, "xdebug_link": null}, {"message": "[ability => warehouse.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>warehouse.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">warehouse.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.904026, "xdebug_link": null}, {"message": "[ability => warehouse.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-402078887 data-indent-pad=\"  \"><span class=sf-dump-note>warehouse.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">warehouse.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-402078887\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.905061, "xdebug_link": null}, {"message": "[ability => stock_transfer.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-535969537 data-indent-pad=\"  \"><span class=sf-dump-note>stock_transfer.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">stock_transfer.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-535969537\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.905896, "xdebug_link": null}, {"message": "[ability => import.item, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-56722876 data-indent-pad=\"  \"><span class=sf-dump-note>import.item</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">import.item</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-56722876\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.906918, "xdebug_link": null}, {"message": "[ability => import.item, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2083800469 data-indent-pad=\"  \"><span class=sf-dump-note>import.item</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">import.item</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2083800469\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.907901, "xdebug_link": null}, {"message": "[ability => import.party, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-365172606 data-indent-pad=\"  \"><span class=sf-dump-note>import.party</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">import.party</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-365172606\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.908914, "xdebug_link": null}, {"message": "[ability => generate.barcode, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-note>generate.barcode</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">generate.barcode</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.909914, "xdebug_link": null}, {"message": "[ability => account.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>account.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">account.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.912918, "xdebug_link": null}, {"message": "[ability => account.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>account.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">account.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.913655, "xdebug_link": null}, {"message": "[ability => account.group.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>account.group.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">account.group.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.91414, "xdebug_link": null}, {"message": "[ability => profile.edit, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>profile.edit</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">profile.edit</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.914438, "xdebug_link": null}, {"message": "[ability => profile.edit, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-966213894 data-indent-pad=\"  \"><span class=sf-dump-note>profile.edit</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">profile.edit</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-966213894\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.914916, "xdebug_link": null}, {"message": "[ability => user.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1366900437 data-indent-pad=\"  \"><span class=sf-dump-note>user.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">user.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1366900437\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.915164, "xdebug_link": null}, {"message": "[ability => role.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1461599486 data-indent-pad=\"  \"><span class=sf-dump-note>role.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">role.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1461599486\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.915423, "xdebug_link": null}, {"message": "[ability => permission.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1733534261 data-indent-pad=\"  \"><span class=sf-dump-note>permission.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">permission.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1733534261\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.915874, "xdebug_link": null}, {"message": "[ability => sms.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-869967803 data-indent-pad=\"  \"><span class=sf-dump-note>sms.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">sms.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-869967803\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.916207, "xdebug_link": null}, {"message": "[ability => sms.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1198116154 data-indent-pad=\"  \"><span class=sf-dump-note>sms.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">sms.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1198116154\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.916731, "xdebug_link": null}, {"message": "[ability => sms.template.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2063940532 data-indent-pad=\"  \"><span class=sf-dump-note>sms.template.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">sms.template.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2063940532\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.917089, "xdebug_link": null}, {"message": "[ability => email.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1434553390 data-indent-pad=\"  \"><span class=sf-dump-note>email.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">email.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1434553390\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.917435, "xdebug_link": null}, {"message": "[ability => email.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-23997448 data-indent-pad=\"  \"><span class=sf-dump-note>email.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">email.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-23997448\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.917764, "xdebug_link": null}, {"message": "[ability => email.template.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1266243648 data-indent-pad=\"  \"><span class=sf-dump-note>email.template.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">email.template.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1266243648\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.91814, "xdebug_link": null}, {"message": "[ability => report.*, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-140243957 data-indent-pad=\"  \"><span class=sf-dump-note>report.*</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">report.*</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-140243957\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.918549, "xdebug_link": null}, {"message": "[ability => report.profit_and_loss, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1744029605 data-indent-pad=\"  \"><span class=sf-dump-note>report.profit_and_loss</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">report.profit_and_loss</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1744029605\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.918958, "xdebug_link": null}, {"message": "[\n  ability => report.item.transaction.batch,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1756862423 data-indent-pad=\"  \"><span class=sf-dump-note>report.item.transaction.batch</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"29 characters\">report.item.transaction.batch</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1756862423\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.919428, "xdebug_link": null}, {"message": "[\n  ability => report.item.transaction.batch,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-94607679 data-indent-pad=\"  \"><span class=sf-dump-note>report.item.transaction.batch</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"29 characters\">report.item.transaction.batch</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-94607679\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.919864, "xdebug_link": null}, {"message": "[\n  ability => report.item.transaction.serial,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2092113504 data-indent-pad=\"  \"><span class=sf-dump-note>report.item.transaction.serial</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"30 characters\">report.item.transaction.serial</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2092113504\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.920334, "xdebug_link": null}, {"message": "[\n  ability => report.item.transaction.general,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-750145887 data-indent-pad=\"  \"><span class=sf-dump-note>report.item.transaction.general</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"31 characters\">report.item.transaction.general</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-750145887\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.920811, "xdebug_link": null}, {"message": "[ability => report.purchase, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1314796427 data-indent-pad=\"  \"><span class=sf-dump-note>report.purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">report.purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1314796427\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.921279, "xdebug_link": null}, {"message": "[ability => report.purchase, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1240840592 data-indent-pad=\"  \"><span class=sf-dump-note>report.purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">report.purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1240840592\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.92174, "xdebug_link": null}, {"message": "[ability => report.purchase.item, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-690138415 data-indent-pad=\"  \"><span class=sf-dump-note>report.purchase.item</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">report.purchase.item</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-690138415\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.92224, "xdebug_link": null}, {"message": "[\n  ability => report.purchase.payment,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-676907904 data-indent-pad=\"  \"><span class=sf-dump-note>report.purchase.payment</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">report.purchase.payment</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-676907904\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.922744, "xdebug_link": null}, {"message": "[ability => report.sale, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-748361832 data-indent-pad=\"  \"><span class=sf-dump-note>report.sale</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">report.sale</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-748361832\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.923244, "xdebug_link": null}, {"message": "[ability => report.sale, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-881729808 data-indent-pad=\"  \"><span class=sf-dump-note>report.sale</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">report.sale</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-881729808\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.923693, "xdebug_link": null}, {"message": "[ability => report.sale.item, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-781651137 data-indent-pad=\"  \"><span class=sf-dump-note>report.sale.item</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">report.sale.item</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-781651137\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.924164, "xdebug_link": null}, {"message": "[ability => report.sale.payment, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1944067777 data-indent-pad=\"  \"><span class=sf-dump-note>report.sale.payment</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">report.sale.payment</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1944067777\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.924645, "xdebug_link": null}, {"message": "[\n  ability => report.customer.due.payment,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-929216691 data-indent-pad=\"  \"><span class=sf-dump-note>report.customer.due.payment</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"27 characters\">report.customer.due.payment</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-929216691\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.925612, "xdebug_link": null}, {"message": "[\n  ability => report.customer.due.payment,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1701465072 data-indent-pad=\"  \"><span class=sf-dump-note>report.customer.due.payment</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"27 characters\">report.customer.due.payment</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1701465072\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.927, "xdebug_link": null}, {"message": "[\n  ability => report.supplier.due.payment,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1899058265 data-indent-pad=\"  \"><span class=sf-dump-note>report.supplier.due.payment</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"27 characters\">report.supplier.due.payment</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1899058265\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.928052, "xdebug_link": null}, {"message": "[ability => report.expense, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1337280246 data-indent-pad=\"  \"><span class=sf-dump-note>report.expense</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">report.expense</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1337280246\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.928547, "xdebug_link": null}, {"message": "[ability => report.expense, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-750066475 data-indent-pad=\"  \"><span class=sf-dump-note>report.expense</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">report.expense</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-750066475\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.929029, "xdebug_link": null}, {"message": "[ability => report.expense.item, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1459877272 data-indent-pad=\"  \"><span class=sf-dump-note>report.expense.item</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">report.expense.item</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1459877272\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.929529, "xdebug_link": null}, {"message": "[ability => report.expense.payment, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-982363171 data-indent-pad=\"  \"><span class=sf-dump-note>report.expense.payment</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">report.expense.payment</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-982363171\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.930045, "xdebug_link": null}, {"message": "[\n  ability => report.transaction.cashflow,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1425838162 data-indent-pad=\"  \"><span class=sf-dump-note>report.transaction.cashflow</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"27 characters\">report.transaction.cashflow</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1425838162\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.930474, "xdebug_link": null}, {"message": "[\n  ability => report.transaction.cashflow,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>report.transaction.cashflow</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"27 characters\">report.transaction.cashflow</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.930868, "xdebug_link": null}, {"message": "[\n  ability => report.transaction.bank-statement,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>report.transaction.bank-statement</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"33 characters\">report.transaction.bank-statement</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.931287, "xdebug_link": null}, {"message": "[ability => report.gst*, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>report.gst*</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">report.gst*</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.931692, "xdebug_link": null}, {"message": "[ability => report.gstr1, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1826370176 data-indent-pad=\"  \"><span class=sf-dump-note>report.gstr1</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">report.gstr1</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1826370176\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.932068, "xdebug_link": null}, {"message": "[ability => report.gstr2, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1839380777 data-indent-pad=\"  \"><span class=sf-dump-note>report.gstr2</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">report.gstr2</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1839380777\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.932459, "xdebug_link": null}, {"message": "[ability => report.stock_transfer, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-853102161 data-indent-pad=\"  \"><span class=sf-dump-note>report.stock_transfer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">report.stock_transfer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-853102161\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.932985, "xdebug_link": null}, {"message": "[ability => report.stock_transfer, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1893388196 data-indent-pad=\"  \"><span class=sf-dump-note>report.stock_transfer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">report.stock_transfer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1893388196\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.933498, "xdebug_link": null}, {"message": "[\n  ability => report.stock_transfer.item,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-569800900 data-indent-pad=\"  \"><span class=sf-dump-note>report.stock_transfer.item</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"26 characters\">report.stock_transfer.item</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-569800900\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.934037, "xdebug_link": null}, {"message": "[ability => report.stock_report.*, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-621500466 data-indent-pad=\"  \"><span class=sf-dump-note>report.stock_report.*</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">report.stock_report.*</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-621500466\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.934432, "xdebug_link": null}, {"message": "[\n  ability => report.stock_report.item.batch,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1218903479 data-indent-pad=\"  \"><span class=sf-dump-note>report.stock_report.item.batch</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"30 characters\">report.stock_report.item.batch</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1218903479\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.935418, "xdebug_link": null}, {"message": "[\n  ability => report.stock_report.item.serial,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1031741912 data-indent-pad=\"  \"><span class=sf-dump-note>report.stock_report.item.serial</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"31 characters\">report.stock_report.item.serial</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1031741912\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.936529, "xdebug_link": null}, {"message": "[\n  ability => report.stock_report.item.general,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-870484492 data-indent-pad=\"  \"><span class=sf-dump-note>report.stock_report.item.general</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"32 characters\">report.stock_report.item.general</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-870484492\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.937566, "xdebug_link": null}, {"message": "[ability => report.expired.item, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-428046710 data-indent-pad=\"  \"><span class=sf-dump-note>report.expired.item</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">report.expired.item</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-428046710\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.938054, "xdebug_link": null}, {"message": "[ability => report.reorder.item, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>report.reorder.item</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">report.reorder.item</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.938544, "xdebug_link": null}, {"message": "[ability => tax.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1422211567 data-indent-pad=\"  \"><span class=sf-dump-note>tax.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">tax.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1422211567\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.938746, "xdebug_link": null}, {"message": "[ability => app.settings.edit, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>app.settings.edit</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">app.settings.edit</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.938991, "xdebug_link": null}, {"message": "[ability => company.edit, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>company.edit</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">company.edit</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.939347, "xdebug_link": null}, {"message": "[ability => tax.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-97273352 data-indent-pad=\"  \"><span class=sf-dump-note>tax.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">tax.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-97273352\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.939543, "xdebug_link": null}, {"message": "[ability => payment.type.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1389860204 data-indent-pad=\"  \"><span class=sf-dump-note>payment.type.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">payment.type.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1389860204\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.940151, "xdebug_link": null}, {"message": "[ability => currency.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-697121943 data-indent-pad=\"  \"><span class=sf-dump-note>currency.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">currency.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-697121943\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.941337, "xdebug_link": null}, {"message": "[ability => unit.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1426457933 data-indent-pad=\"  \"><span class=sf-dump-note>unit.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">unit.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1426457933\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.942364, "xdebug_link": null}, {"message": "[ability => language.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-706356027 data-indent-pad=\"  \"><span class=sf-dump-note>language.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">language.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-706356027\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.942988, "xdebug_link": null}, {"message": "[\n  ability => manufacturing.order.view,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-514505892 data-indent-pad=\"  \"><span class=sf-dump-note>manufacturing.order.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manufacturing.order.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-514505892\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.944415, "xdebug_link": null}, {"message": "[\n  ability => manufacturing.order.view,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1539762616 data-indent-pad=\"  \"><span class=sf-dump-note>manufacturing.order.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manufacturing.order.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1539762616\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.945676, "xdebug_link": null}, {"message": "[\n  ability => manufacturing.calculation.view,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2014388054 data-indent-pad=\"  \"><span class=sf-dump-note>manufacturing.calculation.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"30 characters\">manufacturing.calculation.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2014388054\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.947012, "xdebug_link": null}, {"message": "[ability => purchase.bill.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1262620700 data-indent-pad=\"  \"><span class=sf-dump-note>purchase.bill.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">purchase.bill.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1262620700\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.948616, "xdebug_link": null}, {"message": "[ability => sale.invoice.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-827249545 data-indent-pad=\"  \"><span class=sf-dump-note>sale.invoice.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">sale.invoice.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-827249545\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.949491, "xdebug_link": null}, {"message": "[ability => sale.invoice.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1594875188 data-indent-pad=\"  \"><span class=sf-dump-note>sale.invoice.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">sale.invoice.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1594875188\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.950352, "xdebug_link": null}, {"message": "[ability => sale.invoice.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1399405541 data-indent-pad=\"  \"><span class=sf-dump-note>sale.invoice.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">sale.invoice.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1399405541\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.952135, "xdebug_link": null}, {"message": "[ability => purchase.bill.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1101949160 data-indent-pad=\"  \"><span class=sf-dump-note>purchase.bill.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">purchase.bill.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1101949160\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.95304, "xdebug_link": null}, {"message": "[ability => expense.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1094958190 data-indent-pad=\"  \"><span class=sf-dump-note>expense.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">expense.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1094958190\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.953682, "xdebug_link": null}, {"message": "[ability => sale.invoice.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-411911832 data-indent-pad=\"  \"><span class=sf-dump-note>sale.invoice.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">sale.invoice.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-411911832\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.954691, "xdebug_link": null}, {"message": "[ability => purchase.bill.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1003149396 data-indent-pad=\"  \"><span class=sf-dump-note>purchase.bill.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">purchase.bill.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1003149396\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.955593, "xdebug_link": null}, {"message": "[ability => purchase.order.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-316574310 data-indent-pad=\"  \"><span class=sf-dump-note>purchase.order.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">purchase.order.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-316574310\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.956393, "xdebug_link": null}, {"message": "[ability => sale.return.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-132153338 data-indent-pad=\"  \"><span class=sf-dump-note>sale.return.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">sale.return.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-132153338\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.957469, "xdebug_link": null}, {"message": "[ability => purchase.return.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-739210622 data-indent-pad=\"  \"><span class=sf-dump-note>purchase.return.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">purchase.return.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-739210622\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.958308, "xdebug_link": null}, {"message": "[ability => stock_transfer.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2077099637 data-indent-pad=\"  \"><span class=sf-dump-note>stock_transfer.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">stock_transfer.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2077099637\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.958908, "xdebug_link": null}, {"message": "[ability => item.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-638395372 data-indent-pad=\"  \"><span class=sf-dump-note>item.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">item.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-638395372\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.959528, "xdebug_link": null}, {"message": "[ability => customer.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-499113993 data-indent-pad=\"  \"><span class=sf-dump-note>customer.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">customer.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-499113993\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.959672, "xdebug_link": null}, {"message": "[ability => supplier.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1050101009 data-indent-pad=\"  \"><span class=sf-dump-note>supplier.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">supplier.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1050101009\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.960383, "xdebug_link": null}, {"message": "[ability => sale.quotation.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1456746286 data-indent-pad=\"  \"><span class=sf-dump-note>sale.quotation.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">sale.quotation.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1456746286\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.961499, "xdebug_link": null}, {"message": "[\n  ability => general.permission.to.apply.discount.to.purchase,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-851852523 data-indent-pad=\"  \"><span class=sf-dump-note>general.permission.to.apply.discount.to.purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"48 characters\">general.permission.to.apply.discount.to.purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-851852523\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.97082, "xdebug_link": null}, {"message": "[\n  ability => general.permission.to.apply.discount.to.sale,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-974445802 data-indent-pad=\"  \"><span class=sf-dump-note>general.permission.to.apply.discount.to.sale</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"44 characters\">general.permission.to.apply.discount.to.sale</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-974445802\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.972755, "xdebug_link": null}]}, "session": {"_token": "DbgoOTCuumHy04EHLVHFGwOg3Jxe3DqOD4DE9hb1", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/manufacturing/order/create\"\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"telescope": "<a href=\"http://127.0.0.1:8000/_debugbar/telescope/9f41a73b-b5f9-48cd-bc0f-211a185f3c1f\" target=\"_blank\">View in Telescope</a>", "path_info": "/manufacturing/order/create", "status_code": "<pre class=sf-dump id=sf-dump-1803955999 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1803955999\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-857395037 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-857395037\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-922974239 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-922974239\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1306944040 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/manufacturing/order</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">en-GB,en;q=0.9,en-US;q=0.8,fr;q=0.7,ar;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1685 characters\">ph_phc_3ESMmY9SgqEAGBB6sMGK5ayYHkeUuknH2vP6FmWH9RA_posthog=%7B%22distinct_id%22%3A%220196b41d-0793-7e06-86ce-017691734ea0%22%2C%22%24sesid%22%3A%5B1746780823698%2C%220196b41d-078e-7e17-a700-24eed1211756%22%2C1746778523534%5D%7D; theme_mode=eyJpdiI6Ii9XM3FTcWRDTHBmaVg5M0k2cFJiMEE9PSIsInZhbHVlIjoiNlZBU0xFRmZDaEdXVE5JRVMyMFhQcG0waG1PUWdFSDdWb1pCMWx3R0JVank5WXhQMytscW4vTW4yMkZSWlhWNjVISlc3NjNtcGNMckVYK1JXb3FOU3c9PSIsIm1hYyI6ImU2MmRhOThiNGQyNDA3N2UwMGViMjRhMzRhOThkYmJkZDdjMmNjYjhjZDk2MjIxMWZlZjVlNjA4Y2ZkYTkzNGIiLCJ0YWciOiIifQ%3D%3D; language_data=eyJpdiI6InpaMG5kK3BkZ2c3NFZqKy9nVVFRd2c9PSIsInZhbHVlIjoicmw0UjBkdFZZclA3UlljTXVDSE9HWGN6S05VUHdZamlZb2hjajFlcm9uVU1LNFpsZlhQRW5wTTlPNlUrRUJ5VUMzOU5lSnJHY2JBZUIvNC95Ujl5bnFxTWpmVEt5aXY2dXJVemZlbG1LenFtdmZEKzE5RXhsc1hJUE1zVk0wNlJMTEhvTUJXLzVnWHN6d1FnVzh1WnVGUWFRN2xvUyt4aC9pRzVaWTlBMFN4NWhQN1ZSL3dGY0ljZzZOZ1UxbUN6IiwibWFjIjoiYWE2MTZlMmNhMGM3ZjMwNTRhOTZkZjc4ZTZlNzI3NjUzMjY3NDMyNmJlYmViMmY4NjFiZDExMTBjNjMzNWFjNiIsInRhZyI6IiJ9; XSRF-TOKEN=eyJpdiI6InI0NldOU0p4bENJNkR4a0FxZUs3dWc9PSIsInZhbHVlIjoiaXB3MUo2cTFjeTNUZ0Fob1Q3bkpLZ1U3bklBSmdobHRYTVl4ZEl0R0dCTHBRN2ZRVVNLNG5aaWJTSTNoK2cyenJEZ3E2RVBMOWZBVlFCaHVqNUN2OWwxOTVHOEtHeTF5dXdwMXFiVitBQy9RSko0SzBhYlFzMTR0VjVoeEZmWWwiLCJtYWMiOiI2NDBjMzEyMDg0Zjg2MjY1MTMzY2Q0ZTA5ZjBmMzEwMmVjNGE3MTliMGNhYWZhOTJkNWVmZmFkZjBmM2EzNjcxIiwidGFnIjoiIn0%3D; delta_session=eyJpdiI6IkpYRUZlK0xuYXhVVG5Vb3g1cnI5Y0E9PSIsInZhbHVlIjoiQmlGcVFaNW5FeTNzeWtJMHNYSmd2OGdkL1c4YTZVT2RQZWg2YTBmdlZGRExCeDc5RllyZFh2ckFIOWlSSzhKbFRXWEExOGY4Wm9wK0VqaTlyUUtvSVpDbWFxZmhSZmNVZWRnclV2OHZmYmJ0TVdKVEtRZ1RjZ25GeTQ3bS9OS1IiLCJtYWMiOiI5NWE5NGUyMzFkNjk4YjM4NjQ1NGViMjQzM2MzYmQ0MTU3M2RjNGIzZjliY2EwYWU5MzlkYmI3NTNlZWYzMDhjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1306944040\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1856816614 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ph_phc_3ESMmY9SgqEAGBB6sMGK5ayYHkeUuknH2vP6FmWH9RA_posthog</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>theme_mode</span>\" => \"<span class=sf-dump-str title=\"11 characters\">light-theme</span>\"\n  \"<span class=sf-dump-key>language_data</span>\" => \"<span class=sf-dump-str title=\"94 characters\">{&quot;language_code&quot;:&quot;fr&quot;,&quot;language_flag&quot;:&quot;flag-icon-fr&quot;,&quot;direction&quot;:&quot;ltr&quot;,&quot;emoji&quot;:&quot;flag-icon-fr&quot;}</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DbgoOTCuumHy04EHLVHFGwOg3Jxe3DqOD4DE9hb1</span>\"\n  \"<span class=sf-dump-key>delta_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BzceRqun0Ae5OezZzHQypR8L1x1bHb6dsUfkY5Ah</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1856816614\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1364013229 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 16:54:33 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ijh0M2pVYnBzOUdIMG9tRjI2Z0JWQnc9PSIsInZhbHVlIjoiK1JXKzA1S2VSQjVnbDlkOGhRbyt4Y1lhTThjalF2SlpFSzdzMFc1U2NuK0xkaEZ3V2kybnRDWXBvNEU2SHhQMVp6blVCOFIvQUlGbGVKejN4VndIdk9IQUZTaFR6eHpaNlpMOHlRdEkxTlp6VVA4dXlVK0NJZnZsb2JDNHdhSWYiLCJtYWMiOiIxNTMxZWY3ODE5OTRhNDliNmI3YjU3MjIxODUxZTM5NWViZTFlYTgwMDNkOGUzNWNjYWE4Zjg1ZmJlOTkzNDU0IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 18:54:33 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">delta_session=eyJpdiI6IlRIcklwRFhVMEJqZExJZytxcjVXMEE9PSIsInZhbHVlIjoiQ2w5cmtXYnVJWXZkZE1ISnJQeFB3ZHViSGIxK1ZJcVZHR241aXJmZkozOE9kUDJORG85OS8zNi9zZHdSR2ZJWHowUEhsRXRBb09YcTRkY0ZVUUVCOUhJY1RrS0lDL05DZ1JKMmRZU3BVNkZZQ1Z6R0N0S3o2a1g5eFNtUEpUc2oiLCJtYWMiOiIzNzEwZjYxYjQwZjczZmYwZGFlODgxNTdiZjU2YTA5NDgzNTZjYmI0OWZlZmM2NjU5YzBkZWJjODBmM2ViYzdhIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 18:54:33 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ijh0M2pVYnBzOUdIMG9tRjI2Z0JWQnc9PSIsInZhbHVlIjoiK1JXKzA1S2VSQjVnbDlkOGhRbyt4Y1lhTThjalF2SlpFSzdzMFc1U2NuK0xkaEZ3V2kybnRDWXBvNEU2SHhQMVp6blVCOFIvQUlGbGVKejN4VndIdk9IQUZTaFR6eHpaNlpMOHlRdEkxTlp6VVA4dXlVK0NJZnZsb2JDNHdhSWYiLCJtYWMiOiIxNTMxZWY3ODE5OTRhNDliNmI3YjU3MjIxODUxZTM5NWViZTFlYTgwMDNkOGUzNWNjYWE4Zjg1ZmJlOTkzNDU0IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 18:54:33 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">delta_session=eyJpdiI6IlRIcklwRFhVMEJqZExJZytxcjVXMEE9PSIsInZhbHVlIjoiQ2w5cmtXYnVJWXZkZE1ISnJQeFB3ZHViSGIxK1ZJcVZHR241aXJmZkozOE9kUDJORG85OS8zNi9zZHdSR2ZJWHowUEhsRXRBb09YcTRkY0ZVUUVCOUhJY1RrS0lDL05DZ1JKMmRZU3BVNkZZQ1Z6R0N0S3o2a1g5eFNtUEpUc2oiLCJtYWMiOiIzNzEwZjYxYjQwZjczZmYwZGFlODgxNTdiZjU2YTA5NDgzNTZjYmI0OWZlZmM2NjU5YzBkZWJjODBmM2ViYzdhIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 18:54:33 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1364013229\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-259967566 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DbgoOTCuumHy04EHLVHFGwOg3Jxe3DqOD4DE9hb1</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"48 characters\">http://127.0.0.1:8000/manufacturing/order/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-259967566\", {\"maxDepth\":0})</script>\n"}}