{"__meta": {"id": "X3703101c01dfcfb522e10e90fb1a5ac5", "datetime": "2025-06-27 18:26:00", "utime": **********.880955, "method": "GET", "uri": "/app/getimage/684aec0a5b455.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.569792, "end": **********.880972, "duration": 0.31117987632751465, "duration_str": "311ms", "measures": [{"label": "Booting", "start": **********.569792, "relative_start": 0, "end": **********.819531, "relative_end": **********.819531, "duration": 0.2497389316558838, "duration_str": "250ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.819543, "relative_start": 0.*****************, "end": **********.880974, "relative_end": 2.1457672119140625e-06, "duration": 0.061431169509887695, "duration_str": "61.43ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "25MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET app/getimage/{image_name?}", "middleware": "web", "uses": "Closure($image_name = null) {#377\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#366 …}\n  file: \"D:\\www\\delta\\routes\\web.php\"\n  line: \"121 to 127\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Froutes%2Fweb.php&line=121\" onclick=\"\">routes/web.php:121-127</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "DbgoOTCuumHy04EHLVHFGwOg3Jxe3DqOD4DE9hb1", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/app/getimage/684aec0a5b455.png\"\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1"}, "request": {"telescope": "<a href=\"http://127.0.0.1:8000/_debugbar/telescope/9f41b27a-e945-4490-aa71-a8dfce4924bc\" target=\"_blank\">View in Telescope</a>", "path_info": "/app/getimage/684aec0a5b455.png", "status_code": "<pre class=sf-dump id=sf-dump-861828059 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-861828059\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "image/png", "request_query": "<pre class=sf-dump id=sf-dump-1208820888 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1208820888\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-238042805 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-238042805\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1764407857 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/manufacturing/order</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">en-GB,en;q=0.9,en-US;q=0.8,fr;q=0.7,ar;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1685 characters\">ph_phc_3ESMmY9SgqEAGBB6sMGK5ayYHkeUuknH2vP6FmWH9RA_posthog=%7B%22distinct_id%22%3A%220196b41d-0793-7e06-86ce-017691734ea0%22%2C%22%24sesid%22%3A%5B1746780823698%2C%220196b41d-078e-7e17-a700-24eed1211756%22%2C1746778523534%5D%7D; theme_mode=eyJpdiI6Ii9XM3FTcWRDTHBmaVg5M0k2cFJiMEE9PSIsInZhbHVlIjoiNlZBU0xFRmZDaEdXVE5JRVMyMFhQcG0waG1PUWdFSDdWb1pCMWx3R0JVank5WXhQMytscW4vTW4yMkZSWlhWNjVISlc3NjNtcGNMckVYK1JXb3FOU3c9PSIsIm1hYyI6ImU2MmRhOThiNGQyNDA3N2UwMGViMjRhMzRhOThkYmJkZDdjMmNjYjhjZDk2MjIxMWZlZjVlNjA4Y2ZkYTkzNGIiLCJ0YWciOiIifQ%3D%3D; language_data=eyJpdiI6InpaMG5kK3BkZ2c3NFZqKy9nVVFRd2c9PSIsInZhbHVlIjoicmw0UjBkdFZZclA3UlljTXVDSE9HWGN6S05VUHdZamlZb2hjajFlcm9uVU1LNFpsZlhQRW5wTTlPNlUrRUJ5VUMzOU5lSnJHY2JBZUIvNC95Ujl5bnFxTWpmVEt5aXY2dXJVemZlbG1LenFtdmZEKzE5RXhsc1hJUE1zVk0wNlJMTEhvTUJXLzVnWHN6d1FnVzh1WnVGUWFRN2xvUyt4aC9pRzVaWTlBMFN4NWhQN1ZSL3dGY0ljZzZOZ1UxbUN6IiwibWFjIjoiYWE2MTZlMmNhMGM3ZjMwNTRhOTZkZjc4ZTZlNzI3NjUzMjY3NDMyNmJlYmViMmY4NjFiZDExMTBjNjMzNWFjNiIsInRhZyI6IiJ9; XSRF-TOKEN=eyJpdiI6ImxMaXphUkt4WHRIMGF4dzNkVk1TYVE9PSIsInZhbHVlIjoidHJvRnB4akJ4bXViK2FQdmMzK1RuOEhsanFsdm9DUnQ2OWEvY3RZQTJzdUt3RG9GLzNmbHhJU2tWaXkzc29CS0hzc3ZvbTkwbnRDSmVsMm15WGRjNzF3V3NHK3JBbTZwRmhTNXlBS0FCbmdHdHhncjQ1SXdqaE9LV2NWMklHMU0iLCJtYWMiOiI5YmU1ZjBlMjBhYmZjYmI5YjY4ZjJhNmI3MTNiZWVmNTQ1NmY1YjE0YjNkZjhkNWM5MGZlMDc0NGFiNWVmYWY2IiwidGFnIjoiIn0%3D; delta_session=eyJpdiI6InY1OGJybHNrVnM2UVNkbXR1UExnQ3c9PSIsInZhbHVlIjoiS1NCSWZVdWRSMzJHTURRM0Rhc0dTd0lOVlhsK1k2NU5NN0hvenNqMm1tYk1ZUy8ySXZ0Z2IxbXMzOXdyaTFVVExtWUg2YllCNU4rOGw5NFQvbkZPcVJTRk5PM3hvT0hOWWFnb3AwN3poVWFpSXpwakRLZW9xVzVuRWxDYnBvUGgiLCJtYWMiOiJhZjI3MmM4OGY4MTVkMDlkNzE1YTBmZjg2ODM4MWFiZmVmZGUxYTQzN2MxMjJmYjQxNjQ5MTNmYmRjZTAwMDJmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1764407857\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-930852298 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ph_phc_3ESMmY9SgqEAGBB6sMGK5ayYHkeUuknH2vP6FmWH9RA_posthog</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>theme_mode</span>\" => \"<span class=sf-dump-str title=\"11 characters\">light-theme</span>\"\n  \"<span class=sf-dump-key>language_data</span>\" => \"<span class=sf-dump-str title=\"94 characters\">{&quot;language_code&quot;:&quot;fr&quot;,&quot;language_flag&quot;:&quot;flag-icon-fr&quot;,&quot;direction&quot;:&quot;ltr&quot;,&quot;emoji&quot;:&quot;flag-icon-fr&quot;}</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DbgoOTCuumHy04EHLVHFGwOg3Jxe3DqOD4DE9hb1</span>\"\n  \"<span class=sf-dump-key>delta_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BzceRqun0Ae5OezZzHQypR8L1x1bHb6dsUfkY5Ah</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-930852298\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-546424627 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 17:26:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>last-modified</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 12 Jun 2025 15:02:33 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">image/png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">5442</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-ranges</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">bytes</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImNuZU5IYmZENlUvcWpueWErRVlBOVE9PSIsInZhbHVlIjoiUE9mb3BaWGxDYXV0Y1M0QkM5ZVMxVDg2SWhHTkh1NW1DLzNWY1gzN1NQRG01SFk2NHZPL2VKc2RLbDg3Z3NjSzM3SlF1Vmk5NXNvTkxnRWxEdEd4SjRFR2hpSVZPQk1nR0gwNmpEL3czbklobzlEUXd2V0pveiticTVUZFpuTEciLCJtYWMiOiJhYWE0OGY0NmM2ZjdkZmM5MWVjZDBhM2VmMTVmMTUwNmU1YjlhYjlhYmIwZTM0ODllNjA1YzNiYjlkMjkzNzcxIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 19:26:00 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">delta_session=eyJpdiI6IlcyTlFhc3dVaHhBTzBTdDc0WElZT1E9PSIsInZhbHVlIjoiSTQxbEV4N3F4Y0haRS9FS2syTWMvNGRUcnNVc0JlQy8wcVZoREJPSnVqV3ZDVk5hTTlJajJMZWl2S2NSblZLYXdvSGRBVk1YYUF6Mjl4VFRnRzNiVkxPK2tmRDY0bEk4RDBuS3FuSmYvMDRnVkU5RXh4WE1kUC9URTBRUWRqQ3ciLCJtYWMiOiI4MTIzOWUzMDBlYzQ1NTE5ODVlOTdjNmNjMjViMzdmNTZmNmEzZTRkM2RlODQyOGY1MDE4YTMyMTFmMTllYjE5IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 19:26:00 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImNuZU5IYmZENlUvcWpueWErRVlBOVE9PSIsInZhbHVlIjoiUE9mb3BaWGxDYXV0Y1M0QkM5ZVMxVDg2SWhHTkh1NW1DLzNWY1gzN1NQRG01SFk2NHZPL2VKc2RLbDg3Z3NjSzM3SlF1Vmk5NXNvTkxnRWxEdEd4SjRFR2hpSVZPQk1nR0gwNmpEL3czbklobzlEUXd2V0pveiticTVUZFpuTEciLCJtYWMiOiJhYWE0OGY0NmM2ZjdkZmM5MWVjZDBhM2VmMTVmMTUwNmU1YjlhYjlhYmIwZTM0ODllNjA1YzNiYjlkMjkzNzcxIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 19:26:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">delta_session=eyJpdiI6IlcyTlFhc3dVaHhBTzBTdDc0WElZT1E9PSIsInZhbHVlIjoiSTQxbEV4N3F4Y0haRS9FS2syTWMvNGRUcnNVc0JlQy8wcVZoREJPSnVqV3ZDVk5hTTlJajJMZWl2S2NSblZLYXdvSGRBVk1YYUF6Mjl4VFRnRzNiVkxPK2tmRDY0bEk4RDBuS3FuSmYvMDRnVkU5RXh4WE1kUC9URTBRUWRqQ3ciLCJtYWMiOiI4MTIzOWUzMDBlYzQ1NTE5ODVlOTdjNmNjMjViMzdmNTZmNmEzZTRkM2RlODQyOGY1MDE4YTMyMTFmMTllYjE5IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 19:26:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-546424627\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-927621670 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DbgoOTCuumHy04EHLVHFGwOg3Jxe3DqOD4DE9hb1</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"52 characters\">http://127.0.0.1:8000/app/getimage/684aec0a5b455.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-927621670\", {\"maxDepth\":0})</script>\n"}}