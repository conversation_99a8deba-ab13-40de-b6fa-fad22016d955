<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_payments', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->date('payment_date');
            $table->unsignedBigInteger('order_id')->index('order_payments_order_id_foreign');
            $table->unsignedBigInteger('payment_type_id')->index('order_payments_payment_type_id_foreign');
            $table->string('transaction_id')->nullable()->comment('If Online Payments');
            $table->decimal('amount', 10)->default(0);
            $table->text('note')->nullable();
            $table->string('status')->nullable();
            $table->unsignedBigInteger('created_by')->nullable()->index('order_payments_created_by_foreign');
            $table->unsignedBigInteger('updated_by')->nullable()->index('order_payments_updated_by_foreign');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_payments');
    }
};
