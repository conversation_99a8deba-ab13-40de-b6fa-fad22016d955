<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('manufacturing_calculations', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->enum('height_type', ['simple', 'double']);
            $table->enum('curtain_finish', ['Wave', 'PP', 'PF']);
            $table->enum('manipulation', ['Moteur', 'Manuelle'])->nullable();
            $table->decimal('height_deduction');
            $table->string('formula_description');
            $table->integer('agrafes_spacing')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            $table->unique(['height_type', 'curtain_finish', 'manipulation'], 'manuf_calc_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('manufacturing_calculations');
    }
};
