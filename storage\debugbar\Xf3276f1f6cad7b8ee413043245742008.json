{"__meta": {"id": "Xf3276f1f6cad7b8ee413043245742008", "datetime": "2025-06-27 15:53:39", "utime": 1751036019.513256, "method": "GET", "uri": "/login", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 5, "messages": [{"message": "[15:53:38] LOG.warning: str_contains(): Passing null to parameter #1 ($haystack) of type string is deprecated in D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 268", "message_html": null, "is_string": false, "label": "warning", "time": 1751036018.774646, "xdebug_link": null, "collector": "log"}, {"message": "[15:53:38] LOG.warning: str_contains(): Passing null to parameter #1 ($haystack) of type string is deprecated in D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 268", "message_html": null, "is_string": false, "label": "warning", "time": 1751036018.774778, "xdebug_link": null, "collector": "log"}, {"message": "[15:53:38] LOG.warning: str_contains(): Passing null to parameter #1 ($haystack) of type string is deprecated in D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 268", "message_html": null, "is_string": false, "label": "warning", "time": 1751036018.774859, "xdebug_link": null, "collector": "log"}, {"message": "[15:53:38] LOG.warning: str_contains(): Passing null to parameter #1 ($haystack) of type string is deprecated in D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 268", "message_html": null, "is_string": false, "label": "warning", "time": 1751036018.774919, "xdebug_link": null, "collector": "log"}, {"message": "[15:53:38] LOG.warning: str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in D:\\www\\delta\\storage\\framework\\views\\d3a4659e394615ecf5628ee1fecde399.php on line 2", "message_html": null, "is_string": false, "label": "warning", "time": 1751036018.975597, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1751036018.325904, "end": 1751036019.513363, "duration": 1.1874589920043945, "duration_str": "1.19s", "measures": [{"label": "Booting", "start": 1751036018.325904, "relative_start": 0, "end": 1751036018.730211, "relative_end": 1751036018.730211, "duration": 0.40430712699890137, "duration_str": "404ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1751036018.730233, "relative_start": 0.4043290615081787, "end": 1751036019.513367, "relative_end": 4.0531158447265625e-06, "duration": 0.7831339836120605, "duration_str": "783ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 25783640, "peak_usage_str": "25MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 18, "templates": [{"name": "auth.login", "param_count": null, "params": [], "start": 1751036018.905472, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}}, {"name": "layouts.session", "param_count": null, "params": [], "start": 1751036018.929222, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/layouts/session.blade.phplayouts.session", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Flayouts%2Fsession.blade.php&line=1", "ajax": false, "filename": "session.blade.php", "line": "?"}}, {"name": "components.label", "param_count": null, "params": [], "start": 1751036018.942113, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/components/label.blade.phpcomponents.label", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fcomponents%2Flabel.blade.php&line=1", "ajax": false, "filename": "label.blade.php", "line": "?"}}, {"name": "components.input", "param_count": null, "params": [], "start": 1751036018.944792, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/components/input.blade.phpcomponents.input", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fcomponents%2Finput.blade.php&line=1", "ajax": false, "filename": "input.blade.php", "line": "?"}}, {"name": "components.label", "param_count": null, "params": [], "start": 1751036018.945972, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/components/label.blade.phpcomponents.label", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fcomponents%2Flabel.blade.php&line=1", "ajax": false, "filename": "label.blade.php", "line": "?"}}, {"name": "components.input", "param_count": null, "params": [], "start": 1751036018.950462, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/components/input.blade.phpcomponents.input", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fcomponents%2Finput.blade.php&line=1", "ajax": false, "filename": "input.blade.php", "line": "?"}}, {"name": "components.radio-block", "param_count": null, "params": [], "start": 1751036018.952387, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/components/radio-block.blade.phpcomponents.radio-block", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fcomponents%2Fradio-block.blade.php&line=1", "ajax": false, "filename": "radio-block.blade.php", "line": "?"}}, {"name": "components.anchor-tag", "param_count": null, "params": [], "start": 1751036018.955161, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/components/anchor-tag.blade.phpcomponents.anchor-tag", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fcomponents%2Fanchor-tag.blade.php&line=1", "ajax": false, "filename": "anchor-tag.blade.php", "line": "?"}}, {"name": "components.button", "param_count": null, "params": [], "start": 1751036018.957051, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/components/button.blade.phpcomponents.button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fcomponents%2Fbutton.blade.php&line=1", "ajax": false, "filename": "button.blade.php", "line": "?"}}, {"name": "components.flag-toggle", "param_count": null, "params": [], "start": 1751036018.965519, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/components/flag-toggle.blade.phpcomponents.flag-toggle", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fcomponents%2Fflag-toggle.blade.php&line=1", "ajax": false, "filename": "flag-toggle.blade.php", "line": "?"}}, {"name": "components.anchor-tag", "param_count": null, "params": [], "start": 1751036018.967505, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/components/anchor-tag.blade.phpcomponents.anchor-tag", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fcomponents%2Fanchor-tag.blade.php&line=1", "ajax": false, "filename": "anchor-tag.blade.php", "line": "?"}}, {"name": "components.anchor-tag", "param_count": null, "params": [], "start": 1751036018.968881, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/components/anchor-tag.blade.phpcomponents.anchor-tag", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fcomponents%2Fanchor-tag.blade.php&line=1", "ajax": false, "filename": "anchor-tag.blade.php", "line": "?"}}, {"name": "components.anchor-tag", "param_count": null, "params": [], "start": 1751036018.97065, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/components/anchor-tag.blade.phpcomponents.anchor-tag", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fcomponents%2Fanchor-tag.blade.php&line=1", "ajax": false, "filename": "anchor-tag.blade.php", "line": "?"}}, {"name": "components.anchor-tag", "param_count": null, "params": [], "start": 1751036018.972348, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/components/anchor-tag.blade.phpcomponents.anchor-tag", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fcomponents%2Fanchor-tag.blade.php&line=1", "ajax": false, "filename": "anchor-tag.blade.php", "line": "?"}}, {"name": "auth.demo-login", "param_count": null, "params": [], "start": 1751036018.973627, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/auth/demo-login.blade.phpauth.demo-login", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fauth%2Fdemo-login.blade.php&line=1", "ajax": false, "filename": "demo-login.blade.php", "line": "?"}}, {"name": "layouts.guest", "param_count": null, "params": [], "start": 1751036018.97509, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/layouts/guest.blade.phplayouts.guest", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Flayouts%2Fguest.blade.php&line=1", "ajax": false, "filename": "guest.blade.php", "line": "?"}}, {"name": "layouts.head", "param_count": null, "params": [], "start": 1751036018.976485, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/layouts/head.blade.phplayouts.head", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Flayouts%2Fhead.blade.php&line=1", "ajax": false, "filename": "head.blade.php", "line": "?"}}, {"name": "layouts.script", "param_count": null, "params": [], "start": 1751036019.381211, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/layouts/script.blade.phplayouts.script", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Flayouts%2Fscript.blade.php&line=1", "ajax": false, "filename": "script.blade.php", "line": "?"}}]}, "route": {"uri": "GET login", "middleware": "web, installation<PERSON><PERSON><PERSON>, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@create", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=19\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:19-22</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.0074600000000000005, "accumulated_duration_str": "7.46ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/CacheService.php", "file": "D:\\www\\delta\\app\\Services\\CacheService.php", "line": 22}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 19, "namespace": null, "name": "app/Services/CacheService.php", "file": "D:\\www\\delta\\app\\Services\\CacheService.php", "line": 21}, {"index": 20, "namespace": null, "name": "app/View/Composers/AppSettingsComposer.php", "file": "D:\\www\\delta\\app\\View\\Composers\\AppSettingsComposer.php", "line": 24}], "start": 1751036018.870029, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "CacheService.php:22", "source": "app/Services/CacheService.php:22", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FServices%2FCacheService.php&line=22", "ajax": false, "filename": "CacheService.php", "line": "22"}, "connection": "deltapos", "start_percent": 0, "width_percent": 0}, {"sql": "select * from `app_settings` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Services/CacheService.php", "file": "D:\\www\\delta\\app\\Services\\CacheService.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/Services/CacheService.php", "file": "D:\\www\\delta\\app\\Services\\CacheService.php", "line": 21}, {"index": 24, "namespace": null, "name": "app/View/Composers/AppSettingsComposer.php", "file": "D:\\www\\delta\\app\\View\\Composers\\AppSettingsComposer.php", "line": 24}], "start": 1751036018.89112, "duration": 0.00578, "duration_str": "5.78ms", "memory": 0, "memory_str": null, "filename": "CacheService.php:22", "source": "app/Services/CacheService.php:22", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FServices%2FCacheService.php&line=22", "ajax": false, "filename": "CacheService.php", "line": "22"}, "connection": "deltapos", "start_percent": 0, "width_percent": 77.48}, {"sql": "select `id`, `name`, `code`, `emoji` from `languages` where `status` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/View/Components/FlagToggle.php", "file": "D:\\www\\delta\\app\\View\\Components\\FlagToggle.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 102}, {"index": 17, "namespace": "view", "name": "auth.login", "file": "D:\\www\\delta\\resources\\views/auth/login.blade.php", "line": 241}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1751036018.958894, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "FlagToggle.php:43", "source": "app/View/Components/FlagToggle.php:43", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FView%2FComponents%2FFlagToggle.php&line=43", "ajax": false, "filename": "FlagToggle.php", "line": "43"}, "connection": "deltapos", "start_percent": 77.48, "width_percent": 22.52}]}, "models": {"data": {"App\\Models\\Language": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "App\\Models\\AppSettings": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FModels%2FAppSettings.php&line=1", "ajax": false, "filename": "AppSettings.php", "line": "?"}}}, "count": 5, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "iSnE31HdLWjb9pxwIm1DCJlCI99aPSw6mNotXKpu", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"telescope": "<a href=\"http://127.0.0.1:8000/_debugbar/telescope/9f417bfe-4f81-4ef3-b772-5eb958966e1e\" target=\"_blank\">View in Telescope</a>", "path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-2017930165 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2017930165\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1632519032 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1632519032\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1826467546 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1826467546\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-437296016 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">en-GB,en;q=0.9,en-US;q=0.8,fr;q=0.7,ar;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2047 characters\">ph_phc_3ESMmY9SgqEAGBB6sMGK5ayYHkeUuknH2vP6FmWH9RA_posthog=%7B%22distinct_id%22%3A%220196b41d-0793-7e06-86ce-017691734ea0%22%2C%22%24sesid%22%3A%5B1746780823698%2C%220196b41d-078e-7e17-a700-24eed1211756%22%2C1746778523534%5D%7D; aureuserp_session=eyJpdiI6IlIvOXVBdXl4UFJOWHhsSjA2YWNNeGc9PSIsInZhbHVlIjoiVXFQdlhTd3ZFMXZyV1JwNjczTEJUczB4N3YxZG5uWXNpamRHWXZyb0xIdDdiaXFoL2xOalBmNmtqMVdKZWllamlOWnNxREZvNjV5VWVxOXVGY3Q5MVNqK1ArTVBteDZIY01KTXdoc2xEaDEwY2draUl3Qi9SbHJUUnVOOWNsY1ciLCJtYWMiOiI1OWU4NmE1NjczOTQyZjZjZDM4YTYyMzZhMmZmODg4NGJlMWQ1ZjY3MjRkNzM5ZmRkMTllNDFjMTE1ODYxN2QyIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Imw0WWYvQTBWQ3paVjc3R0dVbGFsNmc9PSIsInZhbHVlIjoiM2lYT0hVbU9PZ1p5OHNtcWJWZTM5MWxON3dobEpJbk12c3RTblBwblc5bTNZMTQydW5TVW9hRFNNbG9uSkVDcWhyTEM3YlE1Ti91d1lrazdjN25kQVBVM1QxcHlkamNZRzFZaXE3SU9NRXJTVEVFM1V1SXJwVUgzcGI1dFp1QVAiLCJtYWMiOiI5NmRlYTU0N2E3ZmIwMjIzZjdkMjgyYjVlYzEwYTVhOTM5NWY5MjZiZmI3YjBkZGE0YjEwMDVkMWFmYjU2MGVlIiwidGFnIjoiIn0%3D; delta_session=eyJpdiI6InZqbk9QMGpDZVBraFJPKzdEdytTZlE9PSIsInZhbHVlIjoickdHVHlNRHhaR0QxbVRHRThkc3VqUVJiRUo5bVJtOENVTUFUeXdPaFFud1orRjFtSWF5dGJwUVF6SFVCQi93aS9DMmpKVExxc3krcjVyLzYvQ1gzc1FrcXhzNlY5dlIyc0FRR3B1anczMXVqeGc5RGdmZlI2RVpZK0NxY0M2TnkiLCJtYWMiOiI1YjNmZTM5YzY0MDgzMzBhYzJhNTJlNTJkZTU4YWYzMmViOGYxYTViZjM3ZDU1Y2VjNmFjMjQ5ODgyODFiOWNhIiwidGFnIjoiIn0%3D; language_data=eyJpdiI6IkFLeWYyUVZhc3dlMURKNE1kWHYxUmc9PSIsInZhbHVlIjoiSUtsV1UvZ1FmVFNjRTgvMVZpM3E2YkRTR3FGNXl4akNCQVhRVkU2SlFqTnJoYUlHTml2T0hQb21RMzdhcm0rdm1OZjExWjFPRmhRQ00zTjNuZEZWeU5HNUNxZEsxQUdVbG1yVk1CM2hMY1BiYkZXNjQ1czFIUVgrTXhPd2VvZFloMWZHMlh0d3VtaDVMVDRHWU16Qm9qZkJ6V3JuOWtCK3lZbEJud3ZLNytKaEw2dGxUcnd4d0psQTdER1k5bWMzIiwibWFjIjoiOWU3MGMxNTA5MGE5YjJlZjQ4NzNjZDc1ZTliNWM1NTM1MTQ4NzcwN2QxZTNlN2E3YTZmNGFjMTI3YWZhOThkYiIsInRhZyI6IiJ9; theme_mode=eyJpdiI6Ii9XM3FTcWRDTHBmaVg5M0k2cFJiMEE9PSIsInZhbHVlIjoiNlZBU0xFRmZDaEdXVE5JRVMyMFhQcG0waG1PUWdFSDdWb1pCMWx3R0JVank5WXhQMytscW4vTW4yMkZSWlhWNjVISlc3NjNtcGNMckVYK1JXb3FOU3c9PSIsIm1hYyI6ImU2MmRhOThiNGQyNDA3N2UwMGViMjRhMzRhOThkYmJkZDdjMmNjYjhjZDk2MjIxMWZlZjVlNjA4Y2ZkYTkzNGIiLCJ0YWciOiIifQ%3D%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-437296016\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-763741584 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ph_phc_3ESMmY9SgqEAGBB6sMGK5ayYHkeUuknH2vP6FmWH9RA_posthog</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>aureuserp_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iSnE31HdLWjb9pxwIm1DCJlCI99aPSw6mNotXKpu</span>\"\n  \"<span class=sf-dump-key>delta_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TnVV7kfA4A6TOw8sFVFdOPCSbxEqNnJE3VHiSuhC</span>\"\n  \"<span class=sf-dump-key>language_data</span>\" => \"<span class=sf-dump-str title=\"94 characters\">{&quot;language_code&quot;:null,&quot;language_flag&quot;:&quot;flag-icon-us&quot;,&quot;direction&quot;:&quot;ltr&quot;,&quot;emoji&quot;:&quot;flag-icon-us&quot;}</span>\"\n  \"<span class=sf-dump-key>theme_mode</span>\" => \"<span class=sf-dump-str title=\"11 characters\">light-theme</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-763741584\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 14:53:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IklwLzMzWmU1N2lGdmZNRlR4bVQzQ1E9PSIsInZhbHVlIjoiMjlpK1dWMjh6SFhKTmZLbzJZMkFXQ0dNZzE3c0hlVlhFOWRHeDVlZ2VjRG9EVGpBTUJTNlhtRHJBZTVUZXptYnVkZ3Z6bm40MlB2WG5FeWxpZ0Q4OU4zZFJWK3p1L2lSeVNzOURaM2wyZnhyV3N0dUo3eWhxbGtoQWd5VzkzVEwiLCJtYWMiOiJmZGU0YWVkYzU3ZWE0NTg3OGUyM2EzYTExZmQ2M2RkZmE5NWE5OGQ1NGJmOTAxYjcwOTVlYjdlM2E3N2Q4ZjgzIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 16:53:39 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">delta_session=eyJpdiI6ImRxeUdDK3Q5Sm5EUTZJbFZFUS84b0E9PSIsInZhbHVlIjoiODBoWEdLVnpDWUVRRSsrRC9DMUhKbHJncFRUTXY3WW90WW9qeERBWEpEMDZPVWZMb2FMdUxyYWZxV1pHTHIrNFJzbnZGem42RzNDM3MrQjJ1VXFmSjdCa3ZNaEFDclJ4RDVsTkpzdyt0RGZ3cDNFL09WdGdKY1hQeGR4MkpMaWwiLCJtYWMiOiIwMGYyMDA2ZTllOTYyYmU0Njc5OGFjNWU2YzBmMjM4Mzk0MGQxZTY1MmYzN2MyZmY3OGJmNTUyMmYyMzEwNTNjIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 16:53:39 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IklwLzMzWmU1N2lGdmZNRlR4bVQzQ1E9PSIsInZhbHVlIjoiMjlpK1dWMjh6SFhKTmZLbzJZMkFXQ0dNZzE3c0hlVlhFOWRHeDVlZ2VjRG9EVGpBTUJTNlhtRHJBZTVUZXptYnVkZ3Z6bm40MlB2WG5FeWxpZ0Q4OU4zZFJWK3p1L2lSeVNzOURaM2wyZnhyV3N0dUo3eWhxbGtoQWd5VzkzVEwiLCJtYWMiOiJmZGU0YWVkYzU3ZWE0NTg3OGUyM2EzYTExZmQ2M2RkZmE5NWE5OGQ1NGJmOTAxYjcwOTVlYjdlM2E3N2Q4ZjgzIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 16:53:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">delta_session=eyJpdiI6ImRxeUdDK3Q5Sm5EUTZJbFZFUS84b0E9PSIsInZhbHVlIjoiODBoWEdLVnpDWUVRRSsrRC9DMUhKbHJncFRUTXY3WW90WW9qeERBWEpEMDZPVWZMb2FMdUxyYWZxV1pHTHIrNFJzbnZGem42RzNDM3MrQjJ1VXFmSjdCa3ZNaEFDclJ4RDVsTkpzdyt0RGZ3cDNFL09WdGdKY1hQeGR4MkpMaWwiLCJtYWMiOiIwMGYyMDA2ZTllOTYyYmU0Njc5OGFjNWU2YzBmMjM4Mzk0MGQxZTY1MmYzN2MyZmY3OGJmNTUyMmYyMzEwNTNjIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 16:53:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2053113855 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iSnE31HdLWjb9pxwIm1DCJlCI99aPSw6mNotXKpu</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2053113855\", {\"maxDepth\":0})</script>\n"}}