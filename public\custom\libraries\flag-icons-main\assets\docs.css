.hide {
  display: none;
}

.header {
  margin: 0 0 48px;
  padding: 32px 0;
  text-align: center;
  background: linear-gradient(90deg, #2b8a3e, #e8590c);
  color: #fff;
  font-size: 1.1em;
}

.header pre {
  background-color: rgba(32, 32, 32, 0.3);
  padding: 8px;
  max-width: 320px;
  margin: 12px auto;
  border-radius: 4px;
}

h2 {
  text-align: center;
  margin: 32px 0;
}

.no-wrap {
  white-space: nowrap;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.flag {
  border-radius: 4px;
  border: 1px solid #dee2e6;
  margin-bottom: 32px;
  padding: 8px;
}

.flag-country {
  width: 100%;
  border-bottom: 1px solid #e9ecef;
}

.flag-img {
  width: 100%;
  aspect-ratio: 4 / 3;
  border: 1px solid #e9ecef;
  border-radius: 2px;
  overflow: hidden;
}

.flag-img-square {
  width: 100%;
  aspect-ratio: 1 / 1;
  border: 1px solid #e9ecef;
  border-radius: 2px;
  overflow: hidden;
}

.flag-code {
  color: #343a40;
  font-weight: 600;
}

.switch {
  position: fixed;
  bottom: 4px;
  left: 4px;
  display: flex;
  flex-direction: column;
  width: 48px;
}

.switch a {
  text-decoration: none;
  padding: 4px;
  margin: 4px;
  border-radius: 4px;
  border: 1px solid #868e96;
  text-align: center;
  width: 100%;
  color: #343a40;
  background-color: #e9ecef;
}
