{"__meta": {"id": "Xe21ee4189fbaef4fc592f158e014cb19", "datetime": "2025-06-27 17:43:16", "utime": **********.388968, "method": "GET", "uri": "/manufacturing/order/create", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.334932, "end": **********.388986, "duration": 1.0540540218353271, "duration_str": "1.05s", "measures": [{"label": "Booting", "start": **********.334932, "relative_start": 0, "end": **********.868683, "relative_end": **********.868683, "duration": 0.5337510108947754, "duration_str": "534ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.868702, "relative_start": 0.5337698459625244, "end": **********.388988, "relative_end": 1.9073486328125e-06, "duration": 0.5202860832214355, "duration_str": "520ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 28445656, "peak_usage_str": "27MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 99, "templates": [{"name": "1x manufacturing_orders.create", "param_count": null, "params": [], "start": **********.068237, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/manufacturing_orders/create.blade.phpmanufacturing_orders.create", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fmanufacturing_orders%2Fcreate.blade.php&line=1", "ajax": false, "filename": "create.blade.php", "line": "?"}, "render_count": 1, "name_original": "manufacturing_orders.create"}, {"name": "1x components.breadcrumb", "param_count": null, "params": [], "start": **********.071782, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/components/breadcrumb.blade.phpcomponents.breadcrumb", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fcomponents%2Fbreadcrumb.blade.php&line=1", "ajax": false, "filename": "breadcrumb.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.breadcrumb"}, {"name": "44x components.label", "param_count": null, "params": [], "start": **********.074549, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/components/label.blade.phpcomponents.label", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fcomponents%2Flabel.blade.php&line=1", "ajax": false, "filename": "label.blade.php", "line": "?"}, "render_count": 44, "name_original": "components.label"}, {"name": "1x manufacturing_orders._store_basic_info", "param_count": null, "params": [], "start": **********.075403, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/manufacturing_orders/_store_basic_info.blade.phpmanufacturing_orders._store_basic_info", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fmanufacturing_orders%2F_store_basic_info.blade.php&line=1", "ajax": false, "filename": "_store_basic_info.blade.php", "line": "?"}, "render_count": 1, "name_original": "manufacturing_orders._store_basic_info"}, {"name": "33x components.input", "param_count": null, "params": [], "start": **********.077748, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/components/input.blade.phpcomponents.input", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fcomponents%2Finput.blade.php&line=1", "ajax": false, "filename": "input.blade.php", "line": "?"}, "render_count": 33, "name_original": "components.input"}, {"name": "1x manufacturing_orders._store_dimensions", "param_count": null, "params": [], "start": **********.086037, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/manufacturing_orders/_store_dimensions.blade.phpmanufacturing_orders._store_dimensions", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fmanufacturing_orders%2F_store_dimensions.blade.php&line=1", "ajax": false, "filename": "_store_dimensions.blade.php", "line": "?"}, "render_count": 1, "name_original": "manufacturing_orders._store_dimensions"}, {"name": "1x manufacturing_orders._store_pricing", "param_count": null, "params": [], "start": **********.090518, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/manufacturing_orders/_store_pricing.blade.phpmanufacturing_orders._store_pricing", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fmanufacturing_orders%2F_store_pricing.blade.php&line=1", "ajax": false, "filename": "_store_pricing.blade.php", "line": "?"}, "render_count": 1, "name_original": "manufacturing_orders._store_pricing"}, {"name": "2x components.textarea", "param_count": null, "params": [], "start": **********.106192, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/components/textarea.blade.phpcomponents.textarea", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fcomponents%2Ftextarea.blade.php&line=1", "ajax": false, "filename": "textarea.blade.php", "line": "?"}, "render_count": 2, "name_original": "components.textarea"}, {"name": "2x components.anchor-tag", "param_count": null, "params": [], "start": **********.10721, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/components/anchor-tag.blade.phpcomponents.anchor-tag", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fcomponents%2Fanchor-tag.blade.php&line=1", "ajax": false, "filename": "anchor-tag.blade.php", "line": "?"}, "render_count": 2, "name_original": "components.anchor-tag"}, {"name": "1x manufacturing_orders._rideaux_basic_info", "param_count": null, "params": [], "start": **********.107929, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/manufacturing_orders/_rideaux_basic_info.blade.phpmanufacturing_orders._rideaux_basic_info", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fmanufacturing_orders%2F_rideaux_basic_info.blade.php&line=1", "ajax": false, "filename": "_rideaux_basic_info.blade.php", "line": "?"}, "render_count": 1, "name_original": "manufacturing_orders._rideaux_basic_info"}, {"name": "1x manufacturing_orders._rideaux_dimensions", "param_count": null, "params": [], "start": **********.119765, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/manufacturing_orders/_rideaux_dimensions.blade.phpmanufacturing_orders._rideaux_dimensions", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fmanufacturing_orders%2F_rideaux_dimensions.blade.php&line=1", "ajax": false, "filename": "_rideaux_dimensions.blade.php", "line": "?"}, "render_count": 1, "name_original": "manufacturing_orders._rideaux_dimensions"}, {"name": "1x manufacturing_orders._rideaux_details", "param_count": null, "params": [], "start": **********.123834, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/manufacturing_orders/_rideaux_details.blade.phpmanufacturing_orders._rideaux_details", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fmanufacturing_orders%2F_rideaux_details.blade.php&line=1", "ajax": false, "filename": "_rideaux_details.blade.php", "line": "?"}, "render_count": 1, "name_original": "manufacturing_orders._rideaux_details"}, {"name": "1x manufacturing_orders._rideaux_pricing", "param_count": null, "params": [], "start": **********.130357, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/manufacturing_orders/_rideaux_pricing.blade.phpmanufacturing_orders._rideaux_pricing", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fmanufacturing_orders%2F_rideaux_pricing.blade.php&line=1", "ajax": false, "filename": "_rideaux_pricing.blade.php", "line": "?"}, "render_count": 1, "name_original": "manufacturing_orders._rideaux_pricing"}, {"name": "1x layouts.app", "param_count": null, "params": [], "start": **********.159154, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/layouts/app.blade.phplayouts.app", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.app"}, {"name": "1x layouts.head", "param_count": null, "params": [], "start": **********.160188, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/layouts/head.blade.phplayouts.head", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Flayouts%2Fhead.blade.php&line=1", "ajax": false, "filename": "head.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.head"}, {"name": "1x layouts.page-loader", "param_count": null, "params": [], "start": **********.163869, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/layouts/page-loader.blade.phplayouts.page-loader", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Flayouts%2Fpage-loader.blade.php&line=1", "ajax": false, "filename": "page-loader.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.page-loader"}, {"name": "1x layouts.navigation", "param_count": null, "params": [], "start": **********.165901, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/layouts/navigation.blade.phplayouts.navigation", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Flayouts%2Fnavigation.blade.php&line=1", "ajax": false, "filename": "navigation.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.navigation"}, {"name": "1x layouts.header", "param_count": null, "params": [], "start": **********.323682, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/layouts/header.blade.phplayouts.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Flayouts%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.header"}, {"name": "1x components.header-shortcut-menu", "param_count": null, "params": [], "start": **********.333289, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/components/header-shortcut-menu.blade.phpcomponents.header-shortcut-menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fcomponents%2Fheader-shortcut-menu.blade.php&line=1", "ajax": false, "filename": "header-shortcut-menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.header-shortcut-menu"}, {"name": "1x components.flag-toggle", "param_count": null, "params": [], "start": **********.36487, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/components/flag-toggle.blade.phpcomponents.flag-toggle", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Fcomponents%2Fflag-toggle.blade.php&line=1", "ajax": false, "filename": "flag-toggle.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.flag-toggle"}, {"name": "1x layouts.footer", "param_count": null, "params": [], "start": **********.372944, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/layouts/footer.blade.phplayouts.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Flayouts%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.footer"}, {"name": "1x layouts.script", "param_count": null, "params": [], "start": **********.374355, "type": "blade", "hash": "bladeD:\\www\\delta\\resources\\views/layouts/script.blade.phplayouts.script", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Flayouts%2Fscript.blade.php&line=1", "ajax": false, "filename": "script.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.script"}]}, "route": {"uri": "GET manufacturing/order/create", "middleware": "web, auth, can:manufacturing.order.create", "as": "manufacturing.order.create", "controller": "App\\Http\\Controllers\\ManufacturingOrderController@create", "namespace": null, "prefix": "/manufacturing", "where": [], "file": "<a href=\"phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FHttp%2FControllers%2FManufacturingOrderController.php&line=30\" onclick=\"\">app/Http/Controllers/ManufacturingOrderController.php:30-42</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.031279999999999995, "accumulated_duration_str": "31.28ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 17, "namespace": "middleware", "name": "auth", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}], "start": **********.940296, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:168", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "deltapos", "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.9542909, "duration": 0.02197, "duration_str": "21.97ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "deltapos", "start_percent": 0, "width_percent": 70.237}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 1 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["1", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\www\\delta\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\www\\delta\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 193}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\www\\delta\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 244}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\www\\delta\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 136}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.019315, "duration": 0.00269, "duration_str": "2.69ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:305", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:305", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=305", "ajax": false, "filename": "HasPermissions.php", "line": "305"}, "connection": "deltapos", "start_percent": 70.237, "width_percent": 8.6}, {"sql": "select * from `manufacturing_orders` order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ManufacturingOrderController.php", "file": "D:\\www\\delta\\app\\Http\\Controllers\\ManufacturingOrderController.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.044029, "duration": 0.00239, "duration_str": "2.39ms", "memory": 0, "memory_str": null, "filename": "ManufacturingOrderController.php:33", "source": "app/Http/Controllers/ManufacturingOrderController.php:33", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FHttp%2FControllers%2FManufacturingOrderController.php&line=33", "ajax": false, "filename": "ManufacturingOrderController.php", "line": "33"}, "connection": "deltapos", "start_percent": 78.836, "width_percent": 7.641}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\www\\delta\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, {"index": 23, "namespace": null, "name": "app/Providers/AuthServiceProvider.php", "file": "D:\\www\\delta\\app\\Providers\\AuthServiceProvider.php", "line": 42}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 438}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 406}], "start": **********.246587, "duration": 0.00209, "duration_str": "2.09ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:188", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:188", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=188", "ajax": false, "filename": "HasRoles.php", "line": "188"}, "connection": "deltapos", "start_percent": 86.477, "width_percent": 6.682}, {"sql": "select `id`, `name`, `code`, `emoji` from `languages` where `status` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/View/Components/FlagToggle.php", "file": "D:\\www\\delta\\app\\View\\Components\\FlagToggle.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 961}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.35983, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "FlagToggle.php:43", "source": "app/View/Components/FlagToggle.php:43", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FView%2FComponents%2FFlagToggle.php&line=43", "ajax": false, "filename": "FlagToggle.php", "line": "43"}, "connection": "deltapos", "start_percent": 93.159, "width_percent": 3.037}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "view", "name": "layouts.header", "file": "D:\\www\\delta\\resources\\views/layouts/header.blade.php", "line": 81}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\www\\delta\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.3682501, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "layouts.header:81", "source": "view::layouts.header:81", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fresources%2Fviews%2Flayouts%2Fheader.blade.php&line=81", "ajax": false, "filename": "header.blade.php", "line": "81"}, "connection": "deltapos", "start_percent": 96.196, "width_percent": 3.804}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 167, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\Language": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwww%2Fdelta%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 172, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 114, "messages": [{"message": "[\n  ability => manufacturing.order.create,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-610003097 data-indent-pad=\"  \"><span class=sf-dump-note>manufacturing.order.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"26 characters\">manufacturing.order.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-610003097\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.042825, "xdebug_link": null}, {"message": "[ability => customer.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-105374298 data-indent-pad=\"  \"><span class=sf-dump-note>customer.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">customer.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-105374298\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.172787, "xdebug_link": null}, {"message": "[ability => customer.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-345577782 data-indent-pad=\"  \"><span class=sf-dump-note>customer.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">customer.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-345577782\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.173869, "xdebug_link": null}, {"message": "[ability => supplier.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-285308747 data-indent-pad=\"  \"><span class=sf-dump-note>supplier.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">supplier.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-285308747\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.177107, "xdebug_link": null}, {"message": "[ability => sale.invoice.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1148132194 data-indent-pad=\"  \"><span class=sf-dump-note>sale.invoice.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">sale.invoice.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1148132194\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.181339, "xdebug_link": null}, {"message": "[ability => sale.invoice.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1724263694 data-indent-pad=\"  \"><span class=sf-dump-note>sale.invoice.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">sale.invoice.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1724263694\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.183897, "xdebug_link": null}, {"message": "[ability => sale.invoice.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-960353878 data-indent-pad=\"  \"><span class=sf-dump-note>sale.invoice.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">sale.invoice.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-960353878\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.185721, "xdebug_link": null}, {"message": "[ability => sale.quotation.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-313025082 data-indent-pad=\"  \"><span class=sf-dump-note>sale.quotation.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">sale.quotation.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-313025082\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.187729, "xdebug_link": null}, {"message": "[ability => sale.invoice.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1318259776 data-indent-pad=\"  \"><span class=sf-dump-note>sale.invoice.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">sale.invoice.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1318259776\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.189112, "xdebug_link": null}, {"message": "[ability => sale.order.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-618784510 data-indent-pad=\"  \"><span class=sf-dump-note>sale.order.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">sale.order.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-618784510\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.190778, "xdebug_link": null}, {"message": "[ability => sale.return.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-991103863 data-indent-pad=\"  \"><span class=sf-dump-note>sale.return.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">sale.return.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-991103863\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.192193, "xdebug_link": null}, {"message": "[ability => purchase.bill.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1087850914 data-indent-pad=\"  \"><span class=sf-dump-note>purchase.bill.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">purchase.bill.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1087850914\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.193434, "xdebug_link": null}, {"message": "[ability => purchase.bill.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-888580436 data-indent-pad=\"  \"><span class=sf-dump-note>purchase.bill.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">purchase.bill.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-888580436\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.196726, "xdebug_link": null}, {"message": "[ability => purchase.bill.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1643488307 data-indent-pad=\"  \"><span class=sf-dump-note>purchase.bill.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">purchase.bill.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1643488307\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.19919, "xdebug_link": null}, {"message": "[ability => purchase.order.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-275988095 data-indent-pad=\"  \"><span class=sf-dump-note>purchase.order.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">purchase.order.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-275988095\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.201052, "xdebug_link": null}, {"message": "[ability => purchase.return.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2002275925 data-indent-pad=\"  \"><span class=sf-dump-note>purchase.return.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">purchase.return.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2002275925\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.202821, "xdebug_link": null}, {"message": "[ability => item.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-950700763 data-indent-pad=\"  \"><span class=sf-dump-note>item.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">item.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-950700763\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.203815, "xdebug_link": null}, {"message": "[ability => item.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-736280668 data-indent-pad=\"  \"><span class=sf-dump-note>item.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">item.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-736280668\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.20536, "xdebug_link": null}, {"message": "[ability => item.category.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1721880614 data-indent-pad=\"  \"><span class=sf-dump-note>item.category.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">item.category.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1721880614\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.206572, "xdebug_link": null}, {"message": "[ability => item.brand.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1197239293 data-indent-pad=\"  \"><span class=sf-dump-note>item.brand.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">item.brand.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1197239293\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.208342, "xdebug_link": null}, {"message": "[ability => expense.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1599684376 data-indent-pad=\"  \"><span class=sf-dump-note>expense.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">expense.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1599684376\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.209129, "xdebug_link": null}, {"message": "[ability => expense.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-812021406 data-indent-pad=\"  \"><span class=sf-dump-note>expense.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">expense.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-812021406\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.210538, "xdebug_link": null}, {"message": "[ability => expense.category.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-192257098 data-indent-pad=\"  \"><span class=sf-dump-note>expense.category.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">expense.category.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-192257098\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.21231, "xdebug_link": null}, {"message": "[\n  ability => expense.subcategory.view,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1942370997 data-indent-pad=\"  \"><span class=sf-dump-note>expense.subcategory.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">expense.subcategory.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1942370997\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.217214, "xdebug_link": null}, {"message": "[ability => transaction.cash.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1482242337 data-indent-pad=\"  \"><span class=sf-dump-note>transaction.cash.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">transaction.cash.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1482242337\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.22059, "xdebug_link": null}, {"message": "[ability => transaction.cash.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-780475772 data-indent-pad=\"  \"><span class=sf-dump-note>transaction.cash.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">transaction.cash.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-780475772\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.224281, "xdebug_link": null}, {"message": "[\n  ability => transaction.cheque.view,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>transaction.cheque.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">transaction.cheque.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.227217, "xdebug_link": null}, {"message": "[ability => transaction.bank.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>transaction.bank.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">transaction.bank.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.230787, "xdebug_link": null}, {"message": "[ability => warehouse.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-note>warehouse.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">warehouse.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.232986, "xdebug_link": null}, {"message": "[ability => warehouse.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1299082870 data-indent-pad=\"  \"><span class=sf-dump-note>warehouse.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">warehouse.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1299082870\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.235582, "xdebug_link": null}, {"message": "[ability => stock_transfer.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-405272833 data-indent-pad=\"  \"><span class=sf-dump-note>stock_transfer.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">stock_transfer.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-405272833\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.237264, "xdebug_link": null}, {"message": "[ability => import.item, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-939036255 data-indent-pad=\"  \"><span class=sf-dump-note>import.item</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">import.item</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-939036255\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.239018, "xdebug_link": null}, {"message": "[ability => import.item, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-653144745 data-indent-pad=\"  \"><span class=sf-dump-note>import.item</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">import.item</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-653144745\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.240495, "xdebug_link": null}, {"message": "[ability => import.party, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-684651339 data-indent-pad=\"  \"><span class=sf-dump-note>import.party</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">import.party</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-684651339\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.241857, "xdebug_link": null}, {"message": "[ability => generate.barcode, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>generate.barcode</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">generate.barcode</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.24323, "xdebug_link": null}, {"message": "[ability => account.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>account.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">account.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.250183, "xdebug_link": null}, {"message": "[ability => account.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>account.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">account.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.252568, "xdebug_link": null}, {"message": "[ability => account.group.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>account.group.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">account.group.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.254048, "xdebug_link": null}, {"message": "[ability => profile.edit, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>profile.edit</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">profile.edit</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.255178, "xdebug_link": null}, {"message": "[ability => profile.edit, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-230431009 data-indent-pad=\"  \"><span class=sf-dump-note>profile.edit</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">profile.edit</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-230431009\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.257007, "xdebug_link": null}, {"message": "[ability => user.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1602552313 data-indent-pad=\"  \"><span class=sf-dump-note>user.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">user.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1602552313\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.257296, "xdebug_link": null}, {"message": "[ability => role.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-306852221 data-indent-pad=\"  \"><span class=sf-dump-note>role.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">role.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-306852221\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.257566, "xdebug_link": null}, {"message": "[ability => permission.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-133200416 data-indent-pad=\"  \"><span class=sf-dump-note>permission.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">permission.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-133200416\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.258182, "xdebug_link": null}, {"message": "[ability => sms.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2023817538 data-indent-pad=\"  \"><span class=sf-dump-note>sms.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">sms.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2023817538\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.258881, "xdebug_link": null}, {"message": "[ability => sms.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1847386068 data-indent-pad=\"  \"><span class=sf-dump-note>sms.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">sms.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1847386068\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.260119, "xdebug_link": null}, {"message": "[ability => sms.template.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1844884464 data-indent-pad=\"  \"><span class=sf-dump-note>sms.template.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">sms.template.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1844884464\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.261057, "xdebug_link": null}, {"message": "[ability => email.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-609136301 data-indent-pad=\"  \"><span class=sf-dump-note>email.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">email.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-609136301\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.262115, "xdebug_link": null}, {"message": "[ability => email.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-289789640 data-indent-pad=\"  \"><span class=sf-dump-note>email.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">email.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-289789640\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.262679, "xdebug_link": null}, {"message": "[ability => email.template.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-47168110 data-indent-pad=\"  \"><span class=sf-dump-note>email.template.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">email.template.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-47168110\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.263293, "xdebug_link": null}, {"message": "[ability => report.*, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-931340293 data-indent-pad=\"  \"><span class=sf-dump-note>report.*</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">report.*</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-931340293\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.26401, "xdebug_link": null}, {"message": "[ability => report.profit_and_loss, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-504320820 data-indent-pad=\"  \"><span class=sf-dump-note>report.profit_and_loss</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">report.profit_and_loss</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-504320820\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.264747, "xdebug_link": null}, {"message": "[\n  ability => report.item.transaction.batch,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1331966549 data-indent-pad=\"  \"><span class=sf-dump-note>report.item.transaction.batch</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"29 characters\">report.item.transaction.batch</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1331966549\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.265461, "xdebug_link": null}, {"message": "[\n  ability => report.item.transaction.batch,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1091218089 data-indent-pad=\"  \"><span class=sf-dump-note>report.item.transaction.batch</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"29 characters\">report.item.transaction.batch</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1091218089\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.26605, "xdebug_link": null}, {"message": "[\n  ability => report.item.transaction.serial,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1310702494 data-indent-pad=\"  \"><span class=sf-dump-note>report.item.transaction.serial</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"30 characters\">report.item.transaction.serial</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1310702494\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.266616, "xdebug_link": null}, {"message": "[\n  ability => report.item.transaction.general,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1144213999 data-indent-pad=\"  \"><span class=sf-dump-note>report.item.transaction.general</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"31 characters\">report.item.transaction.general</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1144213999\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.267273, "xdebug_link": null}, {"message": "[ability => report.purchase, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2047161307 data-indent-pad=\"  \"><span class=sf-dump-note>report.purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">report.purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2047161307\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.267925, "xdebug_link": null}, {"message": "[ability => report.purchase, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1362173926 data-indent-pad=\"  \"><span class=sf-dump-note>report.purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">report.purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1362173926\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.268548, "xdebug_link": null}, {"message": "[ability => report.purchase.item, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-922357398 data-indent-pad=\"  \"><span class=sf-dump-note>report.purchase.item</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">report.purchase.item</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-922357398\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.269187, "xdebug_link": null}, {"message": "[\n  ability => report.purchase.payment,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1370603704 data-indent-pad=\"  \"><span class=sf-dump-note>report.purchase.payment</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">report.purchase.payment</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1370603704\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.26983, "xdebug_link": null}, {"message": "[ability => report.sale, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2027935599 data-indent-pad=\"  \"><span class=sf-dump-note>report.sale</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">report.sale</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2027935599\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.270479, "xdebug_link": null}, {"message": "[ability => report.sale, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-394779975 data-indent-pad=\"  \"><span class=sf-dump-note>report.sale</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">report.sale</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-394779975\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.271331, "xdebug_link": null}, {"message": "[ability => report.sale.item, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-693875439 data-indent-pad=\"  \"><span class=sf-dump-note>report.sale.item</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">report.sale.item</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-693875439\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.272161, "xdebug_link": null}, {"message": "[ability => report.sale.payment, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-889465653 data-indent-pad=\"  \"><span class=sf-dump-note>report.sale.payment</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">report.sale.payment</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-889465653\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.272992, "xdebug_link": null}, {"message": "[\n  ability => report.customer.due.payment,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1067772949 data-indent-pad=\"  \"><span class=sf-dump-note>report.customer.due.payment</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"27 characters\">report.customer.due.payment</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1067772949\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.274743, "xdebug_link": null}, {"message": "[\n  ability => report.customer.due.payment,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-468162945 data-indent-pad=\"  \"><span class=sf-dump-note>report.customer.due.payment</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"27 characters\">report.customer.due.payment</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-468162945\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.276264, "xdebug_link": null}, {"message": "[\n  ability => report.supplier.due.payment,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-235261010 data-indent-pad=\"  \"><span class=sf-dump-note>report.supplier.due.payment</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"27 characters\">report.supplier.due.payment</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-235261010\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.278938, "xdebug_link": null}, {"message": "[ability => report.expense, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-63759867 data-indent-pad=\"  \"><span class=sf-dump-note>report.expense</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">report.expense</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-63759867\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.280416, "xdebug_link": null}, {"message": "[ability => report.expense, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1255072865 data-indent-pad=\"  \"><span class=sf-dump-note>report.expense</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">report.expense</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1255072865\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.281398, "xdebug_link": null}, {"message": "[ability => report.expense.item, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1155975092 data-indent-pad=\"  \"><span class=sf-dump-note>report.expense.item</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">report.expense.item</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1155975092\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.282391, "xdebug_link": null}, {"message": "[ability => report.expense.payment, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-675188879 data-indent-pad=\"  \"><span class=sf-dump-note>report.expense.payment</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">report.expense.payment</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-675188879\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.283563, "xdebug_link": null}, {"message": "[\n  ability => report.transaction.cashflow,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1176541502 data-indent-pad=\"  \"><span class=sf-dump-note>report.transaction.cashflow</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"27 characters\">report.transaction.cashflow</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1176541502\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.284348, "xdebug_link": null}, {"message": "[\n  ability => report.transaction.cashflow,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>report.transaction.cashflow</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"27 characters\">report.transaction.cashflow</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.285042, "xdebug_link": null}, {"message": "[\n  ability => report.transaction.bank-statement,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>report.transaction.bank-statement</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"33 characters\">report.transaction.bank-statement</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.285608, "xdebug_link": null}, {"message": "[ability => report.gst*, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>report.gst*</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">report.gst*</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.2862, "xdebug_link": null}, {"message": "[ability => report.gstr1, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1616257066 data-indent-pad=\"  \"><span class=sf-dump-note>report.gstr1</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">report.gstr1</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1616257066\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.28697, "xdebug_link": null}, {"message": "[ability => report.gstr2, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-744498405 data-indent-pad=\"  \"><span class=sf-dump-note>report.gstr2</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">report.gstr2</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-744498405\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.287741, "xdebug_link": null}, {"message": "[ability => report.stock_transfer, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-511872881 data-indent-pad=\"  \"><span class=sf-dump-note>report.stock_transfer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">report.stock_transfer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-511872881\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.289333, "xdebug_link": null}, {"message": "[ability => report.stock_transfer, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2111354815 data-indent-pad=\"  \"><span class=sf-dump-note>report.stock_transfer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">report.stock_transfer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2111354815\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.290347, "xdebug_link": null}, {"message": "[\n  ability => report.stock_transfer.item,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1688754723 data-indent-pad=\"  \"><span class=sf-dump-note>report.stock_transfer.item</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"26 characters\">report.stock_transfer.item</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1688754723\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.29198, "xdebug_link": null}, {"message": "[ability => report.stock_report.*, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2002991426 data-indent-pad=\"  \"><span class=sf-dump-note>report.stock_report.*</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">report.stock_report.*</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2002991426\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.293159, "xdebug_link": null}, {"message": "[\n  ability => report.stock_report.item.batch,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-679832957 data-indent-pad=\"  \"><span class=sf-dump-note>report.stock_report.item.batch</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"30 characters\">report.stock_report.item.batch</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-679832957\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.296092, "xdebug_link": null}, {"message": "[\n  ability => report.stock_report.item.serial,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1079953123 data-indent-pad=\"  \"><span class=sf-dump-note>report.stock_report.item.serial</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"31 characters\">report.stock_report.item.serial</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1079953123\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.299966, "xdebug_link": null}, {"message": "[\n  ability => report.stock_report.item.general,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-821890672 data-indent-pad=\"  \"><span class=sf-dump-note>report.stock_report.item.general</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"32 characters\">report.stock_report.item.general</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-821890672\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.302634, "xdebug_link": null}, {"message": "[ability => report.expired.item, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1895212620 data-indent-pad=\"  \"><span class=sf-dump-note>report.expired.item</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">report.expired.item</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1895212620\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.303612, "xdebug_link": null}, {"message": "[ability => report.reorder.item, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1277125312 data-indent-pad=\"  \"><span class=sf-dump-note>report.reorder.item</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">report.reorder.item</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1277125312\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.304443, "xdebug_link": null}, {"message": "[ability => tax.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2140540460 data-indent-pad=\"  \"><span class=sf-dump-note>tax.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">tax.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2140540460\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.304831, "xdebug_link": null}, {"message": "[ability => app.settings.edit, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>app.settings.edit</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">app.settings.edit</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.305195, "xdebug_link": null}, {"message": "[ability => company.edit, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1772980015 data-indent-pad=\"  \"><span class=sf-dump-note>company.edit</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">company.edit</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1772980015\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.305601, "xdebug_link": null}, {"message": "[ability => tax.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1197621142 data-indent-pad=\"  \"><span class=sf-dump-note>tax.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">tax.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1197621142\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.305854, "xdebug_link": null}, {"message": "[ability => payment.type.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>payment.type.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">payment.type.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.306666, "xdebug_link": null}, {"message": "[ability => currency.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1864832745 data-indent-pad=\"  \"><span class=sf-dump-note>currency.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">currency.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1864832745\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.308415, "xdebug_link": null}, {"message": "[ability => unit.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1150808606 data-indent-pad=\"  \"><span class=sf-dump-note>unit.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">unit.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1150808606\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.309767, "xdebug_link": null}, {"message": "[ability => language.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-134258857 data-indent-pad=\"  \"><span class=sf-dump-note>language.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">language.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-134258857\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.311036, "xdebug_link": null}, {"message": "[\n  ability => manufacturing.order.view,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1643716609 data-indent-pad=\"  \"><span class=sf-dump-note>manufacturing.order.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manufacturing.order.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1643716609\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.318041, "xdebug_link": null}, {"message": "[\n  ability => manufacturing.order.view,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1542485450 data-indent-pad=\"  \"><span class=sf-dump-note>manufacturing.order.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manufacturing.order.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1542485450\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.320755, "xdebug_link": null}, {"message": "[\n  ability => manufacturing.calculation.view,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-417363366 data-indent-pad=\"  \"><span class=sf-dump-note>manufacturing.calculation.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"30 characters\">manufacturing.calculation.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-417363366\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.322693, "xdebug_link": null}, {"message": "[ability => purchase.bill.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-527627626 data-indent-pad=\"  \"><span class=sf-dump-note>purchase.bill.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">purchase.bill.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-527627626\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.326173, "xdebug_link": null}, {"message": "[ability => sale.invoice.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1034117785 data-indent-pad=\"  \"><span class=sf-dump-note>sale.invoice.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">sale.invoice.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1034117785\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.329401, "xdebug_link": null}, {"message": "[ability => sale.invoice.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-702310225 data-indent-pad=\"  \"><span class=sf-dump-note>sale.invoice.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">sale.invoice.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-702310225\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.331902, "xdebug_link": null}, {"message": "[ability => sale.invoice.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1147152437 data-indent-pad=\"  \"><span class=sf-dump-note>sale.invoice.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">sale.invoice.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1147152437\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.33723, "xdebug_link": null}, {"message": "[ability => purchase.bill.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1344845472 data-indent-pad=\"  \"><span class=sf-dump-note>purchase.bill.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">purchase.bill.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1344845472\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.340423, "xdebug_link": null}, {"message": "[ability => expense.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1091140311 data-indent-pad=\"  \"><span class=sf-dump-note>expense.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">expense.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1091140311\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.341997, "xdebug_link": null}, {"message": "[ability => sale.invoice.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-624102173 data-indent-pad=\"  \"><span class=sf-dump-note>sale.invoice.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">sale.invoice.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-624102173\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.344464, "xdebug_link": null}, {"message": "[ability => purchase.bill.view, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1578243810 data-indent-pad=\"  \"><span class=sf-dump-note>purchase.bill.view</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">purchase.bill.view</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1578243810\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.34702, "xdebug_link": null}, {"message": "[ability => purchase.order.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-814262437 data-indent-pad=\"  \"><span class=sf-dump-note>purchase.order.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">purchase.order.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-814262437\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.349597, "xdebug_link": null}, {"message": "[ability => sale.return.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1874774132 data-indent-pad=\"  \"><span class=sf-dump-note>sale.return.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">sale.return.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1874774132\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.351678, "xdebug_link": null}, {"message": "[ability => purchase.return.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-804675635 data-indent-pad=\"  \"><span class=sf-dump-note>purchase.return.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">purchase.return.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-804675635\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.353859, "xdebug_link": null}, {"message": "[ability => stock_transfer.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2119655239 data-indent-pad=\"  \"><span class=sf-dump-note>stock_transfer.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">stock_transfer.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2119655239\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.35492, "xdebug_link": null}, {"message": "[ability => item.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-112864439 data-indent-pad=\"  \"><span class=sf-dump-note>item.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">item.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-112864439\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.355848, "xdebug_link": null}, {"message": "[ability => customer.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1045880832 data-indent-pad=\"  \"><span class=sf-dump-note>customer.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">customer.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1045880832\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.356055, "xdebug_link": null}, {"message": "[ability => supplier.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-209840559 data-indent-pad=\"  \"><span class=sf-dump-note>supplier.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">supplier.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-209840559\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.357161, "xdebug_link": null}, {"message": "[ability => sale.quotation.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-3408209 data-indent-pad=\"  \"><span class=sf-dump-note>sale.quotation.create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">sale.quotation.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-3408209\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.358871, "xdebug_link": null}, {"message": "[\n  ability => general.permission.to.apply.discount.to.purchase,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2085616 data-indent-pad=\"  \"><span class=sf-dump-note>general.permission.to.apply.discount.to.purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"48 characters\">general.permission.to.apply.discount.to.purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2085616\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.379188, "xdebug_link": null}, {"message": "[\n  ability => general.permission.to.apply.discount.to.sale,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-20260191 data-indent-pad=\"  \"><span class=sf-dump-note>general.permission.to.apply.discount.to.sale</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"44 characters\">general.permission.to.apply.discount.to.sale</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-20260191\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.381419, "xdebug_link": null}]}, "session": {"_token": "DbgoOTCuumHy04EHLVHFGwOg3Jxe3DqOD4DE9hb1", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/manufacturing/order/create\"\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"telescope": "<a href=\"http://127.0.0.1:8000/_debugbar/telescope/9f41a331-cc22-4fd2-8117-15c419102255\" target=\"_blank\">View in Telescope</a>", "path_info": "/manufacturing/order/create", "status_code": "<pre class=sf-dump id=sf-dump-668749303 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-668749303\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-447936074 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-447936074\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-968172508 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-968172508\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-547541628 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/manufacturing/order</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">en-GB,en;q=0.9,en-US;q=0.8,fr;q=0.7,ar;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2047 characters\">ph_phc_3ESMmY9SgqEAGBB6sMGK5ayYHkeUuknH2vP6FmWH9RA_posthog=%7B%22distinct_id%22%3A%220196b41d-0793-7e06-86ce-017691734ea0%22%2C%22%24sesid%22%3A%5B1746780823698%2C%220196b41d-078e-7e17-a700-24eed1211756%22%2C1746778523534%5D%7D; aureuserp_session=eyJpdiI6IlIvOXVBdXl4UFJOWHhsSjA2YWNNeGc9PSIsInZhbHVlIjoiVXFQdlhTd3ZFMXZyV1JwNjczTEJUczB4N3YxZG5uWXNpamRHWXZyb0xIdDdiaXFoL2xOalBmNmtqMVdKZWllamlOWnNxREZvNjV5VWVxOXVGY3Q5MVNqK1ArTVBteDZIY01KTXdoc2xEaDEwY2draUl3Qi9SbHJUUnVOOWNsY1ciLCJtYWMiOiI1OWU4NmE1NjczOTQyZjZjZDM4YTYyMzZhMmZmODg4NGJlMWQ1ZjY3MjRkNzM5ZmRkMTllNDFjMTE1ODYxN2QyIiwidGFnIjoiIn0%3D; theme_mode=eyJpdiI6Ii9XM3FTcWRDTHBmaVg5M0k2cFJiMEE9PSIsInZhbHVlIjoiNlZBU0xFRmZDaEdXVE5JRVMyMFhQcG0waG1PUWdFSDdWb1pCMWx3R0JVank5WXhQMytscW4vTW4yMkZSWlhWNjVISlc3NjNtcGNMckVYK1JXb3FOU3c9PSIsIm1hYyI6ImU2MmRhOThiNGQyNDA3N2UwMGViMjRhMzRhOThkYmJkZDdjMmNjYjhjZDk2MjIxMWZlZjVlNjA4Y2ZkYTkzNGIiLCJ0YWciOiIifQ%3D%3D; language_data=eyJpdiI6InpaMG5kK3BkZ2c3NFZqKy9nVVFRd2c9PSIsInZhbHVlIjoicmw0UjBkdFZZclA3UlljTXVDSE9HWGN6S05VUHdZamlZb2hjajFlcm9uVU1LNFpsZlhQRW5wTTlPNlUrRUJ5VUMzOU5lSnJHY2JBZUIvNC95Ujl5bnFxTWpmVEt5aXY2dXJVemZlbG1LenFtdmZEKzE5RXhsc1hJUE1zVk0wNlJMTEhvTUJXLzVnWHN6d1FnVzh1WnVGUWFRN2xvUyt4aC9pRzVaWTlBMFN4NWhQN1ZSL3dGY0ljZzZOZ1UxbUN6IiwibWFjIjoiYWE2MTZlMmNhMGM3ZjMwNTRhOTZkZjc4ZTZlNzI3NjUzMjY3NDMyNmJlYmViMmY4NjFiZDExMTBjNjMzNWFjNiIsInRhZyI6IiJ9; XSRF-TOKEN=eyJpdiI6IkVqMXFiWFFRTm9yTm5QRnp5UUFMalE9PSIsInZhbHVlIjoic0oyK1BESFdxTkRma1pnZXhPUzB1emxkWDA5ZDlxaDJiUlNEVzZ1aElTcmRhWk8vVXJ4dGVkSktDOEErcHFtcjFLbkJUWnlaU3cvRWlBb0lmaXdFSHBoRGFIWUZmMk9TM3RFTDN6S3hBYmZrZ0J0S1E0bmptOFl3L2lWd2QyaGwiLCJtYWMiOiIwYTRmYzQzNDAwMDVhY2I5YjViOGRlMzNhNTI1YzE5YTlkMWIzYzBiNmRhNWMxNDI1MDI3ZmM5ODg3MzRlMWJiIiwidGFnIjoiIn0%3D; delta_session=eyJpdiI6IjJUT09heVJJaVNsdUZhZDUvOFllWWc9PSIsInZhbHVlIjoibjhGb3JoR3NId2ZPemtONk5EenJoL0Rwb0QxYlVtQytMUVZ6MmZ6MkliRm9kZkVjS3VjdldyZ0hGemNtQjE1bHgrbFZlWDREWUl0N3JhR2Z6NXRhUTZnRDk5TERmL3dMVitvYmNUTlhjYXZWdXpPNm1tNk9MdStIUnZVUnhtbm0iLCJtYWMiOiIzZDNmNDkwNzM4ZDIxOGI4OGZkMjhkYzJjZjU1ZjIzZDg2MzJlMTY5MGQyNTRiZTQ1NzEwMTgxZjM0YWZjOTUzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-547541628\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1183114738 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ph_phc_3ESMmY9SgqEAGBB6sMGK5ayYHkeUuknH2vP6FmWH9RA_posthog</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>aureuserp_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>theme_mode</span>\" => \"<span class=sf-dump-str title=\"11 characters\">light-theme</span>\"\n  \"<span class=sf-dump-key>language_data</span>\" => \"<span class=sf-dump-str title=\"94 characters\">{&quot;language_code&quot;:&quot;fr&quot;,&quot;language_flag&quot;:&quot;flag-icon-fr&quot;,&quot;direction&quot;:&quot;ltr&quot;,&quot;emoji&quot;:&quot;flag-icon-fr&quot;}</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DbgoOTCuumHy04EHLVHFGwOg3Jxe3DqOD4DE9hb1</span>\"\n  \"<span class=sf-dump-key>delta_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BzceRqun0Ae5OezZzHQypR8L1x1bHb6dsUfkY5Ah</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1183114738\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1667226676 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 16:43:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkswWVplV0FFK21FQUU3eDBEckx2TkE9PSIsInZhbHVlIjoicUJBajAxRmdGMXFZMkswOVhLeloya0g2ZXEzN2FNcjhwd0dDV1ZHdnE0N01tWjhnaXBvV2FnOURTQ2pDQ1g0QUdKTFVDSWY0QXFyT0FQVGF4cUNWK3oxYjlVbUY5eHAyTXNKTXBtUHBCRjZkV1JOWG5KTXV6VUk3UWF2UDFxZisiLCJtYWMiOiIwZDQ2MGE0ZDM5Y2FiMmNiZjA3OTRlOTI5MDg4ZTc0NDA1Y2VhZjlhZDNhZTcyNzM2MWE3Mzk0Zjc4ZThmY2U3IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 18:43:16 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">delta_session=eyJpdiI6InNvZk1ITkVwMFJyL0ZUek1YbFVyaWc9PSIsInZhbHVlIjoiR0hmOVpIVU5xK2N0Z1dLaCtKUWlYRkNJSVdGT0FuSlpaeG5kQllVdUVtSS9uSmFTQXVxRUhqZFFkTjA1MWxqbEZRaUNzdG5xMVRWdmt4NUIyNW5JZWovYnRjNHIzTXF4TzdQZmY5akw0OXFSSlNkWHhlN1JVS0wxKzd0QzBqYmciLCJtYWMiOiJhM2VlNWU0NTA1M2U2NTIzMzk3ODRiMTUxYWMzMmFmMzZmZDkwMTg5YjYzZWE2NjRjNDFlNTBmNmNmZGExOTA0IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 18:43:16 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkswWVplV0FFK21FQUU3eDBEckx2TkE9PSIsInZhbHVlIjoicUJBajAxRmdGMXFZMkswOVhLeloya0g2ZXEzN2FNcjhwd0dDV1ZHdnE0N01tWjhnaXBvV2FnOURTQ2pDQ1g0QUdKTFVDSWY0QXFyT0FQVGF4cUNWK3oxYjlVbUY5eHAyTXNKTXBtUHBCRjZkV1JOWG5KTXV6VUk3UWF2UDFxZisiLCJtYWMiOiIwZDQ2MGE0ZDM5Y2FiMmNiZjA3OTRlOTI5MDg4ZTc0NDA1Y2VhZjlhZDNhZTcyNzM2MWE3Mzk0Zjc4ZThmY2U3IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 18:43:16 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">delta_session=eyJpdiI6InNvZk1ITkVwMFJyL0ZUek1YbFVyaWc9PSIsInZhbHVlIjoiR0hmOVpIVU5xK2N0Z1dLaCtKUWlYRkNJSVdGT0FuSlpaeG5kQllVdUVtSS9uSmFTQXVxRUhqZFFkTjA1MWxqbEZRaUNzdG5xMVRWdmt4NUIyNW5JZWovYnRjNHIzTXF4TzdQZmY5akw0OXFSSlNkWHhlN1JVS0wxKzd0QzBqYmciLCJtYWMiOiJhM2VlNWU0NTA1M2U2NTIzMzk3ODRiMTUxYWMzMmFmMzZmZDkwMTg5YjYzZWE2NjRjNDFlNTBmNmNmZGExOTA0IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 18:43:16 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1667226676\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1711997695 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DbgoOTCuumHy04EHLVHFGwOg3Jxe3DqOD4DE9hb1</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"48 characters\">http://127.0.0.1:8000/manufacturing/order/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1711997695\", {\"maxDepth\":0})</script>\n"}}