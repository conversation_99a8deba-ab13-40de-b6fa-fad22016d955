<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('sale_orders', function (Blueprint $table) {
            $table->foreign(['created_by'])->references(['id'])->on('users')->onUpdate('no action')->onDelete('no action');
            $table->foreign(['currency_id'])->references(['id'])->on('currencies')->onUpdate('no action')->onDelete('no action');
            $table->foreign(['party_id'])->references(['id'])->on('parties')->onUpdate('no action')->onDelete('no action');
            $table->foreign(['state_id'])->references(['id'])->on('states')->onUpdate('no action')->onDelete('no action');
            $table->foreign(['updated_by'])->references(['id'])->on('users')->onUpdate('no action')->onDelete('no action');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('sale_orders', function (Blueprint $table) {
            $table->dropForeign('sale_orders_created_by_foreign');
            $table->dropForeign('sale_orders_currency_id_foreign');
            $table->dropForeign('sale_orders_party_id_foreign');
            $table->dropForeign('sale_orders_state_id_foreign');
            $table->dropForeign('sale_orders_updated_by_foreign');
        });
    }
};
