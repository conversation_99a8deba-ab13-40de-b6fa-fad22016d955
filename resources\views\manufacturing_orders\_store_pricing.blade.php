<div class="row g-3">
    <div class="col-md-4">
        <x-label for="hauteur_finale" name="{{ __('manufacturing.hauteur_finale') }}" />
        <x-input type="number" step="0.01" name="hauteur_finale" id="store_hauteur_finale" :required="true" value="" readonly/>
    </div>

    <div class="col-md-4">
        <x-label for="qte_to_billed" name="{{ __('manufacturing.qte_to_billed') }}" />
        <x-input type="number" step="0.01" name="qte_to_billed" id="store_qte_to_billed" :required="true" value=""/>
    </div>

    <div class="col-md-4">
        <x-label for="billed_unity" name="{{ __('manufacturing.billed_unity') }}" />
        <x-input type="text" name="billed_unity" :required="true" value=""/>
    </div>

    <div class="col-md-4">
        <x-label for="unit_price" name="{{ __('manufacturing.unit_price') }}" />
        <x-input type="number" step="0.01" name="unit_price" id="store_unit_price" :required="true" value=""/>
    </div>

    <div class="col-md-4">
        <x-label for="extras_fees" name="{{ __('manufacturing.extras_fees') }}" />
        <x-input type="number" step="0.01" name="extras_fees" id="store_extras_fees" value=""/>
    </div>

    <div class="col-md-4">
        <x-label for="fabric_requirement" name="{{ __('manufacturing.fabric_requirement') }}" />
        <x-input type="number" step="0.01" name="fabric_requirement" :required="true" value=""/>
    </div>

    <div class="col-md-4">
        <x-label for="qty" name="{{ __('manufacturing.qty') }}" />
        <x-input type="number" step="0.01" name="qty" :required="true" value=""/>
    </div>

    <div class="col-md-4">
        <x-label for="total_amount" name="{{ __('manufacturing.total_amount') }}" />
        <x-input type="number" step="0.01" name="total_amount" id="store_total_amount" :required="true" value="0" readonly/>
    </div>

    <div class="col-md-12">
        <x-label for="notes" name="{{ __('manufacturing.notes') }}" />
        <x-textarea name="notes" value=""/>
    </div>
</div> 