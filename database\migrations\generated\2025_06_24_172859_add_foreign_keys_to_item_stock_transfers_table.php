<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('item_stock_transfers', function (Blueprint $table) {
            $table->foreign(['from_item_transaction_id'])->references(['id'])->on('item_transactions')->onUpdate('no action')->onDelete('cascade');
            $table->foreign(['from_warehouse_id'])->references(['id'])->on('warehouses')->onUpdate('no action')->onDelete('cascade');
            $table->foreign(['item_id'])->references(['id'])->on('items')->onUpdate('no action')->onDelete('cascade');
            $table->foreign(['stock_transfer_id'])->references(['id'])->on('stock_transfers')->onUpdate('no action')->onDelete('cascade');
            $table->foreign(['to_item_transaction_id'])->references(['id'])->on('item_transactions')->onUpdate('no action')->onDelete('cascade');
            $table->foreign(['to_warehouse_id'])->references(['id'])->on('warehouses')->onUpdate('no action')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('item_stock_transfers', function (Blueprint $table) {
            $table->dropForeign('item_stock_transfers_from_item_transaction_id_foreign');
            $table->dropForeign('item_stock_transfers_from_warehouse_id_foreign');
            $table->dropForeign('item_stock_transfers_item_id_foreign');
            $table->dropForeign('item_stock_transfers_stock_transfer_id_foreign');
            $table->dropForeign('item_stock_transfers_to_item_transaction_id_foreign');
            $table->dropForeign('item_stock_transfers_to_warehouse_id_foreign');
        });
    }
};
