
<?php $__env->startSection('title', __('manufacturing.manufacturing_orders')); ?>

<?php $__env->startSection('css'); ?>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css">
<style>
    .order-card {
        transition: all 0.3s ease;
        border-left: 4px solid #ddd;
        margin-bottom: 1rem;
    }
    .order-card:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    .order-card.status-Draft { border-left-color: #6c757d; }
    .order-card.status-In-Progress { border-left-color: #0d6efd; }
    .order-card.status-Completed { border-left-color: #198754; }
    .order-card.status-Delivered { border-left-color: #0dcaf0; }
    
    .order-card .card-body {
        padding: 1.25rem;
    }

    .order-info {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .order-info-item {
        display: flex;
        flex-direction: column;
    }

    .order-info-label {
        font-size: 0.875rem;
        color: #6c757d;
        margin-bottom: 0.25rem;
    }

    .order-info-value {
        font-weight: 500;
    }
    
    .expand-btn {
        transition: transform 0.3s ease;
        cursor: pointer;
        padding: 0.375rem 0.75rem;
        border: none;
        background: none;
    }
    .expand-btn:hover {
        background-color: #f8f9fa;
        border-radius: 0.25rem;
    }
    .expand-btn.expanded i {
        transform: rotate(180deg);
    }
    
    .order-details {
        display: none;
        background-color: #f8f9fa;
        border-radius: 0.25rem;
        margin-top: 1rem;
        padding: 1rem;
    }

    .filters-section {
        background: #fff;
        padding: 1rem;
        border-radius: 0.25rem;
        margin-bottom: 1rem;
    }

    .badge {
        font-size: 0.875rem;
        padding: 0.5em 0.75em;
    }

    .action-buttons {
        display: flex;
        gap: 0.5rem;
    }

    .action-buttons .btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        padding: 0;
    }

    .action-buttons .btn i {
        font-size: 1.25rem;
    }

    .card-header-content {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .order-meta {
        display: flex;
        align-items: center;
        gap: 2rem;
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="page-wrapper">
    <div class="page-content">
        <?php if (isset($component)) { $__componentOriginal269900abaed345884ce342681cdc99f6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal269900abaed345884ce342681cdc99f6 = $attributes; } ?>
<?php $component = App\View\Components\Breadcrumb::resolve(['langArray' => [
            __('manufacturing.manufacturing'),
            __('manufacturing.manufacturing_orders'),
            __('manufacturing.list')
        ]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('breadcrumb'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\Breadcrumb::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal269900abaed345884ce342681cdc99f6)): ?>
<?php $attributes = $__attributesOriginal269900abaed345884ce342681cdc99f6; ?>
<?php unset($__attributesOriginal269900abaed345884ce342681cdc99f6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal269900abaed345884ce342681cdc99f6)): ?>
<?php $component = $__componentOriginal269900abaed345884ce342681cdc99f6; ?>
<?php unset($__componentOriginal269900abaed345884ce342681cdc99f6); ?>
<?php endif; ?>
        
        <div class="card">
            <div class="card-header px-4 py-3 d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><?php echo e(__('manufacturing.manufacturing_orders')); ?></h5>
                <a href="<?php echo e(route('manufacturing.order.create')); ?>" class="btn btn-primary">
                    <i class="bx bx-plus"></i> <?php echo e(__('manufacturing.create_order')); ?>

                </a>
            </div>
            <div class="card-body p-4">
                <!-- Filters Section -->
                <div class="filters-section mb-4">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <label class="form-label"><?php echo e(__('manufacturing.date_range')); ?></label>
                            <input type="text" class="form-control" id="dateRange">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label"><?php echo e(__('manufacturing.status')); ?></label>
                            <select class="form-select" id="statusFilter">
                                <option value=""><?php echo e(__('manufacturing.all_statuses')); ?></option>
                                <option value="Draft"><?php echo e(__('manufacturing.status_draft')); ?></option>
                                <option value="In Progress"><?php echo e(__('manufacturing.status_in_progress')); ?></option>
                                <option value="Completed"><?php echo e(__('manufacturing.status_completed')); ?></option>
                                <option value="Delivered"><?php echo e(__('manufacturing.status_delivered')); ?></option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label"><?php echo e(__('manufacturing.type')); ?></label>
                            <select class="form-select" id="typeFilter">
                                <option value=""><?php echo e(__('manufacturing.all_types')); ?></option>
                                <option value="Store"><?php echo e(__('manufacturing.curtain_types.store')); ?></option>
                                <option value="Rideaux"><?php echo e(__('manufacturing.curtain_types.rideaux')); ?></option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label"><?php echo e(__('manufacturing.search')); ?></label>
                            <input type="text" class="form-control" id="searchInput" placeholder="<?php echo e(__('manufacturing.search_placeholder')); ?>">
                        </div>
                    </div>
                </div>

                <!-- Orders List -->
                <div id="ordersList" class="row g-3">
                    <!-- Orders will be dynamically inserted here -->
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center mt-4">
                    <div id="pagination-info"></div>
                    <nav>
                        <ul class="pagination" id="pagination"></ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Order Details Modal -->
<div class="modal fade" id="orderDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><?php echo e(__('manufacturing.order_details')); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="orderDetailsContent">
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('js'); ?>
<!-- Moment.js from CDN (required by daterangepicker) -->
<script type="text/javascript" src="https://cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
<!-- Date Range Picker from CDN -->
<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
<!-- List.js from CDN -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/list.js/2.3.1/list.min.js"></script>
<!-- Define base URL and translations -->
<script>
    if (typeof baseUrl === 'undefined') {
        var baseUrl = '<?php echo e(url("/")); ?>';
    }

    // Global translations object
    window.appTranslations = {
        // Basic translations
        showing: '<?php echo e(__("manufacturing.showing")); ?>',
        to: '<?php echo e(__("manufacturing.to")); ?>',
        of: '<?php echo e(__("manufacturing.of")); ?>',
        entries: '<?php echo e(__("manufacturing.entries")); ?>',
        confirm_delete: '<?php echo e(__("manufacturing.confirm_delete")); ?>',
        delete_warning: '<?php echo e(__("manufacturing.delete_warning")); ?>',
        yes_delete: '<?php echo e(__("manufacturing.yes_delete")); ?>',
        cancel: '<?php echo e(__("manufacturing.cancel")); ?>',
        deleted: '<?php echo e(__("manufacturing.deleted")); ?>',
        delete_success: '<?php echo e(__("manufacturing.delete_success")); ?>',
        error: '<?php echo e(__("manufacturing.error")); ?>',
        delete_error: '<?php echo e(__("manufacturing.delete_error")); ?>',
        
        // Additional translations
        designation: '<?php echo e(__("manufacturing.designation")); ?>',
        tissus: '<?php echo e(__("manufacturing.tissus")); ?>',
        curtain_type: '<?php echo e(__("manufacturing.curtain_type")); ?>',
        delivery_date: '<?php echo e(__("manufacturing.delivery_date")); ?>',
        created_by: '<?php echo e(__("manufacturing.created_by")); ?>',
        total_amount: '<?php echo e(__("manufacturing.total_amount")); ?>',
        edit: '<?php echo e(__("manufacturing.edit")); ?>',
        view: '<?php echo e(__("manufacturing.view")); ?>',
        delete: '<?php echo e(__("manufacturing.delete")); ?>'
    };
</script>
<!-- Your custom JS -->
<script src="<?php echo e(versionedAsset('custom/js/manufacturing/manufacturing-orders-list.js')); ?>"></script>
<?php $__env->stopSection(); ?> 
<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\www\delta\resources\views/manufacturing_orders/index.blade.php ENDPATH**/ ?>