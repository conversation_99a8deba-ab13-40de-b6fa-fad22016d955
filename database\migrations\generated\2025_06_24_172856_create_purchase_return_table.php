<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('purchase_return', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->date('return_date');
            $table->string('prefix_code')->nullable();
            $table->string('count_id')->nullable();
            $table->string('return_code')->nullable();
            $table->string('reference_no')->nullable();
            $table->unsignedBigInteger('party_id')->index('purchase_return_party_id_foreign');
            $table->unsignedBigInteger('state_id')->nullable()->index('purchase_return_state_id_foreign');
            $table->text('note')->nullable();
            $table->decimal('round_off', 20, 4)->default(0);
            $table->decimal('grand_total', 20, 4)->default(0);
            $table->decimal('paid_amount', 20, 4)->default(0);
            $table->unsignedBigInteger('currency_id')->nullable()->index('purchase_return_currency_id_foreign');
            $table->decimal('exchange_rate', 20, 4)->default(0);
            $table->unsignedBigInteger('created_by')->nullable()->index('purchase_return_created_by_foreign');
            $table->unsignedBigInteger('updated_by')->nullable()->index('purchase_return_updated_by_foreign');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('purchase_return');
    }
};
