<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customers', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('prefix_code')->nullable();
            $table->string('count_id')->nullable();
            $table->string('customer_id')->nullable();
            $table->string('first_name')->nullable();
            $table->string('last_name')->nullable();
            $table->string('email')->nullable()->unique();
            $table->string('mobile')->nullable();
            $table->string('whatsapp')->nullable();
            $table->text('address')->nullable();
            $table->unsignedBigInteger('created_by')->nullable()->index('customers_created_by_foreign');
            $table->unsignedBigInteger('updated_by')->nullable()->index('customers_updated_by_foreign');
            $table->boolean('status')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customers');
    }
};
